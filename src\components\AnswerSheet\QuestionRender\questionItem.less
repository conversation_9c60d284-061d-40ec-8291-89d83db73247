.subjectiveWrap,
.questionWriting {
  position: relative;

  & + .subjectiveWrap {
    border-top: none !important;
  }

  &:hover,
  &:focus,
  &:focus-visible {
    :global {
      .moveBtn {
        display: block;
      }
    }
  }

  :global {
    .moveBtn {
      position: absolute;
      right: -1px;
      bottom: 0;
      cursor: n-resize;
      z-index: 100;
      display: none;

      .moveBorder {
        height: 0;
        margin: 0;
        border-bottom: 1px solid #000;
        box-shadow: inset 0 -3em 5em rgba(0, 0, 0, 3%);
      }

      .ant-image {
        position: absolute;
        right: 8px;
        bottom: 8px;
        width: 24px;
      }
    }

    .moveEnd {
      position: absolute;
      width: 100%;
      height: 0;
      border-bottom: 1px solid #a8d7fb;
      box-shadow: 0 0 3px #a8d7fb;
      opacity: 0;
      transition: opacity 0.5s;
    }
  }
}

.subjectiveWrap:hover {
  border: 1px solid #dadada;
}

.questionWriting {
  border: 1px solid #000;
  position: relative;
  padding: 8px 0 16px;

  :global {
    .rows {
      height: 37.5px;
      margin-top: 7px;
      text-align: center;

      .words {
        display: inline-block;
        width: 37.5px;
        height: 37.5px;
        border: 1px solid #000;
        border-left: 0;
        position: relative;

        b {
          font-size: 12px;
          position: absolute;
          bottom: -13px;
          width: 50px;
          left: 0;
          font-weight: 400;
          line-height: 12px;
          transform: scale(0.6) translate(-40%, -40%);
          text-align: left;
          margin-left: 50%;

          i {
            display: inline-block;
            width: 0;
            height: 0;
            border-width: 0 6px 10px;
            border-style: solid;
            border-color: transparent transparent #000;
          }
        }

        &:first-child {
          border-left: 1px solid #000;
        }
      }
    }

    .moveBtn .moveBorder {
      position: absolute;
      bottom: -3px;
      right: -2px;
      height: 0;
      margin: 0;
      border-bottom: 1px solid #000;
      box-shadow: inset 0 -3em 5em rgba(0, 0, 0, 3%);
    }
  }
}
