import { DictionarieState } from '@/models/dictionarie';
import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormDigit,
  ProFormSegmented,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Button, Divider, Input, Select, Space } from 'antd';
import React, { useState } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Dictionarie;
  onSave: (info: API.Dictionarie) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  dictionarie,
}) => {
  const [types, setTypes] = useState<string[]>(
    [...new Set(dictionarie.list.map((item) => item.type))].sort((a, b) =>
      a.localeCompare(b),
    ),
  );
  const [name, setName] = useState('');

  const onNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const addItem = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault();
    if (!name) return;
    setTypes([...types, name].sort((a, b) => a.localeCompare(b)));
    setName('');
  };

  return (
    <ModalForm<API.Dictionarie>
      title={info ? '编辑字典项' : '注册字典项'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={
        info ? { ...info, status: String(info.status) } : { status: '1' }
      }
    >
      <ProFormText
        name="code"
        label="编号"
        tooltip="请确保编号唯一，尽量不使用无意义字符"
        rules={[{ required: true, message: '请输入编号！' }]}
        colProps={{ span: 12 }}
        readonly={!!info}
      />
      <ProFormText
        name="type"
        label="类型"
        rules={[{ required: true, message: '请输入类型！' }]}
        colProps={{ span: 12 }}
      >
        <Select
          options={types.map((type) => ({ label: type, value: type }))}
          dropdownRender={(menu) => (
            <>
              {menu}
              <Divider style={{ margin: '8px 0' }} />
              <Space style={{ padding: '0 8px 4px' }}>
                <Input
                  value={name}
                  onChange={onNameChange}
                  onKeyDown={(e) => e.stopPropagation()}
                />
                <Button type="text" icon={<PlusOutlined />} onClick={addItem}>
                  添加类型
                </Button>
              </Space>
            </>
          )}
        />
      </ProFormText>
      <ProFormText
        name="name"
        label="名称"
        tooltip="用于方便识别，请使用容易理解的名称"
        rules={[{ required: true, message: '请输入名称！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormText name="alias" label="别名" colProps={{ span: 12 }} />
      {!!info && (
        // 创建时不显示排序，系统自动计算最新序号
        <ProFormDigit name="sortOrder" label="排序" colProps={{ span: 6 }} />
      )}
      <ProFormSegmented
        name="status"
        label="状态"
        valueEnum={{ 1: '启用', 0: '禁用' }}
        colProps={{ span: 6 }}
      />
      <ProFormTextArea
        name="description"
        label="描述"
        placeholder="请输入描述，不超过100个字符"
        fieldProps={{
          maxLength: 100,
          showCount: true,
        }}
        colProps={{ span: 24 }}
      />
    </ModalForm>
  );
};

export default connect(({ dictionarie, loading }) => ({
  dictionarie,
  loading,
}))(EditModal);
