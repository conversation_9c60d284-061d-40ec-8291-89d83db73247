import { getSchoolQuestion } from '@/services/school_question_bank';
import { Col, message, Modal, Row, Space } from 'antd';
import React from 'react';
import TopicContent from '../QuestionBank/TopicContent';
import styles from './index.less';

interface DiffModalProps {
  diffVisible: boolean;
  onCancel: () => void;
  /** 当前试题内容 */
  currentQuestion: any;
}

const DiffModal: React.FC<DiffModalProps> = ({
  diffVisible,
  onCancel,
  currentQuestion,
}) => {
  const [data, setData] = React.useState<{
    currentQuestion: any;
    schoolQuestion: any;
  }>();

  /** 查询题库版本试题 */
  const queryQuestion = async () => {
    const { errCode, data, msg } = await getSchoolQuestion(currentQuestion._id);
    if (errCode) {
      return message.warning(`查询试题失败，${msg}`);
    }
    setData({ currentQuestion, schoolQuestion: data.list?.[0] ?? {} });
  };
  React.useEffect(() => {
    if (diffVisible && currentQuestion && currentQuestion._id) {
      queryQuestion();
    }
  }, [diffVisible]);

  return (
    <>
      <Modal
        title="试题内容对比"
        width={1000}
        open={diffVisible}
        onCancel={onCancel}
        footer={null}
        styles={{
          body: {
            maxHeight: '600px',
            overflowY: 'auto',
            padding: 0,
          },
        }}
      >
        <Row className={styles.modalContent}>
          <Col span={12}>
            <h4>学校题库版本（已入库）</h4>
            <Space>
              <span>题型：{data?.schoolQuestion?.type?.name}</span>
              <span>难度：{data?.schoolQuestion?.difficulty?.name}</span>
            </Space>
            <div className={styles.topicCardContent}>
              <TopicContent info={data?.schoolQuestion} />
            </div>
          </Col>
          <Col span={12} className={styles.alteration}>
            <h4>变更的版本（待审核）</h4>
            <Space>
              <span>题型：{data?.currentQuestion?.type?.name}</span>
              <span>难度：{data?.currentQuestion?.difficulty?.name}</span>
            </Space>
            <div className={styles.topicCardContent}>
              <TopicContent info={data?.currentQuestion} />
            </div>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
export default DiffModal;
