import ProFormImg from '@/components/ProFormItem/ProFormImg';
import { DictionarieState } from '@/models/dictionarie';
import { EnterpriseState } from '@/models/enterprise';
import { index } from '@/services/subjects';
import { convertSystemToGrade } from '@/utils/calc';
import {
  ModalForm,
  ProForm,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Form, message } from 'antd';
import React from 'react';

const EditModal: React.FC<{
  open: boolean;
  info?: API.User;
  onSave: (info: API.User) => Promise<void>;
  onClose: () => void;
  enterprise: EnterpriseState;
  dictionarie: DictionarieState;
}> = ({ open, info, onSave, onClose, enterprise, dictionarie }) => {
  const [form] = Form.useForm<any>();
  const sectionOptions = dictionarie?.list
    .filter((item) => item?.type === 'grade_section')
    .map((item) => ({
      label: item.name ?? '',
      value: item.code ?? '',
    }));
  const gradeOptions = dictionarie?.list?.filter(
    (item) => item?.type === 'grade',
  );
  return (
    <ModalForm<API.User>
      title={info ? '编辑用户' : '注册用户'}
      autoFocusFirstInput
      form={form}
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      onOpenChange={(visible) => {
        if (visible) {
          if (info) {
            form.setFieldsValue(info);
          } else {
            form.setFieldsValue({
              isActive: true,
            });
          }
        } else {
          form.resetFields();
        }
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={async (info) => {
        if (info.id) {
          // 更新时，可以清除某些字段的值
          const { enterprise_id } = info;
          if (!enterprise_id) {
            info.enterprise_id = '';
          }
        }
        onSave(info);
      }}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProForm.Group colProps={{ span: 12 }}>
        <ProFormText
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名！' }]}
        />
        <ProFormText.Password
          name="password"
          label="密码"
          rules={[{ required: true, message: '请输入密码！' }]}
          initialValue={info ? '********' : ''}
          transform={(value: string) => {
            if (value === '********') {
              return undefined;
            }
            return value;
          }}
        />
        <ProFormText name="email" label="邮箱" />
      </ProForm.Group>
      <ProForm.Group colProps={{ span: 12 }}>
        <ProFormImg name="avatar" label="头像" />
      </ProForm.Group>
      <ProFormText
        name="nickname"
        label="昵称"
        rules={[{ required: true, message: '请输入昵称！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormRadio.Group
        name="isActive"
        label="状态"
        radioType="button"
        fieldProps={{
          options: [
            {
              label: '禁用',
              value: false,
            },
            {
              label: '启用',
              value: true,
            },
          ],
        }}
        colProps={{ span: 12 }}
      />
      <ProFormSelect
        name="enterprise_id"
        label="所属单位"
        showSearch
        allowClear
        options={enterprise.list.map((item) => ({
          label: item.name,
          value: item.id,
        }))}
        onChange={() => {
          form.setFieldsValue({
            grade_section_code: undefined,
            grade_section_name: undefined,
            grade: undefined,
            subject_id: undefined,
            subject_name: undefined,
          });
        }}
        colProps={{ span: 12 }}
      />
      <ProFormDependency name={['enterprise_id']}>
        {({ enterprise_id }) => {
          const curEnter = enterprise.currentList.find(
            (item) => item.id === enterprise_id,
          );
          const { school_system, type } = curEnter || {};
          if (type === 'SCHOOL') {
            const sectionData = convertSystemToGrade(
              'section',
              school_system,
              sectionOptions,
            );
            if (sectionData?.length === 1) {
              form.setFieldValue('grade_section_code', sectionData[0].value);
              form.setFieldValue('grade_section_name', sectionData[0].label);
            }
            return (
              <>
                <ProFormSelect
                  name="grade_section_code"
                  label="所属学段"
                  allowClear
                  options={sectionData}
                  onChange={(_, option: any) => {
                    form.setFieldValue('grade_section_name', option?.label);
                  }}
                  colProps={{ span: 12 }}
                />
                <ProFormText
                  name="grade_section_name"
                  label="grade_section_name"
                  hidden
                />
              </>
            );
          }
          return undefined;
        }}
      </ProFormDependency>
      <ProFormDependency name={['enterprise_id', 'grade_section_code']}>
        {({ enterprise_id, grade_section_code }) => {
          const curEnter = enterprise.currentList.find(
            (item) => item.id === enterprise_id,
          );
          const { type } = curEnter || {};
          if (type === 'SCHOOL') {
            const gradeData = convertSystemToGrade(
              'grade',
              grade_section_code,
              gradeOptions,
            );
            return (
              <>
                <ProFormSelect
                  name="grade"
                  label="所属年级"
                  mode="multiple"
                  options={gradeData}
                  colProps={{ span: 12 }}
                />
                <ProFormSelect
                  name="subject_id"
                  label="所属科目"
                  params={{ grade_section: grade_section_code }}
                  request={async () => {
                    const { errCode, data, msg } = await index({
                      grade_section: grade_section_code,
                    });
                    if (errCode) {
                      message.warning(`获取科目失败，请稍后重试 ${msg}`);
                      return [];
                    }
                    return (
                      data?.list?.map((item: any) => ({
                        label: item.grade_section_name
                          ? item.grade_section_name + item.subject
                          : item.subject,
                        value: item.id,
                      })) || []
                    );
                  }}
                  onChange={(_, option: any) => {
                    form.setFieldValue('subject_name', option?.label);
                  }}
                  colProps={{ span: 12 }}
                />
                <ProFormText name="subject_name" label="subject_name" hidden />
              </>
            );
          }
          return undefined;
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default connect(({ enterprise, dictionarie }) => ({
  enterprise,
  dictionarie,
}))(EditModal);
