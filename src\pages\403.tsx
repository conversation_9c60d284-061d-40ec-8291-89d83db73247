import { SSOState } from '@/models/sso_3th';
import { getSSOToken, gotoLoginPage, setSSOToken } from '@/utils/auth';
import {
  AnyAction,
  connect,
  Dispatch,
  history,
  terminal,
  useAccess,
  useLocation,
  useSearchParams,
} from '@umijs/max';
import { Button, Result } from 'antd';
import { useEffect, useState } from 'react';

const UnAccessible: React.FC<{
  sso_3th: SSOState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ sso_3th, dispatch, loading }) => {
  const { isLogin } = useAccess();
  const { pathname } = useLocation();
  const [query] = useSearchParams();
  const token = query.get('token');

  const [needLogin, setNeedLogin] = useState<boolean>(!!token);

  useEffect(() => {
    console.log('进入403页面，token: %s, isLogin: %s', token, isLogin);
    // 只有在SSO模式下才处理第三方token参数
    if (AUTH_MODE === 'sso' && token && token !== 'null') {
      sessionStorage.setItem('homework-ischild', 'true');
      const oldSSOToken = getSSOToken();
      terminal.log('oldSSOToken: ', oldSSOToken);
      if (
        (!isLogin || oldSSOToken !== token) &&
        !loading.effects['sso_3th/login']
      ) {
        setNeedLogin(true);
        if (!sso_3th.done && !loading.effects['sso_3th/login']) {
          terminal.log('用户未登录，使用ssoToken进行自动登录');
          setSSOToken(token);
          dispatch({
            type: 'sso_3th/login',
            payload: { token },
          });
        }
      } else {
        setNeedLogin(false);
        terminal.log('用户已登录，ssoToken无效，执行参数清除，走默认流程');
        query.delete('token');
        history.replace(
          `${pathname}${query.toString() ? `?${query.toString()}` : ''}`,
        );
        // const url = new URL(location.href);
        // url.searchParams.delete('token');
        // location.href = url.toString();
      }
    }
  }, [token, sso_3th.done]);

  useEffect(() => {
    if (needLogin && sso_3th.done) {
      terminal.log('ssoToken自动登录成功');
      const url = new URL(location.href);
      url.searchParams.delete('token');
      location.href = url.toString();
    }
  }, [needLogin, sso_3th.done]);

  if ((AUTH_MODE === 'sso' && token && token !== 'null') || needLogin) {
    return (
      <div
        style={{
          height: '80vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        加载中……
      </div>
    );
  }

  if (!isLogin) {
    gotoLoginPage();
    return <></>;
  }

  return (
    <Result
      status="403"
      title="403"
      subTitle="抱歉，您无权访问此页面。"
      extra={
        <Button
          type="primary"
          onClick={() => {
            history.replace('/');
          }}
        >
          返回首页
        </Button>
      }
    />
  );
};

export default connect(({ sso_3th, loading }) => ({ sso_3th, loading }))(
  UnAccessible,
);
