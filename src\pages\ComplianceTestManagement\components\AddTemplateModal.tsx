import CatalogTree from '@/components/CatalogTree';
import { CatalogTreeNode } from '@/components/CatalogTree/typings';
import ConditionalRender from '@/components/ConditionalRender';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { useLocation, useModel, useParams } from '@umijs/max';
import { Button, Col, Form, Row, Space, Tag, message } from 'antd';
import React, { useEffect, useState } from 'react';

const AddTemplateModal: React.FC<ModalFormProps> = (ModalFormProps) => {
  const { id } = useParams();
  const { initialState: currentUser } = useModel('@@initialState');
  const { state: info, pathname } = useLocation() as {
    state: API.TemplateWork;
    pathname: string;
  };
  const isUnit = pathname.includes('unit');
  const [form] = Form.useForm<any>();
  const [selectedCatalogs, setSelectedCatalogs] = useState<any>([]);

  /** 选择目录 */
  const onSelect = (value: Partial<API.TextbookCatalog>) => {
    if (value?.id && isUnit) {
      setSelectedCatalogs([
        {
          id: value.id,
          title: value.title,
        },
      ]);
    }
  };
  useEffect(() => {
    form.setFieldsValue({
      catalogIds: selectedCatalogs?.map((item: any) => item.id),
      catalogs: selectedCatalogs?.map((item: any) => item.title),
    });
  }, [selectedCatalogs]);
  return (
    <ModalForm
      {...ModalFormProps}
      form={form}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            defaultDoms[0],
            <Button
              key="ok"
              type="primary"
              onClick={() => {
                if (selectedCatalogs?.length) {
                  props.submit();
                } else {
                  message.warning('请选择考核' + isUnit ? '单元' : '范围');
                }
              }}
            >
              确定
            </Button>,
          ];
        },
      }}
      onOpenChange={(visible) => {
        if (visible) {
          setSelectedCatalogs([]);
          form?.setFieldsValue({
            template_id: id,
            detection_type: isUnit ? '单元' : '期中',
            textbookChecklist_id: info?.textbookChecklist_id,
            status: '草稿',
            creator_name: currentUser?.username,
            creator_id: currentUser?.id,
          });
        }
      }}
    >
      <Row>
        <Col
          span={11}
          style={{
            height: '60vh',
            overflowY: 'auto',
          }}
        >
          <CatalogTree
            readonly
            checkable={isUnit ? undefined : true}
            maxLevel={1}
            currentTextbookChecklist={{
              id: info?.textbookChecklist_id,
            }}
            onSelect={onSelect}
            onMultipleSelect={async (nodes: CatalogTreeNode[]) => {
              setSelectedCatalogs(nodes);
            }}
          />
        </Col>
        <Col span={12} offset={1}>
          <>
            <ProFormText name="id" hidden />
            <ProFormText name="template_id" hidden />
            <ProFormText name="textbookChecklist_id" hidden />
            <ProFormText name="catalogIds" hidden />
            <ProFormText name="catalogs" hidden />
            <ProFormText name="status" hidden />
            <ProFormText name="creator_id" hidden />
            <ProFormText name="creator_name" hidden />
            <ProForm.Item
              label={
                <>
                  <span
                    style={{
                      marginRight: '4px',
                      color: '#ff4d4f',
                      fontSize: '14px',
                      fontFamily: 'SimSun, sans-serif',
                      lineHeight: 1,
                    }}
                  >
                    *
                  </span>
                  考核{isUnit ? '单元' : '范围'}
                </>
              }
            >
              <div
                style={{
                  border: '1px solid #d9d9d9',
                  padding: '10px',
                  borderRadius: '4px',
                  minHeight: '60px',
                }}
              >
                <ConditionalRender
                  hasAccess={selectedCatalogs?.length}
                  accessComponent={
                    <Space wrap>
                      {selectedCatalogs?.map((item: any) => {
                        return <Tag key={item.id}>{item.title}</Tag>;
                      })}
                    </Space>
                  }
                  noAccessComponent={
                    <div
                      style={{
                        color: 'GrayText',
                      }}
                    >
                      请从左侧章节目录中选择考核{isUnit ? '单元' : '范围'}
                    </div>
                  }
                />
              </div>
            </ProForm.Item>
            <ProFormRadio.Group
              name="detection_type"
              label="范本类型"
              radioType="button"
              options={isUnit ? ['单元'] : ['期中', '期末']}
              rules={[{ required: true, message: '请选择范本类型' }]}
            />
            <ProFormText
              name="name"
              label="范本名称"
              labelCol={{ flex: '6em' }}
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '请输入范本名称' }]}
            />
          </>
        </Col>
      </Row>
    </ModalForm>
  );
};
export default AddTemplateModal;
