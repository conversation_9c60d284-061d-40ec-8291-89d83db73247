import { importSubjects } from '@/services/subjects';
import { getToken } from '@/utils/auth';
import { ImportOutlined } from '@ant-design/icons';
import { ModalForm, ProFormUploadDragger } from '@ant-design/pro-components';
import { Alert, Button, message, Table, Typography } from 'antd';
import { UploadChangeParam, UploadFile } from 'antd/es/upload';
import { flattenDeep } from 'lodash-es';
import { useCallback, useState } from 'react';

const ImpotModal: React.FC<{ onFinish: () => Promise<void> | void }> = ({
  onFinish,
}) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [errList, setErrList] =
    useState<Record<string, Record<string, string>>>();

  const handleFileChange = useCallback(
    (info: UploadChangeParam<UploadFile<any>>) => {
      if (info.file.status === 'done') {
        const res = info.file.response as API.ResType<{
          id: string;
          filename: string;
          url: string;
        }>;
        const { errCode, msg } = res;
        if (errCode) {
          console.error(msg);
          messageApi.error('上传失败');
          return;
        }
      }
    },
    [],
  );

  return (
    <>
      {contextHolder}
      <ModalForm<{ schoolId?: string; file?: string }>
        title="批量导入"
        trigger={<Button icon={<ImportOutlined />}>批量导入</Button>}
        width={600}
        modalProps={{
          destroyOnClose: true,
          afterClose: () => {
            setErrList(undefined);
          },
        }}
        onFinish={async (formData) => {
          const { file } = formData;
          if (!file) {
            messageApi.error('请先上传文件');
            return false;
          }
          const params: { schoolId?: string; filePath: string } = {
            filePath: file,
          };
          if (formData.schoolId) {
            params.schoolId = formData.schoolId;
          }
          const { errCode, msg } = await importSubjects(params);
          if (errCode) {
            try {
              const err = JSON.parse(msg);
              if (err.msg) {
                messageApi.error(err.msg);
              } else {
                messageApi.error('导入失败，请重新上传');
                setErrList(err);
              }
            } catch (error) {
              messageApi.error(msg || '导入失败，请重新上传');
            }
            return false;
          }
          messageApi.success('导入成功');
          onFinish();
          return true;
        }}
      >
        <ProFormUploadDragger
          label="上传文件"
          max={1}
          name="file"
          accept=".xlsx,.xls"
          description="只能上传一个 xlsx 或 xls 文件"
          action={`/homework_design_api/file/upload_single/temp`}
          onChange={handleFileChange}
          fieldProps={{
            headers: {
              Authorization: `Bearer ${getToken()}`,
            },
            beforeUpload: (file) => {
              if (
                file.type !==
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
              ) {
                messageApi.error('请上传正确的文件格式');
                return false;
              }
              const isLt10M = file.size / 1024 / 1024 < 10;
              if (!isLt10M) {
                messageApi.error('文件大小不应超过10M');
                return false;
              }
              setErrList(undefined);
              return true;
            },
          }}
          transform={(fileList: UploadFile[]) => {
            const list = fileList.filter(
              (f) => f.status === 'done' && f.response?.errCode === 0,
            );
            return { file: list?.[0]?.response?.data };
          }}
        />
        {errList && (
          <Alert
            description={
              <Table
                size="small"
                columns={[
                  {
                    title: '行号',
                    dataIndex: 'row',
                    key: 'row',
                    width: 120,
                    align: 'center',
                  },
                  { title: '字段', dataIndex: 'field', key: 'field' },
                  { title: '错误信息', dataIndex: 'msg', key: 'msg' },
                ]}
                dataSource={flattenDeep(
                  Object.keys(errList).map((key) => {
                    const row = Number(key);
                    const current = errList[key];
                    const list = Object.keys(current).map((field) => ({
                      row,
                      field,
                      msg: current[field],
                    }));
                    return list;
                  }),
                )}
                scroll={{ y: 200 }}
                pagination={false}
              />
            }
            type="error"
            closable
            style={{ marginBottom: 16 }}
          />
        )}
        <Typography.Paragraph>
          上传的文件必须符合格式要求，可
          <a href={`${BASE_URL}/doc_template/学科导入模板.xlsx`}>下载模板</a>
          进行填写后导入
        </Typography.Paragraph>
      </ModalForm>
    </>
  );
};

export default ImpotModal;
