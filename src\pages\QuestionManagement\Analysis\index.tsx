/*
 * @Description: 答案解析管理
 * @Date: 2025-04-21 17:11:29
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-21 17:13:18
 */
import CatalogTreeWithHead from '@/components/CatalogTreeWithHead';
import CommonCard from '@/components/CommonCard';
import ConditionalRender from '@/components/ConditionalRender';
import LookQuestionModal from '@/components/QuestionBank/LookQuestionModal';
import ReviseClassHoursModal from '@/components/QuestionBank/ReviseClassHoursModal';
import TopicList from '@/components/QuestionBank/TopicList';
import QuestionForm, { QuestionFormRef } from '@/components/QuestionForm';
import SearchTopicBox, {
  SearchTopicBoxResult,
} from '@/components/SearchTopicBox';
import { QuestionContext } from '@/context/QuestionContext';
import {
  getCommonAllQuestions,
  updateAssociatetCommonQuestion,
  updatetCommonQuestion,
} from '@/services/common_question';
import { batchSystemQuestion } from '@/services/question_bank';
import { DeleteOutlined, FormOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Col, message, Popconfirm, Row, Spin } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { PartialWithRequired } from 'typings';
import { MONGO_MODEL_KEY } from '../Entry';
import styles from './index.less';

interface SubjectInfoType {
  section?: API.Dictionarie;
  grade?: API.Dictionarie;
  subject?: API.Subject;
  textbook?: API.Textbook;
  catalog?: API.TextbookCatalog;
  volume?: API.TextbookChecklist;
}

const QuestionBankAnalysis = () => {
  const { initialState } = useModel('@@initialState');
  const childRef = useRef<QuestionFormRef>(null);
  const [allSelect, setAllSelect] = useState<string[]>([]);
  const [data, setData] = useState<any>([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [searchTopicInfo, setSearchTopicInfo] = useState<SearchTopicBoxResult>(
    {},
  );
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [readonly, setReadonly] = useState(false);
  const [showAnalysis, setShowAnalysis] = useState<Record<string, boolean>>({});
  const [lookQuestioOpen, setLookQuestioOpen] = useState<{
    open: boolean;
    data?: API.SystemQuestion;
  }>({
    open: false,
    data: undefined,
  });
  /** 教材信息 */
  const [subjectInfo, setSubjectInfo] = useState<SubjectInfoType>({});
  const [selectedUnitHours, setSelectedUnitHours] = useState<boolean>(false);
  /** 编辑试题的科目id */
  const [editSubjectId, setEditSubjectId] = useState<number>();
  const [currentCatalog, setCurrentCatalog] = useState<{
    id?: number;
    name?: string;
  }>();
  const prevSubjectInfo = useRef<SubjectInfoType>({});
  const rolesList = initialState?.roles;

  const isTeacher = rolesList?.some((item) => item.name.includes('教师'));
  const isSchoolAdmin = rolesList?.some((item) =>
    ['小学管理员', '初中管理员', '高中管理员', '学校管理员'].includes(
      item.name,
    ),
  );
  const isAdmin = rolesList?.some((item) => item.name.includes('管理员'));

  /** 根据身份返回不同的type类型 */
  const getType = () => {
    if (isTeacher) {
      return MONGO_MODEL_KEY.PERSONAL;
    }
    if (isSchoolAdmin) {
      return MONGO_MODEL_KEY.SCHOOL;
    }
    if (isAdmin) {
      return MONGO_MODEL_KEY.SYSTEM;
    }
    return MONGO_MODEL_KEY.SYSTEM;
  };

  useEffect(() => {
    prevSubjectInfo.current = subjectInfo;
  }, [subjectInfo]);

  /** 搜索 */
  const handleSearch = async (data: SearchTopicBoxResult) => {
    const isDataChanged =
      JSON.stringify(data) !== JSON.stringify(searchTopicInfo);
    if (isDataChanged) {
      setAllSelect([]);
      setShowAnalysis({});
      setSearchTopicInfo(data);
      setPagination({ current: 1, pageSize: 10 });
    }
  };

  /** 获取试题列表 */
  const getAllQuestion = useCallback(async () => {
    setLoading(true);
    setShowAnalysis({});
    const {
      difficulty,
      questionTypes: type,
      knowledgePoint,
      searchTopic,
      searchTag,
    } = searchTopicInfo ?? {};
    const { grade, textbook, section, subject, volume } =
      prevSubjectInfo.current ?? {};

    if (!volume?.id) return;
    const { errCode, data, msg } = await getCommonAllQuestions(getType(), {
      offset:
        Number((pagination?.current || 1) - 1) *
        Number(pagination?.pageSize || 10),
      limit: Number(pagination?.pageSize || 10),
      difficulty: difficulty?.code,
      type: type?.code,
      grade: grade?.code as any,
      subject: subject?.id,
      textbookVersion: textbook?.id,
      volume: volume?.id,
      gradeSection: section?.code as any,
      catalog: currentCatalog?.id,
      author: isTeacher ? initialState?.id : undefined,
      userId: initialState?.id,
      enterpriseCode: initialState?.enterprise?.code,
      points: knowledgePoint?.join(',') || undefined,
      stem: searchTopic || undefined,
      userTag: searchTag || undefined,
    });
    if (errCode) {
      setLoading(false);
      message.warning(`获取试题列表失败，请稍后重试 ${msg}`);
    } else {
      setData(data ?? []);
      setLoading(false);
    }
  }, [
    pagination?.current,
    pagination?.pageSize,
    searchTopicInfo,
    subjectInfo?.catalog,
  ]);

  /** 编辑试题 */
  const editQuestion = async (
    data: PartialWithRequired<API.SystemQuestion, '_id'>,
  ) => {
    childRef?.current?.loading(true, '修改试题中，请耐心等候...');
    const { errCode, msg } = await updatetCommonQuestion(
      data._id,
      getType(),
      data,
    );
    if (errCode) {
      childRef?.current?.loading(false);
      return message.warning(`修改试题失败，请稍后重试 ${msg}`);
    }
    message.success('修改试题成功');
    childRef?.current?.loading(false);
    childRef?.current?.close();
  };

  /** 编辑父子题 */
  const editAssociateQuestion = async (data: Partial<API.SystemQuestion>) => {
    const { _id: id } = data;
    if (!id) {
      return message.warning('组题类试题ID为空');
    }
    childRef?.current?.loading?.(true, '修改试题中，请耐心等候...');
    const { errCode, msg } = await updateAssociatetCommonQuestion(
      id,
      getType(),
      data as any,
    );
    if (errCode) {
      return message.warning(`修改组题类试题失败，请稍后重试 ${msg}`);
    } else {
      message.success('修改组题类试题成功');
    }
    childRef?.current?.loading(false);
    childRef?.current?.close();
  };

  /** 提交试题 */
  const submitQuestion = async (data: Partial<API.SystemQuestion>) => {
    const { grade, textbook, section, subject, volume } = subjectInfo;
    /** 增加部分参数 */
    const newData: any = {
      ...data,
      grade: data?.grade || {
        name: grade?.name,
        code: grade?.code,
      },
      subject: data?.subject || {
        name: subject?.subject,
        id: subject?.id,
      },
      textbookVersion: data?.textbookVersion || {
        name: textbook?.textbook_version,
        id: textbook?.id,
      },
      volume: data?.volume || {
        name: volume?.volume,
        id: volume?.id,
      },
      gradeSection: data?.gradeSection || {
        name: section?.name,
        code: section?.code,
      },
      catalog: data?.catalog || {
        name: currentCatalog?.name,
        id: currentCatalog?.id,
      },
    };
    try {
      if (newData.isCompose) {
        // 父子题
        if (newData?._id) {
          return await editAssociateQuestion(newData);
        }
      } else {
        // 普通题
        if (newData?._id) {
          return await editQuestion({ ...newData, _id: newData._id });
        }
      }
    } catch (error) {
      childRef?.current?.loading?.(false);
      return message.warning(`提交试题失败，请稍后重试 ${error}`);
    }
  };

  /** 批量删除试题 */
  const batchDeleteQuestion = async () => {
    const { errCode, msg } = await batchSystemQuestion({
      ids: allSelect,
    });
    if (errCode) {
      return message.warning(`批量删除试题失败，请稍后重试 ${msg}`);
    }
    message.success('批量删除试题成功');
    setAllSelect([]);
    getAllQuestion();
  };

  /** 查看试题 */
  const lookQuestion = (data: API.SystemQuestion) => {
    setLookQuestioOpen({
      open: true,
      data,
    });
  };

  /** 编辑回填试题 */
  const onEditInfo = (data: API.SystemQuestion) => {
    setEditSubjectId(data?.subject?.id);
    childRef.current?.show?.();
    if (data?.isCompose) {
      data.questions = data?.children.map(
        (item: { type: { code: string } }) => {
          return {
            ...item,
            type_code: item?.type?.code,
          };
        },
      );
    }
    const backfillData = {
      ...data,
      type_code: data?.type?.code,
      answer:
        data?.baseType?.name === '多选题'
          ? JSON.parse(data?.answer || '[]')
          : data?.answer,
      difficulty: data?.difficulty?.code,
      tier: data?.tier?.id,
      cognitiveHierarchy: data?.cognitiveHierarchy?.code,
      coreQuality: data?.coreQuality?.code,
      investigationAbility: data?.investigationAbility?.code,
    };
    setReadonly(true);
    childRef?.current?.initData?.(backfillData as any);
  };

  useEffect(() => {
    if (pagination.current && pagination.pageSize && subjectInfo?.volume?.id) {
      getAllQuestion();
    }
  }, [
    pagination.current,
    pagination.pageSize,
    searchTopicInfo,
    subjectInfo,
    selectedUnitHours,
    currentCatalog,
  ]);

  useEffect(() => {
    if (subjectInfo && subjectInfo.catalog?.parent_id) {
      setSelectedUnitHours(true);
    } else {
      setSelectedUnitHours(false);
    }
  }, [subjectInfo?.catalog]);

  useEffect(() => {
    if (data?.list) {
      const initialShowAnalysis: Record<string, boolean> = {};
      const setAllAnalysis = (questions: any[]) => {
        questions?.forEach((question) => {
          initialShowAnalysis[question._id] = true;
          if (question.children?.length) {
            setAllAnalysis(question.children);
          }
        });
      };
      setAllAnalysis(data.list);
      setShowAnalysis(initialShowAnalysis);
    }
  }, [data?.list]);

  return (
    <QuestionContext.Provider
      value={{
        readonly,
        subjectId: subjectInfo?.subject?.id,
      }}
    >
      <Row gutter={16}>
        <Col span={6}>
          <CatalogTreeWithHead
            level="volume"
            onSelect={(info: any) => {
              setCurrentCatalog({
                id: info?.catalog?.id,
                name: info?.catalog?.title,
              });
              setSubjectInfo(info);
            }}
          />
        </Col>
        <Col span={18}>
          <div className={styles.questionBox}>
            <CommonCard
              title="答案解析管理"
              className={styles.addQuestionBtn}
            />
            <div className={styles.searchTopicBoxWrapper}>
              <SearchTopicBox
                subjectId={subjectInfo?.subject?.id}
                className={styles.searchTopicBox}
                onChange={handleSearch}
                showAll={INDEPENDENT_QUESTION_BANK}
                showDifficulty
                showTypes
                showTag
                showKeyword
                showSmart
              />
              <CommonCard title={`题目总数（${data?.total ?? 0} 道）`}>
                <ConditionalRender
                  hasAccess={INDEPENDENT_QUESTION_BANK}
                  accessComponent={
                    <Button color="default" variant="link">
                      批量标注
                    </Button>
                  }
                />
                <ConditionalRender
                  hasAccess={INDEPENDENT_QUESTION_BANK}
                  accessComponent={
                    <Button color="default" variant="link">
                      更新相似题数
                    </Button>
                  }
                />
                <ConditionalRender
                  hasAccess={!(allSelect.length === 0)}
                  accessComponent={
                    <>
                      <ConditionalRender
                        hasAccess={INDEPENDENT_QUESTION_BANK}
                        accessComponent={
                          <Button
                            type="link"
                            icon={<FormOutlined />}
                            size="small"
                            onClick={() => {
                              setOpen(true);
                            }}
                          >
                            批量修改课时
                          </Button>
                        }
                      />
                      <Popconfirm
                        title="您确定要删除选中的试题吗？"
                        description="删除后无法恢复，请谨慎操作！"
                        onConfirm={batchDeleteQuestion}
                      >
                        <Button
                          type="link"
                          icon={<DeleteOutlined />}
                          size="small"
                          danger
                        >
                          批量删除
                        </Button>
                      </Popconfirm>
                    </>
                  }
                />
              </CommonCard>
              <Spin tip="数据加载中..." spinning={loading}>
                <div className={styles.questionListWrapper}>
                  <TopicList
                    isTag={false}
                    data={data}
                    onSelect={(value: string, status: boolean) => {
                      if (status) {
                        setAllSelect([...allSelect, value]);
                      } else {
                        setAllSelect(
                          allSelect.filter((item) => item !== value),
                        );
                      }
                    }}
                    onLookInfo={lookQuestion}
                    onEditInfo={onEditInfo}
                    showAnalysis={showAnalysis}
                    onShowAnalysis={(id: string) => {
                      setShowAnalysis({
                        ...showAnalysis,
                        [id]: !showAnalysis[id],
                      });
                    }}
                    allSelect={allSelect}
                    pagination={{
                      total: data?.total,
                      current: pagination?.current,
                      pageSize: pagination?.pageSize,
                      onChange: (page: any, pageSize: any) => {
                        setPagination({
                          current: page,
                          pageSize: pageSize,
                        });
                      },
                    }}
                  />
                </div>
              </Spin>
            </div>
          </div>
        </Col>
      </Row>
      {/* 存放不在当前页面直接展示的组件 */}
      <>
        <ReviseClassHoursModal
          title="批量修改课时"
          onOk={async () => {
            return false;
          }}
          onCancel={() => {
            setOpen(false);
          }}
          open={open}
        />
        <LookQuestionModal
          open={lookQuestioOpen.open}
          info={lookQuestioOpen.data!}
          onCancel={() => {
            setLookQuestioOpen({
              open: false,
              data: undefined,
            });
          }}
        />
        <QuestionForm
          mode="answerAnalysis"
          subjectId={editSubjectId ?? subjectInfo?.subject?.id}
          handleQuestionSubmit={submitQuestion}
          ref={childRef}
          refresh={async () => {
            await getAllQuestion();
          }}
        />
      </>
    </QuestionContext.Provider>
  );
};

export default QuestionBankAnalysis;
