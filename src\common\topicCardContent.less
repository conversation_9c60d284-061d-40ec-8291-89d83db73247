.topicCardContent {
  padding: 6px 0;

  p {
    margin: 0;
  }

  img {
    max-width: 100%;
  }

  .topicAnswer {
    width: 100%;
    margin: 10px 0;
    background-color: #fff;
    border-radius: 4px;
    padding: 10px;
    // border: 1px solid #e0e0e0;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 10%), -1px -1px 3px rgba(0, 0, 0, 10%);

    .lable {
      width: 75px;
      color: var(--primary-color);
    }

    .answerAnalysis,
    .answerTitle {
      display: flex;
    }

    .answerTitle {
      margin-bottom: 8px;
    }

    .analysis {
      flex: 1;
    }
  }

  :global {
    .sub-question {
      padding: 8px 8px 8px 16px;
      background-color: #f1f1f1;
      border-radius: 8px;

      .question {
        margin: 10px;
      }

      .question-box {
        display: flex;
        align-items: center;

        .question-content {
          flex: 1;
        }
      }

      .question-options {
        margin-left: 10px;
      }
    }

    .sub-question:not(:last-child) {
      margin: 16px 0;
    }

    .question {
      display: flex;
      margin-bottom: 8px;
      position: relative;
    }

    .question-name {
      width: 100%;
      flex: 1;
      word-break: break-word;
      overflow-wrap: break-word;
      white-space: pre-wrap;

      p {
        margin-bottom: 0;
      }
    }

    .question-delete {
      width: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 12px;
      color: var(--delete-color);
    }

    .question-options {
      margin-bottom: 8px;

      .option-item {
        display: flex;
        align-items: center;
        margin: 12px 12px 12px 0;

        &:last-child {
          margin-bottom: 0;
        }

        p {
          margin-bottom: 0;
        }

        .option-label {
          .option-key {
            font-weight: 600;
            font-size: 14px;
            color: #333;
          }
        }
      }
    }
  }
}
