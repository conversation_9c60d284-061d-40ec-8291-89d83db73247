import * as area from './area';
import * as auth from './auth';
import * as city_homework_case from './city_homework_case';
import * as city_homework_concept from './city_homework_concept';
import * as city_homework_form from './city_homework_form';
import * as city_homework_principle from './city_homework_principle';
import * as city_homework_requirement from './city_homework_requirement';
import * as city_homework_suggestion from './city_homework_suggestion';
import * as city_homework_type from './city_homework_type';
import * as class_work from './class_work';
import * as class_work_detail from './class_work_detail';
import * as class_work_question from './class_work_question';
import * as class_work_struct from './class_work_struct';
import * as common_question from './common_question';
import * as compliance_detection_design from './compliance_detection_design';
import * as compliance_detection_design_catalog from './compliance_detection_design_catalog';
import * as compliance_detection_design_detail from './compliance_detection_design_detail';
import * as compliance_detection_design_question from './compliance_detection_design_question';
import * as compliance_detection_design_struct from './compliance_detection_design_struct';
import * as compliance_detection_template from './compliance_detection_template';
import * as compliance_detection_template_detail from './compliance_detection_template_detail';
import * as compliance_detection_template_question from './compliance_detection_template_question';
import * as compliance_detection_template_struct from './compliance_detection_template_struct';
import * as compliance_template from './compliance_template';
import * as composer_question_box from './composer_question_box';
import * as composer_test_paper from './composer_test_paper';
import * as course_plan_admin_target from './course_plan_admin_target';
import * as course_plan_course_carry from './course_plan_course_carry';
import * as course_plan_course_system from './course_plan_course_system';
import * as course_plan_subject from './course_plan_subject';
import * as course_standard_appendix from './course_standard_appendix';
import * as course_standard_carry from './course_standard_carry';
import * as course_standard_character from './course_standard_character';
import * as course_standard_content from './course_standard_content';
import * as course_standard_idea from './course_standard_idea';
import * as course_standard_objective from './course_standard_objective';
import * as course_standard_quality from './course_standard_quality';
import * as dictionaries from './dictionaries';
import * as enterprises from './enterprises';
import * as features from './features';
import * as homework_templates from './homework_templates';
import * as htmlToWord from './htmlToWord';
import * as knowledge_point from './knowledge_point';
import * as lesson_work_catalog from './lesson_work_catalog';
import * as lesson_work_design from './lesson_work_design';
import * as lesson_work_design_detail from './lesson_work_design_detail';
import * as lesson_work_design_question from './lesson_work_design_question';
import * as lesson_work_design_struct from './lesson_work_design_struct';
import * as permissions from './permissions';
import * as personal from './personal';
import * as personal_question_bank from './personal_question_bank';
import * as plan_course_setting from './plan_course_setting';
import * as plan_implementation_mechanism from './plan_implementation_mechanism';
import * as province_homework_concept from './province_homework_concept';
import * as province_homework_goal from './province_homework_goal';
import * as province_homework_implement from './province_homework_implement';
import * as province_homework_type from './province_homework_type';
import * as publishers from './publishers';
import * as question_bank from './question_bank';
import * as question_classification from './question_classification';
import * as question_extend_type from './question_extend_type';
import * as question_tier from './question_tier';
import * as roles from './roles';
import * as school_question_bank from './school_question_bank';
import * as semesters from './semesters';
import * as statistics from './statistics';
import * as subjects from './subjects';
import * as subject_dictionary from './subject_dictionary';
import * as system_question from './system_question';
import * as test_paper from './test_paper';
import * as textbooks from './textbooks';
import * as textbook_catalog from './textbook_catalog';
import * as textbook_checklist from './textbook_checklist';
import * as textbook_choose from './textbook_choose';
import * as upload from './upload';
import * as users from './users';

export {
  area,
  auth,
  city_homework_case,
  city_homework_concept,
  city_homework_form,
  city_homework_principle,
  city_homework_requirement,
  city_homework_suggestion,
  city_homework_type,
  class_work,
  class_work_detail,
  class_work_question,
  class_work_struct,
  common_question,
  compliance_detection_design,
  compliance_detection_design_catalog,
  compliance_detection_design_detail,
  compliance_detection_design_question,
  compliance_detection_design_struct,
  compliance_detection_template,
  compliance_detection_template_detail,
  compliance_detection_template_question,
  compliance_detection_template_struct,
  compliance_template,
  composer_question_box,
  composer_test_paper,
  course_plan_admin_target,
  course_plan_course_carry,
  course_plan_course_system,
  course_plan_subject,
  course_standard_appendix,
  course_standard_carry,
  course_standard_character,
  course_standard_content,
  course_standard_idea,
  course_standard_objective,
  course_standard_quality,
  dictionaries,
  enterprises,
  features,
  homework_templates,
  htmlToWord,
  knowledge_point,
  lesson_work_catalog,
  lesson_work_design,
  lesson_work_design_detail,
  lesson_work_design_question,
  lesson_work_design_struct,
  permissions,
  personal,
  personal_question_bank,
  plan_course_setting,
  plan_implementation_mechanism,
  province_homework_concept,
  province_homework_goal,
  province_homework_implement,
  province_homework_type,
  publishers,
  question_bank,
  question_classification,
  question_extend_type,
  question_tier,
  roles,
  school_question_bank,
  semesters,
  statistics,
  subjects,
  subject_dictionary,
  system_question,
  test_paper,
  textbooks,
  textbook_catalog,
  textbook_checklist,
  textbook_choose,
  upload,
  users,
};