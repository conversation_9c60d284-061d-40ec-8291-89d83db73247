import { index as indexTextbookChecklist } from '@/services/textbook_checklist';
import { Col, ColProps, message, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { RadioGroupStyled } from './styled';

const Volume: React.FC<{
  initalValue?: number;
  onChange?: (value?: API.TextbookChecklist) => void;
  /** 教材ID */
  textbookId?: number;
  /** 年级编号 */
  grade?: string;
  /** 标题列属性 */
  labelCol?: ColProps;
  /** 自定义行样式 */
  rowStyles?: React.CSSProperties;
}> = ({ initalValue, onChange, textbookId, grade, labelCol, rowStyles }) => {
  const [currentTextbookChecklistId, setCurrentTextbookChecklistId] = useState<
    number | undefined
  >(initalValue);
  const [textbookChecklists, setTextbookChecklists] = useState<
    API.TextbookChecklist[]
  >([]);

  useEffect(() => {
    if (textbookId && grade) {
      (async () => {
        const { errCode, data, msg } = await indexTextbookChecklist({
          textbook_id: textbookId,
          grade,
        });
        if (errCode) {
          message.error(msg || '教材册次获取失败');
          return;
        }
        setTextbookChecklists(data.list || []);
      })();
    }
  }, [textbookId, grade]);

  // 教材列表切换后，检查currentTextbookId是否在列表中
  useEffect(() => {
    if (currentTextbookChecklistId) {
      if (
        !textbookChecklists.find((d) => d.id === currentTextbookChecklistId)
      ) {
        setCurrentTextbookChecklistId(undefined);
      }
    } else {
      setCurrentTextbookChecklistId(textbookChecklists?.[0]?.id);
      onChange?.(textbookChecklists?.[0]);
    }
  }, [textbookChecklists, currentTextbookChecklistId]);

  return (
    <Row className={styles.row} style={rowStyles}>
      <Col className={styles.label} {...labelCol}>
        <label>册次</label>
      </Col>
      <Col>
        <RadioGroupStyled
          size="small"
          options={textbookChecklists.map((item) => ({
            label: item.volume,
            value: item.id,
            title: item.volume,
          }))}
          onChange={(e) => {
            setCurrentTextbookChecklistId(e.target.value);
            const item = textbookChecklists.find(
              (d) => d.id === e.target.value,
            );
            onChange?.(item);
          }}
          value={currentTextbookChecklistId}
          optionType="button"
          buttonStyle="solid"
        />
      </Col>
    </Row>
  );
};

export default Volume;
