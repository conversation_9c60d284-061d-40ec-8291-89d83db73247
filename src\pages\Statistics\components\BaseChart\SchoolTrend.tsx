import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import React from 'react';
import styles from '../../index.less';
import { getPositiveNumber } from '../Chart/HeaderChart';

interface SchoolTrendProps {
  hidden?: boolean;
  data?: { type?: 'up' | 'down'; value?: string; title?: string }[];
}

const SchoolTrend: React.FC<SchoolTrendProps> = ({ data, hidden }) => {
  return (
    <>
      {!hidden ? (
        <div className={styles.schoolTrend}>
          {data?.map((item, index) => (
            <div key={index}>
              <span
                className={styles.schoolTrendValue}
                style={{
                  color: item?.type
                    ? item?.type === 'up'
                      ? '#3f8600'
                      : '#cf1322'
                    : 'rgba(0, 0, 0, 0.45)',
                }}
              >
                {item?.type ? (
                  item?.type === 'up' ? (
                    <ArrowUpOutlined />
                  ) : (
                    <ArrowDownOutlined />
                  )
                ) : null}
                &nbsp;
                <span className={styles.schoolTrendValueNum}>
                  {getPositiveNumber(item?.value)}
                  <span className={styles.desc}>{item?.title}</span>
                </span>
              </span>
            </div>
          ))}
        </div>
      ) : undefined}
    </>
  );
};
export default SchoolTrend;
