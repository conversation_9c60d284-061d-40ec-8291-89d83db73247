import TinyMCEEditor from '@/components/TinyMCEEditor';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  FormListActionType,
  ProForm,
  ProFormList,
  ProFormRadio,
} from '@ant-design/pro-components';
import { Button, Checkbox, message, Radio } from 'antd';
import { useEffect, useRef, useState } from 'react';
import ConditionalRender from '../ConditionalRender';
const fildsData: any = [{ A: '' }, { B: '' }, { C: '' }, { D: '' }];
/**
 * 默认题型组件，用于渲染题目内容
 * @param param0
 * @returns
 */
export const CommonItem = ({
  isCompose,
  index,
  form,
  isReadonlyMode,
}: {
  isCompose?: boolean;
  index?: number;
  form: any;
  isReadonlyMode?: boolean;
}) => {
  return (
    <>
      <ProForm.Item
        label="题干"
        name="name"
        style={{ width: '100%' }}
        labelCol={{
          flex: '7em',
        }}
        rules={[
          {
            required: true,
            message: '请输入题干',
            validator: (_, value, callback) => {
              if (!value) {
                callback('请输入题干');
              } else {
                callback();
              }
            },
          },
        ]}
      >
        <TinyMCEEditor
          isForm={true}
          readonly={isReadonlyMode}
          editorChange={(val, text) => {
            if (isCompose) {
              form.setFieldsValue({
                questions: form
                  .getFieldValue('questions')
                  .map((question: any, i: number) =>
                    i === index ? { ...question, name: val } : question,
                  ),
              });
            } else {
              form.setFieldValue('stem', text);
              form.setFieldValue('name', val);
            }
          }}
        />
      </ProForm.Item>
      <ProForm.Item
        label="答案"
        labelCol={{
          flex: '7em',
        }}
        name="answer"
        style={{ width: '100%' }}
      >
        <TinyMCEEditor
          isForm={true}
          editorChange={(val) => {
            if (isCompose) {
              form.setFieldsValue({
                questions: form
                  .getFieldValue('questions')
                  .map((question: any, i: number) =>
                    i === index ? { ...question, answer: val } : question,
                  ),
              });
            } else {
              form.setFieldValue('answer', val);
            }
          }}
        />
      </ProForm.Item>
      <ProForm.Item
        labelCol={{
          flex: '7em',
        }}
        label="解析"
        name="analysis"
        style={{ width: '100%' }}
      >
        <TinyMCEEditor
          isForm={true}
          editorChange={(val) => {
            if (isCompose) {
              form.setFieldsValue({
                questions: form
                  .getFieldValue('questions')
                  .map((question: any, i: number) =>
                    i === index ? { ...question, analysis: val } : question,
                  ),
              });
            } else {
              form.setFieldValue('analysis', val);
            }
          }}
        />
      </ProForm.Item>
    </>
  );
};
/**
 * 选择题
 * @param param0
 * @returns
 */
export const SingleChoiceItem = ({
  type,
  isCompose,
  index,
  form,
  initValues,
  readonly,
}: {
  type?: string;
  isCompose?: boolean;
  index?: number;
  initValues?: any;
  form: any;
  readonly?: boolean;
}) => {
  const actionRef = useRef<FormListActionType<any>>();
  const [optionData, setOptionData] = useState<string[]>([]);
  useEffect(() => {
    // 父子题处理
    if (isCompose) {
      const questions =
        form.getFieldValue('questions') || initValues?.questions || [];
      const optionsData = questions?.[index || 0]?.options || fildsData;
      const arr = optionsData?.map((_opt: any, opIndex: number) =>
        String.fromCharCode(64 + opIndex + 1),
      );
      setOptionData(arr);
      if (questions?.length) {
        form.setFieldsValue({
          questions: form
            .getFieldValue('questions')
            .map((question: any, i: number) =>
              i === index
                ? {
                    ...question,
                    options: optionsData,
                  }
                : question,
            ),
        });
      }
    } else {
      // 单题处理
      const optionsData =
        form.getFieldValue('options') || initValues?.options || fildsData;
      const arr = optionsData?.map((_opt: any, opIndex: number) =>
        String.fromCharCode(64 + opIndex + 1),
      );
      setOptionData(arr);
      form.setFieldsValue({
        options: optionsData,
      });
    }
  }, [initValues, isCompose]);

  return (
    <>
      <ProForm.Item
        label="题干"
        name="name"
        labelCol={{
          flex: '7em',
        }}
        style={{ width: '100%' }}
        rules={[
          {
            required: !readonly,
            message: '请输入题干',
            validator: (_, value, callback) => {
              if (!value) {
                callback('请输入题干');
              } else {
                callback();
              }
            },
          },
        ]}
      >
        <TinyMCEEditor
          readonly={readonly}
          isForm={true}
          editorChange={(val, text) => {
            if (isCompose) {
              form.setFieldsValue({
                questions: form
                  .getFieldValue('questions')
                  .map((question: any, i: number) =>
                    i === index ? { ...question, name: val } : question,
                  ),
              });
            } else {
              form.setFieldValue('stem', text);
              form.setFieldValue('name', val);
            }
          }}
        />
      </ProForm.Item>
      <ProFormList
        label="选项"
        name="options"
        alwaysShowItemLabel
        creatorButtonProps={false}
        copyIconProps={false}
        deleteIconProps={false}
        actionRef={actionRef}
        min={3}
        key={isCompose + '_' + (index || '') + '_' + (initValues?.type || '')}
        style={{
          marginBlockEnd: 0,
        }}
      >
        {(f: any, opIndex: number) => {
          return (
            <ProForm.Item
              key={f.key}
              label={String.fromCharCode(64 + opIndex + 1)}
              name={String.fromCharCode(64 + opIndex + 1)}
              rules={[
                {
                  required: true,
                  message:
                    '请输入选项' +
                    String.fromCharCode(64 + opIndex + 1) +
                    '的内容',
                  validator: (_, value, callback) => {
                    if (value && value !== '<p><br></p>') {
                      callback();
                    } else {
                      callback(
                        '请输入选项' +
                          String.fromCharCode(64 + opIndex + 1) +
                          '的内容',
                      );
                    }
                  },
                },
              ]}
            >
              <TinyMCEEditor
                isForm={true}
                readonly={readonly}
                editorChange={(val) => {
                  if (isCompose) {
                    const currentQuestions =
                      form.getFieldValue('questions') || [];
                    form.setFieldsValue({
                      questions: currentQuestions.map(
                        (question: any, idx: number) => {
                          if (idx === index) {
                            return {
                              ...question,
                              options: question.options.map(
                                (option: any, i: number) => {
                                  if (i === opIndex) {
                                    return {
                                      [String.fromCharCode(64 + opIndex + 1)]:
                                        val,
                                    };
                                  }
                                  return option;
                                },
                              ),
                            };
                          }
                          return question;
                        },
                      ),
                    });
                  } else {
                    form.setFieldsValue({
                      options: form
                        .getFieldValue('options')
                        .map((option: any, i: number) =>
                          i === opIndex
                            ? { [String.fromCharCode(64 + opIndex + 1)]: val }
                            : option,
                        ),
                    });
                  }
                }}
              />
            </ProForm.Item>
          );
        }}
      </ProFormList>
      <ConditionalRender
        hasAccess={!readonly}
        accessComponent={
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              width: 250,
              margin: '-24px 0 0 auto',
            }}
          >
            <Button
              type="link"
              onClick={() => {
                actionRef.current?.add();
                const list = actionRef.current?.getList() || [];
                const arr = [...optionData];
                if (isCompose && actionRef.current) {
                  const questions = form.getFieldValue('questions');
                  const optionsLen = questions?.[index || 0]?.options?.length;
                  arr.push(String.fromCharCode(64 + optionsLen));
                  // 清除父子题的答案
                  form.setFieldsValue({
                    questions: form
                      .getFieldValue('questions')
                      .map((q: any, i: number) =>
                        i === index ? { ...q, answer: null } : q,
                      ),
                  });
                } else {
                  arr.push(String.fromCharCode(64 + list?.length));
                  // 清除单题的答案
                  form.setFieldValue('answer', null);
                }
                setOptionData(arr);
              }}
              block
              icon={<PlusOutlined />}
            >
              添加选项
            </Button>
            <Button
              type="link"
              onClick={() => {
                let list = actionRef.current?.getList() || [];
                const minOptions = 2; // 最小选项数量

                if (isCompose) {
                  const questions = form.getFieldValue('questions');
                  list = questions?.[index || 0]?.options || [];
                }

                if (list.length <= minOptions) {
                  return message.warning(`至少保留${minOptions}个选项！`);
                }

                // 执行删除操作
                actionRef.current?.remove(list.length - 1);
                setOptionData((prev) => prev.slice(0, -1));

                // 清除答案
                if (isCompose) {
                  form.setFieldsValue({
                    questions: form
                      .getFieldValue('questions')
                      .map((q: any, i: number) =>
                        i === index ? { ...q, answer: null } : q,
                      ),
                  });
                } else {
                  form.setFieldValue('answer', null);
                }
              }}
              block
              icon={<MinusCircleOutlined />}
            >
              删除选项
            </Button>
          </div>
        }
      />

      <ProForm.Item
        label="题目答案"
        labelCol={{
          flex: '7em',
        }}
        name="answer"
        style={{ width: '100%' }}
        rules={[
          {
            required: true,
            message: '请选择正确选项',
          },
        ]}
      >
        {type === '多选题' ? (
          <Checkbox.Group options={optionData} />
        ) : (
          <Radio.Group options={optionData} />
        )}
      </ProForm.Item>
      <ProForm.Item
        label="解析"
        labelCol={{
          flex: '7em',
        }}
        name="analysis"
        style={{ width: '100%' }}
      >
        <TinyMCEEditor
          isForm={true}
          editorChange={(val) => {
            if (isCompose) {
              form.setFieldsValue({
                questions: form
                  .getFieldValue('questions')
                  .map((question: any, i: number) =>
                    i === index ? { ...question, analysis: val } : question,
                  ),
              });
            } else {
              form.setFieldValue('analysis', val);
            }
          }}
        />
      </ProForm.Item>
    </>
  );
};
/**
 * 判断题
 * @param param0
 * @returns
 */
export const JudgeItem = ({
  isCompose,
  index,
  form,
  readonly,
}: {
  isCompose?: boolean;
  index?: number;
  form: any;
  readonly: boolean;
}) => {
  return (
    <>
      <ProForm.Item
        label="题干"
        name="name"
        labelCol={{
          flex: '7em',
        }}
        style={{ width: '100%' }}
        rules={[
          {
            required: true,
            message: '请输入题干',
            validator: (_, value, callback) => {
              if (!value) {
                callback('请输入题干');
              } else {
                callback();
              }
            },
          },
        ]}
      >
        <TinyMCEEditor
          isForm={true}
          readonly={readonly}
          editorChange={(val, text) => {
            if (isCompose) {
              form.setFieldsValue({
                questions: form
                  .getFieldValue('questions')
                  .map((question: any, i: number) =>
                    i === index ? { ...question, name: val } : question,
                  ),
              });
            } else {
              form.setFieldValue('stem', text);
              form.setFieldValue('name', val);
            }
          }}
        />
      </ProForm.Item>
      <ProFormRadio.Group
        label="答案"
        name="answer"
        labelCol={{
          flex: '7em',
        }}
        style={{ width: '100%' }}
        rules={[
          {
            required: true,
            message: '请输入题目答案',
          },
        ]}
        options={[
          {
            label: '正确',
            value: '正确',
          },
          {
            label: '错误',
            value: '错误',
          },
        ]}
      />
      <ProForm.Item
        labelCol={{
          flex: '7em',
        }}
        label="解析"
        name="analysis"
        style={{ width: '100%' }}
      >
        <TinyMCEEditor
          isForm={true}
          editorChange={(val) => {
            if (isCompose) {
              form.setFieldsValue({
                questions: form
                  .getFieldValue('questions')
                  .map((question: any, i: number) =>
                    i === index ? { ...question, analysis: val } : question,
                  ),
              });
            } else {
              form.setFieldValue('analysis', val);
            }
          }}
        />
      </ProForm.Item>
    </>
  );
};
