/*
 * @Description: 定制collapse，通过styed覆盖了antd的样式，增加了内容区域可浮动的特性
 * @Date: 2025-02-25 11:18:46
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-21 19:40:27
 */
import { styled } from '@umijs/max';
import { Collapse, CollapseProps, theme } from 'antd';
import React, { useCallback } from 'react';

interface CustomCollapaseProps extends CollapseProps {
  /** 内容区域是否飘浮 */
  fixed?: boolean;
  /** 默认z-index为10，如果还被遮挡，可自行设置 */
  zIndex?: number;
}

const CustomCollapse: React.FC<CustomCollapaseProps> = (props) => {
  const { fixed, zIndex, ...others } = props;
  if (!!fixed) {
    others.defaultActiveKey = [];
    if (others.items?.[0]) {
      others.items[0].forceRender = true;
    }
  }
  const { token } = theme.useToken();
  const Dom = useCallback(
    styled(Collapse)`
      &.ant-collapse {
        border-radius: 0;
        border: none;
        background-color: initial;
        .ant-collapse-item {
          position: relative;
          .ant-collapse-header {
            border-radius: 8px;
            border: solid 1px ${token.colorPrimary};
            background-color: ${token.colorPrimary};
            color: #fff;
          }
          .ant-collapse-content {
            position: ${fixed ? 'absolute' : 'initial'};
            z-index: ${zIndex || 10};
            width: 100%;
            border: solid 1px ${token.colorPrimary};
          }

          &.ant-collapse-item-active {
            .ant-collapse-header {
              border-radius: 8px 8px 0 0;
            }
          }
        }
      }
    `,
    [],
  );
  return <Dom {...others} />;
};

export default CustomCollapse;
