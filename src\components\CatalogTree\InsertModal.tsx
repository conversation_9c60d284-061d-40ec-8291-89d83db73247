import { ModalForm, ProFormText } from '@ant-design/pro-components';
import React from 'react';
import { CatalogTreeNode } from './typings';

type InsertModalProps = {
  open: boolean;
  catalog?: CatalogTreeNode;
  action?: 'insert' | 'append';
  onSave: (info: API.TextbookCatalog) => Promise<void>;
  onClose: () => void;
};

const InsertModal: React.FC<InsertModalProps> = ({
  open,
  catalog,
  action,
  onSave,
  onClose,
}) => {
  if (!catalog || !action) {
    return null;
  }
  return (
    <ModalForm<API.TextbookCatalog>
      title={`向【${catalog.title?.trim()}】${
        action === 'insert' ? '前' : '后'
      }插入目录`}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      width={450}
      onFinish={onSave}
    >
      <ProFormText
        name="title"
        label="目录名称"
        rules={[{ required: true, message: '请输入目录名称！' }]}
      />
    </ModalForm>
  );
};

export default InsertModal;
