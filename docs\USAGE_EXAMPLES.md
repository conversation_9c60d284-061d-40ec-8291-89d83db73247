# 认证模式使用示例

## 快速开始

### 1. 切换到本地认证模式

```bash
# 1. 修改配置文件 config/config.ts
# 将 AUTH_MODE: 'sso' 改为 AUTH_MODE: 'local'

# 2. 重启开发服务器
npm run dev

# 3. 访问系统，未登录用户会自动跳转到 /login 页面
```

### 2. 切换到SSO认证模式

```bash
# 1. 修改配置文件 config/config.ts  
# 将 AUTH_MODE: 'local' 改为 AUTH_MODE: 'sso'

# 2. 重启开发服务器
npm run dev

# 3. 访问系统，未登录用户会自动跳转到SSO认证页面
```

## 后端接口实现示例

### 本地认证模式后端接口

#### 1. 登录接口
```javascript
// POST /auth/login
app.post('/auth/login', async (req, res) => {
  const { username, password } = req.body;
  
  // 验证用户名和密码
  const user = await validateUser(username, password);
  if (!user) {
    return res.json({
      errCode: 1,
      msg: '用户名或密码错误',
      data: null
    });
  }
  
  // 生成JWT token
  const token = jwt.sign(
    { userId: user.id, username: user.username },
    'your-secret-key',
    { expiresIn: '24h' }
  );
  
  res.json({
    errCode: 0,
    msg: 'success',
    data: {
      token,
      expiresIn: 86400 // 24小时
    }
  });
});
```

#### 2. 获取用户信息接口
```javascript
// GET /auth/userinfo
app.get('/auth/userinfo', authenticateToken, async (req, res) => {
  const user = await getUserById(req.user.userId);
  
  res.json({
    errCode: 0,
    msg: 'success',
    data: {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      avatar: user.avatar,
      email: user.email,
      enterprise: user.enterprise,
      roles: user.roles,
      features: user.features
    }
  });
});

// JWT验证中间件
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({
      errCode: 401,
      msg: '未提供认证token',
      data: null
    });
  }
  
  jwt.verify(token, 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({
        errCode: 403,
        msg: 'token无效或已过期',
        data: null
      });
    }
    req.user = user;
    next();
  });
}
```

## 前端使用示例

### 1. 检查当前认证模式
```typescript
// 在组件中检查当前认证模式
import { useEffect } from 'react';

const MyComponent = () => {
  useEffect(() => {
    console.log('当前认证模式:', AUTH_MODE);
    
    if (AUTH_MODE === 'local') {
      console.log('使用本地认证');
    } else {
      console.log('使用SSO认证');
    }
  }, []);
  
  return <div>My Component</div>;
};
```

### 2. 手动跳转到登录页
```typescript
import { gotoLoginPage } from '@/utils/auth';

const LogoutButton = () => {
  const handleLogout = () => {
    // 清除本地token
    localStorage.clear();
    // 跳转到对应的登录页
    gotoLoginPage();
  };
  
  return <Button onClick={handleLogout}>退出登录</Button>;
};
```

### 3. 自定义登录逻辑
```typescript
import { localLogin } from '@/services/auth';
import { saveJWT } from '@/utils/auth';

const CustomLogin = () => {
  const handleLogin = async (username: string, password: string) => {
    try {
      const { errCode, msg, data } = await localLogin({ username, password });
      
      if (errCode) {
        message.error(msg);
        return;
      }
      
      // 保存token
      saveJWT(data.token, `${new Date().getTime() + data.expiresIn * 1000}`);
      
      // 跳转到首页
      window.location.href = BASE_URL + '/';
    } catch (error) {
      message.error('登录失败');
    }
  };
  
  return (
    <Form onFinish={handleLogin}>
      {/* 表单内容 */}
    </Form>
  );
};
```

## 测试场景

### 1. 本地认证模式测试
```bash
# 1. 切换到本地认证模式
# 修改 config/config.ts: AUTH_MODE: 'local'

# 2. 启动项目
npm run dev

# 3. 测试场景
# - 访问 http://localhost:8000/homework-design/ 应该跳转到登录页
# - 访问 http://localhost:8000/homework-design/login 应该显示登录表单
# - 输入正确的用户名密码应该能够登录成功
# - 登录后访问其他页面应该正常显示
# - 退出登录应该跳转回登录页面
```

### 2. SSO认证模式测试
```bash
# 1. 切换到SSO认证模式
# 修改 config/config.ts: AUTH_MODE: 'sso'

# 2. 启动项目
npm run dev

# 3. 测试场景
# - 访问 http://localhost:8000/homework-design/ 应该跳转到SSO认证页
# - 完成SSO认证后应该能够正常登录
# - 退出登录应该跳转到SSO认证页面
```

### 3. 第三方Token认证测试
```bash
# 无论哪种认证模式，第三方token认证都应该有效

# 测试URL示例:
# http://localhost:8000/homework-design/?token=your_third_party_token

# 预期行为:
# - 系统检测到token参数
# - 自动调用第三方token认证接口
# - 认证成功后自动登录并跳转
```

## 故障排除

### 1. 切换认证模式后无法登录
- 检查配置文件中的 AUTH_MODE 是否正确
- 重启开发服务器
- 清除浏览器localStorage
- 检查后端接口是否正常

### 2. 本地登录页面样式异常
- 检查 antd 样式是否正确加载
- 检查登录页面组件是否正确导入

### 3. 第三方token认证失效
- 检查token参数是否正确传递
- 检查后端第三方token认证接口
- 查看浏览器控制台错误信息

### 4. 权限控制异常
- 检查用户信息接口返回的features字段
- 确认权限配置是否正确
- 检查access.ts文件中的权限逻辑
