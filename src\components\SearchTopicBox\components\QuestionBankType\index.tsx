import { QuestionTypesState } from '@/models/questionTypes';
import { connect } from '@umijs/max';
import classNames from 'classnames';
import React, { useEffect } from 'react';
import styles from './index.less';

interface QuestionBankTypeProps {
  questionTypes?: QuestionTypesState;
  defaultValue?: string;
  onChenge?: (value: Omit<OptionsType, 'uniqueKey'> | null) => void;
  subjectId?: number;
}

type OptionsType = {
  name: string;
  code: string;
  sourceName: string;
  sourceCode: string;
  uniqueKey: string;
};

const QuestionBankType: React.FC<QuestionBankTypeProps> = ({
  questionTypes,
  defaultValue,
  onChenge,
  subjectId,
}) => {
  const [options, setOptions] = React.useState<OptionsType[]>([]);
  const [selectedCode, setSelectedCode] = React.useState<string>(
    defaultValue || 'all',
  );
  useEffect(() => {
    if (
      !questionTypes ||
      !Array.isArray(questionTypes.list) ||
      questionTypes.list.length === 0
    )
      return;
    if (subjectId) {
      const options = questionTypes.list
        .filter((item) => {
          return item?.subjectId?.includes(subjectId);
        })
        .map((item) => {
          return {
            name: item.name,
            code: item.code,
            sourceName: item.name,
            sourceCode: item.code,
            uniqueKey: `${item.code}_${item.name}`,
          };
        });
      setOptions([
        {
          name: '全部',
          code: 'all',
          sourceName: 'all',
          sourceCode: 'all',
          uniqueKey: 'all',
        },
        ...options,
      ]);
    }
  }, [questionTypes, subjectId]);

  useEffect(() => {
    if (onChenge) {
      const selectInfo = options.find(
        (item: { uniqueKey: string }) => item.uniqueKey === selectedCode,
      );
      if (!selectInfo || selectInfo.code === 'all') {
        return onChenge(null);
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { uniqueKey, ...rest } = selectInfo;
      onChenge(rest);
    }
  }, [selectedCode]);

  return (
    <>
      <div className={styles.wapper}>
        <label>题型：</label>
        <div className={styles.options}>
          {options.map((item) => {
            return (
              <div
                key={item.uniqueKey}
                className={classNames(styles.optionsItem, {
                  [styles.selected]: selectedCode === item.uniqueKey,
                })}
                title={item.name}
                onClick={() => setSelectedCode(item.uniqueKey)}
              >
                {item.name}
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};
export default connect(({ questionTypes }) => ({
  questionTypes,
}))(QuestionBankType);
