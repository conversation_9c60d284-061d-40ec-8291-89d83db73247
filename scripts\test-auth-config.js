#!/usr/bin/env node

/**
 * 认证配置测试脚本
 * 用于验证认证模式配置是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查认证配置...\n');

// 检查配置文件
const configPath = path.join(__dirname, '../config/config.ts');
const typingsPath = path.join(__dirname, '../typings.d.ts');
const authUtilsPath = path.join(__dirname, '../src/utils/auth.ts');
const authServicesPath = path.join(__dirname, '../src/services/auth.ts');
const loginPagePath = path.join(__dirname, '../src/pages/Login/index.tsx');
const routePath = path.join(__dirname, '../config/route.ts');

let hasErrors = false;

function checkFile(filePath, checks) {
  const fileName = path.basename(filePath);
  console.log(`📁 检查文件: ${fileName}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    hasErrors = true;
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  checks.forEach(({ name, pattern, required = true }) => {
    const found = pattern.test(content);
    if (required && !found) {
      console.log(`❌ ${name}: 未找到`);
      hasErrors = true;
    } else if (found) {
      console.log(`✅ ${name}: 已配置`);
    } else {
      console.log(`⚠️  ${name}: 可选配置未找到`);
    }
  });
  
  console.log('');
}

// 检查配置文件
checkFile(configPath, [
  {
    name: 'AUTH_MODE配置',
    pattern: /AUTH_MODE:\s*['"`](sso|local)['"`]/,
    required: true
  }
]);

// 检查类型定义
checkFile(typingsPath, [
  {
    name: 'AUTH_MODE类型声明',
    pattern: /declare\s+const\s+AUTH_MODE:\s*['"`]sso['"`]\s*\|\s*['"`]local['"`]/,
    required: true
  }
]);

// 检查认证工具函数
checkFile(authUtilsPath, [
  {
    name: 'gotoLocalLoginPage函数',
    pattern: /export\s+const\s+gotoLocalLoginPage/,
    required: true
  },
  {
    name: 'gotoLoginPage函数',
    pattern: /export\s+const\s+gotoLoginPage/,
    required: true
  },
  {
    name: 'AUTH_MODE条件判断',
    pattern: /AUTH_MODE\s*===\s*['"`]local['"`]/,
    required: true
  }
]);

// 检查认证服务
checkFile(authServicesPath, [
  {
    name: 'localLogin函数',
    pattern: /export\s+async\s+function\s+localLogin/,
    required: true
  },
  {
    name: 'localCurrentUser函数',
    pattern: /export\s+async\s+function\s+localCurrentUser/,
    required: true
  }
]);

// 检查登录页面
checkFile(loginPagePath, [
  {
    name: '登录页面组件',
    pattern: /const\s+Login\s*=\s*\(\)/,
    required: true
  },
  {
    name: 'localLogin调用',
    pattern: /localLogin\s*\(/,
    required: true
  }
]);

// 检查路由配置
checkFile(routePath, [
  {
    name: '本地登录路由',
    pattern: /path:\s*['"`]\/login['"`]/,
    required: true
  },
  {
    name: '登录页面组件路径',
    pattern: /component:\s*['"`]\.\/Login['"`]/,
    required: true
  }
]);

// 检查当前配置的认证模式
const configContent = fs.readFileSync(configPath, 'utf8');
const authModeMatch = configContent.match(/AUTH_MODE:\s*['"`](sso|local)['"`]/);
if (authModeMatch) {
  const currentMode = authModeMatch[1];
  console.log(`🔧 当前认证模式: ${currentMode.toUpperCase()}`);
  
  if (currentMode === 'local') {
    console.log('📝 本地认证模式说明:');
    console.log('   - 用户将跳转到 /login 页面进行登录');
    console.log('   - 需要后端提供 POST /auth/login 和 GET /auth/userinfo 接口');
    console.log('   - 第三方token参数认证仍然有效');
  } else {
    console.log('📝 SSO认证模式说明:');
    console.log('   - 用户将跳转到SSO认证页面进行登录');
    console.log('   - 需要后端提供SSO相关接口');
    console.log('   - 第三方token参数认证仍然有效');
  }
} else {
  console.log('❌ 无法确定当前认证模式');
  hasErrors = true;
}

console.log('\n' + '='.repeat(50));

if (hasErrors) {
  console.log('❌ 配置检查失败，请修复上述问题');
  process.exit(1);
} else {
  console.log('✅ 认证配置检查通过！');
  console.log('\n💡 提示:');
  console.log('   - 可以通过修改 config/config.ts 中的 AUTH_MODE 来切换认证模式');
  console.log('   - 切换后需要重启开发服务器');
  console.log('   - 建议清除浏览器localStorage后重新测试');
}
