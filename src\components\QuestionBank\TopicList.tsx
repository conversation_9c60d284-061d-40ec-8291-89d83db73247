/**
 * @Description: 试题列表
 */
import ConditionalRender from '@/components/ConditionalRender';
import { Empty, Pagination, PaginationProps } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import styles from './common.less';
import TopicCard from './TopticCard';

export interface TopicListProps {
  data: any;
  /**是否存在已选试题，供试题添加时使用 */
  choosedQuestion?: API.SystemQuestion[];
  /** 是否全选 */
  allSelect?: string[];
  /** 选中回调 */
  onSelect?: (value: string, status: boolean) => void;
  /** 分页 */
  pagination: PaginationProps;
  /** 删除回调 */
  onDelete?: (id: string) => void;
  /** 编辑回调 */
  onEditInfo?: (value: API.SystemQuestion) => void;
  /** 查看回调 */
  onLookInfo?: (value: API.SystemQuestion) => void;
  /** 展示解析 */
  onShowAnalysis?: (value: string) => void;
  /** 选择事件 */
  choosedEvent?: (value: API.SystemQuestion, status: boolean) => void;
  /** 共享试题，仅个人题库需要 */
  onShare?: (data: API.SystemQuestion, status: boolean) => void;
  /** 撤销共享记录 */
  onRevokeShareLog?: (id: API.SystemQuestion) => void;
  /** 刷新回调 */
  onreload?: () => void;
  /** 解析展示 */
  showAnalysis?: {
    [key: string]: boolean;
  };
  /** 组题模式 默认：false*/
  isGroupModel?: boolean;
  /** 是否只读 */
  readonly?: boolean;
  /**是否可标记 */
  isTag?: boolean;
}

const TopicList: React.FC<TopicListProps> = ({
  data,
  choosedQuestion,
  onDelete,
  pagination,
  allSelect,
  onSelect,
  onShare,
  onEditInfo,
  onLookInfo,
  onShowAnalysis,
  onRevokeShareLog,
  choosedEvent,
  onreload,
  showAnalysis,
  readonly,
  isGroupModel = false,
  isTag = true,
}) => {
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    setHasAccess(!data || data.total === 0 ? false : true);
  }, [data]);

  return (
    <div className={styles.topicListBox}>
      <ConditionalRender
        hasAccess={hasAccess}
        accessComponent={
          <>
            <div className={classNames(styles.topicList)}>
              {(data?.list ?? []).map((item: any) => {
                let isChoosed;
                if (!!choosedQuestion) {
                  isChoosed = choosedQuestion.find(
                    (val) => val._id === item?._id,
                  );
                }
                return (
                  <TopicCard
                    readonly={readonly}
                    isTag={isTag}
                    onRevokeShareLog={onRevokeShareLog}
                    onShare={onShare}
                    onDelete={onDelete}
                    onEditInfo={onEditInfo}
                    onLookInfo={onLookInfo}
                    onShowAnalysis={onShowAnalysis}
                    showAnalysis={showAnalysis}
                    allSelect={allSelect}
                    key={item?._id}
                    info={item}
                    onSelect={onSelect}
                    isChoosed={!!isChoosed}
                    choosedEvent={choosedEvent}
                    isGroupModel={isGroupModel}
                    onreload={onreload}
                  />
                );
              })}
            </div>
            <Pagination
              align="center"
              showQuickJumper
              showSizeChanger
              showLessItems
              {...pagination}
            />
          </>
        }
        noAccessComponent={
          <Empty
            className="commonPositionCenter"
            description="暂无试题，请先录入"
            // image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        }
      />
    </div>
  );
};
export default TopicList;
