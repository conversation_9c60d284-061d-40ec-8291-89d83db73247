import CommonSelect from '@/components/CommonSelect';
import ConditionalRender from '@/components/ConditionalRender';
import { DictionarieState } from '@/models/dictionarie';
import { templateWorkIndex } from '@/services/class_work';
import {
  ModalForm,
  ModalFormProps,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { connect, useModel } from '@umijs/max';
import { Descriptions, Form, Space, Tag, message } from 'antd';
import React, { useEffect, useState } from 'react';

const AddTemplate: React.FC<
  ModalFormProps & {
    dictionarie: DictionarieState;
    curSection: any;
  }
> = ({ dictionarie, curSection, initialValues, ...props }) => {
  const { initialState: currentUser } = useModel('@@initialState');
  const [form] = Form.useForm<any>();
  const source = dictionarie?.currentList?.find((item) => item.name === '平台');
  const [curSelected, setSelected] = useState<any>();
  const [fbVersion, setFbVersion] = useState<any>();

  const items = (data?: any) => {
    const {
      volume,
      grade_name,
      textbook_version,
      subject_name,
      grade_section_name,
    } = data || {};
    return [
      {
        key: '1',
        label: '学段',
        children: grade_section_name,
      },
      {
        key: '2',
        label: '学科',
        children: subject_name,
      },
      {
        key: '3',
        label: '版材',
        children: textbook_version,
      },
      {
        key: '4',
        label: '年级',
        children: grade_name,
      },
      {
        key: '5',
        label: '册次',
        children: volume,
      },
    ];
  };
  const onSelectVolume = async (info: {
    section?: API.Dictionarie;
    subject?: API.Subject;
    textbook?: API.Textbook;
    grade?: API.Dictionarie;
    volume?: API.TextbookChecklist;
  }) => {
    const { section, subject, textbook, grade, volume } = info;
    if (section && subject && textbook && grade && volume) {
      const param = {
        grade_section_code: section.code,
        grade_section_name: section.name,
        subject_id: subject.id,
        subject_name: subject.subject,
        grade_code: grade.code,
        grade_name: grade.name,
        textbook_version: textbook.textbook_version,
        textbook_id: textbook.id,
        volume: volume.volume,
        textbookChecklist_id: volume.id,
      };
      setSelected(param);
      form.setFieldsValue(param);
    } else {
      setSelected(undefined);
    }
  };
  const getFb = async () => {
    const {
      grade_section_code,
      grade_code,
      subject_id,
      textbook_id,
      textbookChecklist_id,
    } = curSelected;
    const { errCode, data, msg } = await templateWorkIndex({
      grade_section_code,
      grade_code,
      subject_id,
      textbook_id,
      // textbook_version,
      // volume,
      textbookChecklist_id,
      status: '发布',
    });
    if (errCode) {
      message.warning('范本数据获取失败,请稍后重试' + msg);
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
    const arr = data?.list?.map((item: any) => {
      return {
        label: item.name,
        value: item.id,
        desc: item?.enterprise?.name,
      };
    });
    setFbVersion(arr);
  };
  useEffect(() => {
    if (curSelected && curSection?.activeKey === 'school') {
      getFb();
    } else {
      setFbVersion(undefined);
    }
  }, [curSelected, initialValues, curSection]);
  return (
    <>
      <ModalForm
        {...props}
        form={form}
        onOpenChange={(visiable) => {
          if (visiable) {
            if (initialValues) {
              form.setFieldsValue(initialValues);
            } else {
              form.setFieldsValue({
                source: source?.name || '平台',
                source_code: source?.code || 'PLATFORM',
                type: curSection?.activeKey === 'school' ? '自定义' : '内置', //内置||自定义
                status: '草稿',
                creator_name: currentUser?.username,
                creator_id: currentUser?.id,
                enterprise_id:
                  curSection?.activeKey === 'school'
                    ? currentUser?.enterprise_id
                    : undefined,
                grade_section_code: curSection?.code,
                grade_section_name: curSection?.name,
              });
            }
          } else {
            form.resetFields();
          }
        }}
      >
        <ProFormText name="id" hidden />
        <ProFormText name="creator_id" hidden />
        <ProFormText name="creator_name" hidden />
        <ProFormText name="enterprise_id" hidden />
        <ProFormText name="type" hidden />
        <ProFormText name="status" hidden />
        <ProFormText name="grade_section_code" hidden />
        <ProFormText name="grade_section_name" hidden />
        <ProFormText name="subject_id" hidden />
        <ProFormText name="subject_name" hidden />
        <ProFormText name="grade_code" hidden />
        <ProFormText name="grade_name" hidden />
        <ProFormText name="textbook_id" hidden />
        <ProFormText name="textbook_version" hidden />
        <ProFormText name="volume" hidden />
        <ProFormText name="textbookChecklist_id" hidden />
        <ConditionalRender
          hasAccess={!!initialValues}
          accessComponent={
            <Descriptions
              style={{
                marginBottom: 16,
              }}
              items={items(initialValues)}
            />
          }
          noAccessComponent={
            <CommonSelect
              level="volume"
              sectionCode={curSection?.code}
              onChange={onSelectVolume}
              labelCol={{ flex: '6em' }}
              sectionRowStyles={{ display: 'none' }}
            />
          }
        ></ConditionalRender>
        <ProFormText
          name="name"
          label="范本名称"
          labelCol={{ flex: '6em' }}
          colProps={{ span: 24 }}
          rules={[{ required: true, message: '请输入范本名称' }]}
        />
        <ConditionalRender
          hasAccess={curSection?.activeKey === 'school'}
          accessComponent={
            <ProFormSelect
              options={fbVersion}
              name="template_id"
              labelCol={{ flex: '6em' }}
              label="适用范本"
              fieldProps={{
                optionRender: (option) => (
                  <Space>
                    <span aria-label={option.data.label}>
                      {option.data.label}
                    </span>
                    {option.data.desc && <Tag>{option.data.desc} </Tag>}
                  </Space>
                ),
              }}
            />
          }
        />
        <ConditionalRender
          hasAccess={INDEPENDENT_QUESTION_BANK}
          accessComponent={
            <>
              <ProFormText name="source" hidden />
              <ProFormSelect
                name="source_code"
                label="来源"
                colProps={{ span: 12 }}
                rules={[{ required: true, message: '请选择来源' }]}
              />
            </>
          }
          noAccessComponent={
            <>
              <ProFormText
                name="source"
                hidden
                fieldProps={{
                  value: '平台',
                }}
              />
              <ProFormText
                name="source_code"
                hidden
                fieldProps={{
                  value: 'PLATFORM',
                }}
              />
            </>
          }
        />
      </ModalForm>
    </>
  );
};
export default connect(({ dictionarie }) => ({ dictionarie }))(AddTemplate);
