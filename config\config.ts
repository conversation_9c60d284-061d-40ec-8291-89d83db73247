import { defineConfig } from '@umijs/max';
import proxy from './proxy';
import routes from './route';

export default defineConfig({
  antd: {
    configProvider: {},
  },
  access: {},
  model: {},
  initialState: {},
  request: {},
  dva: {},
  styledComponents: {},
  layout: {
    title: '智慧作业设计系统',
  },
  define: {
    SSO: {
      AppID: 'xr2euke2',
      AppSecret: 'cn37ootpkk7',
      Host: 'http://sso.xdpb.top',
      state: 'homework2025',
    },
    COS_CONFIG: {
      Region: 'ap-nanjing',
      Bucket: 'ysp-uploader-**********',
      Prefix: 'homework/',
    },
    // storage前缀，与其他系统集成时防止重名
    STORAGE_PREFIX: 'homework_',
    // 不支持二级域名时，需要配置base区分不同应用，这里也要保持一致，去掉后面的/，这个变量用于sso跳转和其他无法使用useLocation时的路由加工
    BASE_URL: '/homework-design',
    /** 是否处于独立题库模式 */
    INDEPENDENT_QUESTION_BANK: false,
  },
  favicons: ['/homework-design/logo.png'],
  publicPath: '/homework-design/',
  base: '/homework-design/',
  routes,
  plugins: [require.resolve('../plugins/route-mapping-plugin.js')],
  npmClient: 'pnpm',
  hash: true,
  // proxy: proxy('http://www.xdpb.top/api'),
  proxy: proxy('http://evaluation.xingjiaoyun.cn/homework_design_api'),
  // proxy: proxy('http://127.0.0.1:3130'),
});
