.editorWrappper {
  position: relative;

  .editorToolBarWrappper {
    position: absolute;
    top: -41px;
    left: 4px;
    right: 4px;
  }

  &.formWrappper {
    :global {
      .mce-content-body {
        min-height: 64px;
        border: 1px solid #dadada;
      }
    }
  }

  :global {
    .mce-content-body {
      padding: 2px;
      word-break: break-word;
      margin: 0 !important;

      &:focus,
      &:focus-visible {
        border: 1px solid #dadada;
        outline: none;
      }

      .tinymce-horizontal-line {
        line-break: anywhere;
        font-size: 16px;
        border-bottom: 1px solid #000;
      }
    }
  }
}
