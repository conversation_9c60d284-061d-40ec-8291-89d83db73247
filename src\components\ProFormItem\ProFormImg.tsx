/*
 * @Description: 图片上传组件，在ProForm中使用
 * @Date: 2025-02-13 10:11:28
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-04 09:14:34
 */
import { deleteFile, upload } from '@/utils/cos';
import {
  ProFormUploadButton,
  ProFormUploadButtonProps,
} from '@ant-design/pro-components';
import { message, Modal, Upload, UploadProps } from 'antd';
import React from 'react';

type ProFormImgProps = ProFormUploadButtonProps & {
  maxSize?: {
    size: number;
    message: string;
  };
};

const ProFormImg: React.FC<ProFormImgProps> = ({ maxSize, ...uploadProps }) => {
  const customRequest: UploadProps['customRequest'] = async (options) => {
    if (!options.file) {
      message.warning('没有读取到文件，请重新上传！');
      return;
    }

    const file = options.file as File;
    /** 腾讯云上传 */
    try {
      const { result, data, err } = await upload({
        file,
        dir: 'avatar',
        name: `${Date.now()}_${file.name}`,
        onProgress: (progressData) => {
          options.onProgress?.({ percent: progressData.percent * 100 });
        },
      });
      if (!result) {
        message.error(`上传失败 ${err}`);
        options.onError?.(err);
      } else {
        message.success('上传成功');
        options.onSuccess?.('//' + data!.Location);
        // console.log('🚀 ~ handleUpload ~ data:', data);
      }
    } catch (error) {
      message.error(`源上传失败：${error}`);
    }
  };

  return (
    <ProFormUploadButton
      {...uploadProps}
      // name="avatar"
      // label="头像"
      max={1}
      listType="picture-card"
      fieldProps={{
        multiple: false,
        accept: 'image/*',
        onPreview: (file) => {
          const url = file.thumbUrl || file.url || file.response;
          if (url) {
            Modal.info({
              title: '预览',
              content: (
                <img
                  src={url}
                  alt="预览"
                  style={{ maxWidth: '100%', maxHeight: '100%' }}
                />
              ),
            });
          }
        },
        beforeUpload: (file) => {
          const isImage = file.type.startsWith('image/');
          if (!isImage) {
            message.error(`${file.name} 不是一个图片文件`);
            return Upload.LIST_IGNORE;
          }
          if (file.size > (maxSize ? maxSize.size : 1 * 1024 * 1024)) {
            message.error(maxSize ? maxSize.message : `图片大小不能超过1M`);
            return Upload.LIST_IGNORE;
          }
          return true;
        },
        customRequest,
        onRemove: (file) => {
          if (file.status === 'done') {
            const key = file.response?.split('myqcloud.com/')[1];
            deleteFile({ name: key });
          }
          return true;
        },
      }}
      // initialValue={initialValue}
      transform={(value) => {
        if (Array.isArray(value) && value.length > 0) {
          return value[0].response;
        }
        return '';
      }}
      convertValue={(value) => {
        if (Array.isArray(value)) {
          return value;
        }
        return value
          ? [
              {
                response: value,
                thumbUrl: value,
                status: 'done',
                type: 'image/jpeg',
              },
            ]
          : [];
      }}
    />
  );
};

export default ProFormImg;
