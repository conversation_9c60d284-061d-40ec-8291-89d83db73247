import { useCurrentAccess } from '@/common/useCurrentAccess';
import CatalogTree from '@/components/CatalogTree';
import CommonDesc from '@/components/CommonDesc';
import ConditionalRender from '@/components/ConditionalRender';
import { getQuestionsCount } from '@/services/common_question';
import { autoCreate } from '@/services/composer_test_paper';
import { RollbackOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormCheckbox,
  ProFormDigit,
} from '@ant-design/pro-components';
import { connect, history, Link, useLocation, useParams } from '@umijs/max';
import {
  Button,
  Checkbox,
  Col,
  InputNumber,
  message,
  Radio,
  Row,
  Space,
} from 'antd';
import classNames from 'classnames';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';

interface SmartQuestionProps {
  isAdmin?: boolean;
}

const Detail: React.FC<SmartQuestionProps> = () => {
  const location = useLocation();
  const { id } = useParams();
  const { isSysAdmin } = useCurrentAccess();
  const { textbookChecklistId, name } = location?.state as any;
  const [selectedCatalog, setSelectedCatalog] = useState<any>({});
  const [options, setOptions] = useState<any[]>([]);
  const [getQuestionTypesData, setGetQuestionTypesData] = useState<any[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedPlat, setSelectedPlat] = useState<string>('system');
  const [questionSettings, setQuestionSettings] = useState<
    Record<string, Record<string, { count: number; score: number }>>
  >({});
  const [savedSettings, setSavedSettings] = useState<any[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  /** 是否清除之前的组卷 */
  const [isDestroy, setIsDestroy] = useState<boolean>(false);

  const checklist = useMemo(
    () => ({
      id: textbookChecklistId,
    }),
    [textbookChecklistId],
  );

  // 修改getQuestionTypes函数
  const getQuestionTypes = async (id: number) => {
    const { errCode, data, msg } = await getQuestionsCount(selectedPlat, {
      catalog: id,
    });
    if (errCode) {
      message.warning(`获取当前目录下的题型失败，请稍后重试 ${msg}`);
      return;
    }

    setGetQuestionTypesData(data);

    // 等待状态更新完成后再处理回显
    await new Promise((resolve) => {
      setTimeout(resolve, 0);
    });

    // 获取当前目录下已保存的设置
    const currentSettings = savedSettings.filter((item) => item.catalog === id);
    if (currentSettings.length > 0) {
      const savedTypes = [...new Set(currentSettings.map((item) => item.type))];
      setSelectedTypes(savedTypes);

      // 确保questionSettings已初始化
      const settings = { ...questionSettings };
      currentSettings.forEach((item) => {
        if (!settings[item.type]) {
          settings[item.type] = {};
        }
        settings[item.type][item.difficulty] = {
          count: item.num,
          score: item.score,
        };
      });
      setQuestionSettings(settings);
    }

    const uniqueTypes = Array.from(
      new Set(data.map((item: { typeCode: any }) => item.typeCode)),
    ).map((typeCode) => {
      const typeItem = data.find(
        (item: { typeCode: unknown }) => item.typeCode === typeCode,
      );
      return {
        label: typeItem?.typeName || '',
        value: typeCode,
      };
    });
    setOptions(uniqueTypes ?? []);
  };

  useEffect(() => {
    if (selectedCatalog?.id) {
      getQuestionTypes(selectedCatalog?.id);
    }
  }, [selectedCatalog, selectedPlat]);

  // 修改初始化设置逻辑
  useEffect(() => {
    const settings: typeof questionSettings = {};
    getQuestionTypesData.forEach((item) => {
      if (!settings[item.typeCode]) {
        settings[item.typeCode] = {};
      }
      // 检查是否有已保存的设置
      const savedSetting = savedSettings.find(
        (s) =>
          s.type === item.typeCode &&
          s.difficulty === item.difficultyCode &&
          s.catalog === selectedCatalog?.id,
      );
      settings[item.typeCode][item.difficultyCode] = {
        count: savedSetting?.num || 0,
        score: savedSetting?.score || 0,
      };
    });
    setQuestionSettings(settings);
  }, [getQuestionTypesData, savedSettings, selectedCatalog]);

  const handleGenerate = () => {
    const transformedData = selectedTypes.flatMap((typeCode) => {
      const typeItems = getQuestionTypesData.filter(
        (item) => item.typeCode === typeCode,
      );
      const typeName = typeItems[0]?.typeName || '';

      return Object.entries(questionSettings[typeCode] || {})
        .map(([difficultyCode, setting]) => ({
          name: typeName,
          type: typeCode,
          difficulty: difficultyCode,
          num: setting.count,
          score: setting.score,
          sourceTable: 'system',
          catalog: selectedCatalog?.id || 0,
        }))
        .filter((item) => item.num > 0);
    });

    // 更新逻辑：替换同目录同题型的设置
    setSavedSettings((prev) => [
      ...prev.filter(
        (item) =>
          item.catalog !== selectedCatalog?.id ||
          !transformedData.some((t) => t.type === item.type),
      ),
      ...transformedData,
    ]);
    message.success('已保存当前配置');
  };

  const handleRemoveSetting = (typeName: string) => {
    setSavedSettings((prev) => prev.filter((item) => item.name !== typeName));
    message.success('已移除配置');
  };

  const handleFinalGenerate = async () => {
    const { errCode, msg } = await autoCreate({
      testPaperNum: 1,
      isDestroy: isDestroy,
      composerPaperId: Number(id) ?? 1,
      data: savedSettings,
    });
    if (errCode) {
      message.warning(`生成试卷失败，请稍后重试 ${msg}`);
      setOpen(false);
    } else {
      message.success(`生成试卷成功`);
      setOpen(false);
    }
  };

  const handleCatalogSelect = (info: any) => {
    setSelectedCatalog(info);
    setSelectedTypes([]);
    setQuestionSettings({});
  };

  return (
    <>
      <div className={classNames(styles.header)}>
        <div className={styles.planType}>
          <Row justify="space-between" align="middle">
            <Col
              className={styles.title}
              onClick={() => {
                history.push(`/testPaperManagement/auto`);
              }}
            >
              <RollbackOutlined />
              <span className={styles.titleText} title={name ?? '未知方案'}>
                {name ?? '未知方案'}
              </span>
            </Col>
          </Row>
        </div>
        <span className={styles.optionsTypeText}>自动选题</span>
        <div className={styles.planAction}>
          <Link
            to={`/testPaperManagement/auto/record/${id}`}
            className={styles.planActionTitle}
          >
            自动组卷记录
          </Link>
        </div>
      </div>
      <div className={styles.wapper}>
        <div className={styles.catalogTree}>
          <CatalogTree
            readonly
            currentTextbookChecklist={checklist}
            onSelect={handleCatalogSelect}
          />
        </div>
        <ConditionalRender
          hasAccess={!!selectedCatalog.title}
          accessComponent={
            <Row className={styles.selectSaveContent}>
              <Col span={24} className={styles.selectedCatalog}>
                <div className={styles.selectedTitle}>已选单元：</div>
                <div className={styles.catalogName}>
                  {selectedCatalog.title}
                </div>
              </Col>
              <Col className={styles.contentWapper}>
                <>
                  <div className={styles.questionWapper}>
                    <Row>
                      <ConditionalRender
                        hasAccess={isSysAdmin}
                        accessComponent={<></>}
                        noAccessComponent={
                          <Col span={24}>
                            <div className={styles.sectionTitle}>
                              题库范围选择
                            </div>
                            <div className={styles.checkboxGroup}>
                              <Radio.Group
                                value={selectedPlat}
                                options={[
                                  { value: 'system', label: '系统题库' },
                                  { value: 'school', label: '学校题库' },
                                  { value: 'personal', label: '个人题库' },
                                ]}
                                onChange={(e) => {
                                  setSelectedPlat(e.target.value);
                                  setSelectedTypes([]);
                                  setQuestionSettings({});
                                }}
                              />
                            </div>
                          </Col>
                        }
                      />
                      <Col span={24}>
                        <div className={styles.sectionTitle}>题型选择</div>
                        <ConditionalRender
                          hasAccess={options.length > 0}
                          accessComponent={
                            <div className={styles.checkboxGroup}>
                              <Checkbox.Group
                                options={options}
                                value={selectedTypes}
                                onChange={(checkedValues) => {
                                  setSelectedTypes(checkedValues as string[]);
                                }}
                                style={{
                                  display: 'flex',
                                  flexWrap: 'wrap',
                                  gap: '12px',
                                }}
                              />
                            </div>
                          }
                          noAccessComponent={
                            <div className={styles.noAccess}>暂无题型</div>
                          }
                        />
                      </Col>

                      {/* 修改题型设置部分 */}
                      <Col span={24}>
                        <div className={styles.sectionTitle}>题型设置</div>
                        <div className={styles.typeSettingsContainer}>
                          {Array.from(
                            new Set(
                              getQuestionTypesData.map((item) => item.typeCode),
                            ),
                          ).map((typeCode) => {
                            const typeItems = getQuestionTypesData.filter(
                              (item) => item.typeCode === typeCode,
                            );
                            const isSelected = selectedTypes.includes(typeCode);
                            if (!isSelected) return null;
                            return (
                              <div key={typeCode} className={styles.selectCard}>
                                {typeItems[0].typeName}

                                <div className={styles.difficultySettings}>
                                  {typeItems.map((item) => (
                                    <div
                                      key={`${typeCode}-${item.difficultyCode}`}
                                      className={styles.difficultyRow}
                                    >
                                      <div className={styles.difficultyName}>
                                        {item.difficultyName}：{item.count}道
                                      </div>
                                      <div className={styles.inputField}>
                                        <div>题量：</div>
                                        <InputNumber
                                          min={1}
                                          max={item.count}
                                          value={
                                            questionSettings[typeCode]?.[
                                              item.difficultyCode
                                            ]?.count || undefined
                                          }
                                          onChange={(value) =>
                                            setQuestionSettings((prev) => ({
                                              ...prev,
                                              [typeCode]: {
                                                ...prev[typeCode],
                                                [item.difficultyCode]: {
                                                  ...prev[typeCode]?.[
                                                    item.difficultyCode
                                                  ],
                                                  count: value || undefined,
                                                },
                                              },
                                            }))
                                          }
                                        />
                                      </div>
                                      <div className={styles.inputField}>
                                        <div>分值：</div>

                                        <InputNumber
                                          min={1}
                                          max={100} // 添加最大分值限制
                                          value={
                                            questionSettings[typeCode]?.[
                                              item.difficultyCode
                                            ]?.score || undefined
                                          }
                                          onChange={(value) => {
                                            const newScore = value || undefined;
                                            setQuestionSettings((prev) => ({
                                              ...prev,
                                              [typeCode]: {
                                                ...prev[typeCode],
                                                [item.difficultyCode]: {
                                                  ...prev[typeCode]?.[
                                                    item.difficultyCode
                                                  ],
                                                  score: newScore,
                                                },
                                              },
                                            }));
                                          }}
                                        />
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </Col>
                      <Col
                        span={24}
                        style={{
                          textAlign: 'center',
                        }}
                      >
                        <Button
                          disabled={options.length <= 0}
                          type="primary"
                          onClick={handleGenerate}
                          className={styles.generateBtn}
                        >
                          确认生成
                        </Button>
                      </Col>
                    </Row>
                  </div>
                </>
              </Col>
              <Col span={24} className={styles.contentWapper}>
                {/* 已保存设置部分 */}
                {savedSettings.length > 0 && (
                  <div className={styles.savedSettings}>
                    <div className={styles.settingsHeader}>
                      <Space>
                        <h3>已保存的设置</h3>
                        <Checkbox
                          className={styles.clearTip}
                          checked={isDestroy}
                          onChange={(e) => setIsDestroy(e.target.checked)}
                        >
                          <span>清除原有自动生成的试卷</span>
                        </Checkbox>
                      </Space>
                      <div className={styles.totalInfo}>
                        总计: {savedSettings.reduce((sum, s) => sum + s.num, 0)}{' '}
                        题 /&nbsp;
                        {savedSettings.reduce(
                          (sum, s) => sum + s.num * s.score,
                          0,
                        )}
                        分
                      </div>
                    </div>
                    <div className={styles.settingsList}>
                      {Array.from(
                        new Set(savedSettings.map((s) => s.name)),
                      ).map((typeName, i) => {
                        const typeSettings = savedSettings.filter(
                          (s) => s.name === typeName,
                        );
                        const totalNum = typeSettings.reduce(
                          (sum, s) => sum + s.num,
                          0,
                        );
                        const totalScore = typeSettings.reduce(
                          (sum, s) => sum + s.num * s.score,
                          0,
                        );
                        return (
                          <div key={i} className={styles.settingItem}>
                            <div className={styles.typeName}>{typeName}</div>
                            <div className={styles.typeDetails}>
                              <span>题量: {totalNum}</span>
                              <span>总分: {totalScore}</span>
                            </div>
                            <Button
                              size="small"
                              type="link"
                              danger
                              onClick={() => handleRemoveSetting(typeName)}
                            >
                              移除
                            </Button>
                          </div>
                        );
                      })}
                    </div>
                    <div
                      style={{
                        textAlign: 'center',
                      }}
                    >
                      <Button
                        type="primary"
                        style={{
                          width: '20%',
                        }}
                        onClick={handleFinalGenerate}
                        className={styles.finalGenerateBtn}
                      >
                        生成最终试卷
                      </Button>
                    </div>
                  </div>
                )}
              </Col>
            </Row>
          }
          noAccessComponent={
            <div className={styles.noUnitHoursWapper}>
              <CommonDesc
                title="您尚未选择单元课时"
                className={styles.noUnitHours}
                desc="请先选择单元课时，再进行智能组题"
              />
            </div>
          }
        />
      </div>

      <ModalForm<{
        num: number;
        isDestroy?: boolean;
      }>
        width={400}
        title="保存组卷"
        open={open}
        autoFocusFirstInput
        layout="horizontal"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setOpen(false);
          },
        }}
        submitTimeout={2000}
        onFinish={async (values) => {
          if (!values.num) {
            message.warning(`请输入生成数量`);
            return Promise.reject();
          }
          const { errCode, msg } = await autoCreate({
            testPaperNum: values.num,
            isDestroy: values.isDestroy,
            composerPaperId: Number(id),
            data: savedSettings,
          });
          if (errCode) {
            message.warning(`生成试卷失败，请稍后重试 ${msg}`);
            setOpen(false);
          }
          message.success(`生成试卷成功`);
          setOpen(false);
        }}
      >
        <ProFormDigit
          label="生成试卷数量"
          name="num"
          min={1}
          fieldProps={{ precision: 0 }}
          rules={[
            {
              required: true,
              message: '请输入生成数量',
            },
          ]}
        />
        <ProFormCheckbox name="isDestroy" label="清除原有自动生成的试卷" />
      </ModalForm>
    </>
  );
};

export default connect(({ questionTypes }) => ({
  questionTypes,
}))(Detail);
