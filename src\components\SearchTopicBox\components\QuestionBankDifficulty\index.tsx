import { DictionarieState } from '@/models/dictionarie';
import { connect } from '@umijs/max';
import classNames from 'classnames';
import React, { useEffect } from 'react';
import styles from './index.less';

interface QuestionBankDifficultyProps {
  dictionarie?: DictionarieState;
  defaultValue?: string;
  onChenge?: (value: OptionsType | null) => void;
}

type OptionsType = {
  name: string;
  code: string;
};

const QuestionBankDifficulty: React.FC<QuestionBankDifficultyProps> = ({
  dictionarie,
  defaultValue,
  onChenge,
}) => {
  const [options, setOptions] = React.useState<OptionsType[]>([]);
  const [selectedCode, setSelectedCode] = React.useState<string>(
    defaultValue || 'all',
  );

  useEffect(() => {
    if (
      !dictionarie ||
      !Array.isArray(dictionarie.list) ||
      dictionarie.list.length === 0
    )
      return;

    const options = dictionarie.list
      .filter((item: { type: string }) => item.type === 'difficulty_type')
      .map((item: { name: string; code: string }) => {
        return {
          name: item.name,
          code: item.code,
        };
      });

    setOptions([
      {
        name: '全部',
        code: 'all',
      },
      ...options,
    ]);
    return;
  }, [dictionarie]);

  useEffect(() => {
    if (onChenge) {
      const selectInfo = options.find(
        (item: { code: string }) => item.code === selectedCode,
      );
      if (!selectInfo || selectInfo.code === 'all') {
        return onChenge(null);
      }
      onChenge(selectInfo);
    }
  }, [selectedCode]);

  return (
    <>
      <div className={styles.wapper}>
        <label>难度：</label>
        <div className={styles.options}>
          {options.map((item) => {
            return (
              <div
                key={item.code}
                className={classNames(styles.optionsItem, {
                  [styles.selected]: selectedCode === item.code,
                })}
                title={item.name}
                onClick={() => setSelectedCode(item.code)}
              >
                {item.name}
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};
export default connect(({ dictionarie }) => ({
  dictionarie,
}))(QuestionBankDifficulty);
