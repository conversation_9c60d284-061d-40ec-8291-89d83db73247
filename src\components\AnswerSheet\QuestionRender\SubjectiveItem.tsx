/* eslint-disable */
import dragImg from '@/assets/drag.png';
import TinyMCEEditor from '@/components/TinyMCEEditor';
import { Image } from 'antd';
import { useRef, useState } from 'react';
import styles from './questionItem.less';

const SubjectiveItem = ({
  data,
  paperSetting,
  templateLayout,
  callBack,
  editorChange,
  editorHeightChange,
}: {
  data: any;
  paperSetting: any;
  templateLayout: any;
  callBack: (data: any) => void;
  editorChange?: (content: string) => void; // 用于通知父组件编辑器内容变化
  editorHeightChange?: (height: number) => void; // 用于通知父组件编辑器高度变化
}) => {
  const moveRef = useRef<HTMLDivElement>(null);
  const dividerRef = useRef<HTMLDivElement>(null);
  const moveEndRef = useRef<HTMLDivElement>(null);
  const [editor, setEditor] = useState<any>();
  let moveParams: any = {};
  const editorMoveChange = (e: number) => {
    const n = data.key.split('-')[0];

    const i = templateLayout.find((e: { key: any }) => e.key === n);
    if (i) {
      const t = 25,
        n = i.height + e;
      i.height = n < t ? t : n;
      callBack(templateLayout);
    }
  };
  const mousemove = (e: any) => {
    e.preventDefault(), e.stopPropagation();
    if (dividerRef?.current && moveRef?.current && moveEndRef.current) {
      const t = e.clientY - moveParams.moveStart;
      if (moveParams.contentHeight + t <= 0) {
        dividerRef.current.style.bottom = moveParams.contentHeight + 'px';
        moveRef.current.style.height = '0px';
        return (moveParams.moveSpace = -moveParams.contentHeight), !1;
      }
      moveRef.current.style.bottom = 0 - t + 'px';
      dividerRef.current.style.height = moveParams.contentHeight + t + 'px';
      moveParams.moveSpace = t;
      moveEndRef.current.style.opacity = '1';
    }
  };
  const mouseup = () => {
    document.body.removeEventListener('mousemove', mousemove, !1),
      document.body.removeEventListener('mouseup', mouseup, !1);

    const e = editor.getElement().lastChild;

    let t = !0;
    (t =
      !!e &&
      (data.content ||
        !!e.textContent ||
        !![...e.childNodes].find((e) => ['IMG'].includes(e.nodeName)))),
      t;
    const n = 0,
      i = e ? e.offsetHeight || e.getBoundingClientRect().height : 0,
      r = (t ? e.offsetTop + i + 6 : 0) + n,
      o = moveParams.contentHeight + moveParams.moveSpace,
      s = o < r ? r : o;
    if (s > 0) {
      const e = Math.abs(s - moveParams.end_y);
      !data.isSplit && e < 10
        ? editorMoveChange(moveParams.end_y - moveParams.contentHeight)
        : editorMoveChange(s - moveParams.contentHeight);
    }
    0 === s && editorMoveChange(-parseInt(moveParams.contentHeight + 2));
    moveEndRef.current && (moveEndRef.current.style.opacity = '0'),
      (moveParams.moveSpace = 0),
      (moveParams.moveBorderWidth = 0);
  };
  const mousedownMove = (e: any, height?: number) => {
    e.preventDefault();
    moveParams = {
      moveStart: e.clientY,
      moveSpace: 0,
      contentHeight: height,
    };
    if (dividerRef?.current && moveEndRef?.current) {
      dividerRef.current.style.width = paperSetting?.preloadWidth + 'px';
      document.body.addEventListener('mousemove', mousemove, !1);
      document.body.addEventListener('mouseup', mouseup, !1);
      moveParams.end_y =
        data.contentHeight - editor.bodyElement.parentElement.offsetTop + 66;
      moveEndRef.current.style.top = moveParams.end_y + 'px';
    }
  };

  return (
    <div
      key={`section-${data.key}`}
      className={styles.subjectiveWrap}
      style={{
        minHeight: data?.height,
      }}
    >
      <TinyMCEEditor
        id={data.key}
        content={data?.content}
        height={data?.height}
        onEditorInit={(editor) => {
          editor.bodyElement.style.minHeight = data?.height + 'px';
          setEditor(editor);
        }}
        editorChange={editorChange}
        editorHeightChange={editorHeightChange}
      />
      <div className="moveBtn" ref={moveRef}>
        <Image
          src={dragImg}
          preview={false}
          onMouseDown={(e) => {
            e.stopPropagation();
            mousedownMove(e, data?.height);
          }}
        />
        <p
          className="moveBorder"
          ref={dividerRef}
          style={{
            width: 0,
            height: '32px',
          }}
        />
      </div>
      <div className="moveEnd" ref={moveEndRef}></div>
    </div>
  );
};

export default SubjectiveItem;
