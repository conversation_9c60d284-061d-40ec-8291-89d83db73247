import AnswerSheet from '@/components/AnswerSheet';
import CommonCard from '@/components/CommonCard';
import ConditionalRender from '@/components/ConditionalRender';
import HomeworkDesign from '@/components/HomeworkDesign';
import AddQuestion from '@/components/HomeworkDesign/PaperEditPage/AddQuestion';
import AnalysisPaper from '@/components/HomeworkDesign/PaperEditPage/AnalysisPaper';
import PaperStructure from '@/components/HomeworkDesign/PaperEditPage/Structure/PaperStructure';
import ScoreSetting from '@/components/HomeworkDesign/ScoreSetting';
import TitleBar from '@/components/TitleBar';
import { getCommonQuestions } from '@/services/common_question';
import {
  createComposerTestPaper,
  getComposerTestPaperStruct,
  updateComposerTestPaper,
} from '@/services/composer_test_paper';
import { downPaperToWord } from '@/services/htmlToWord';
import { getQueryParams } from '@/utils/calc';
import {
  ExclamationCircleOutlined,
  FileDoneOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  LeftOutlined,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import { ProForm, ProFormDigit, ProFormText } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import {
  Button,
  Col,
  FloatButton,
  Form,
  Modal,
  Row,
  Space,
  Spin,
  message,
} from 'antd';
import classNames from 'classnames';
import { nanoid } from 'nanoid';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

interface PaperPreviewEditProps {
  onClick: () => void;
  newQuestionData: any;
  homeworkId?: any;
  /** 仅预览 */
  onlyPreviewView?: boolean;
  /** 试卷信息 */
  questionInfo?: any;
  /** 试卷保存成功的回调 */
  onSave?: () => void;
}

const PaperPreviewEdit: React.FC<PaperPreviewEditProps> = ({
  onClick,
  newQuestionData,
  homeworkId,
  questionInfo,
  onSave,
  onlyPreviewView = false,
}) => {
  const { id } = useParams();
  const { type } = getQueryParams();
  const [curHomeID, setCurHomeID] = useState<any>(homeworkId);
  const [isWordDownloading, setIsWordDownloading] = useState<boolean>(false);
  const [listLoading, setListLoading] = useState<boolean>(false);
  const [questionData, setQuestionData] = useState<API.SystemQuestion[]>([]);
  const [strucData, setStrucData] = useState<any>();
  const [totalScore, setTotalScore] = useState<number>();
  const [questionModal, setQuestionModal] = useState<any>({
    open: false,
  });

  /** 仅预览试题 */
  const [onlyPreview, setOnlyPreview] = useState<boolean>(onlyPreviewView);
  const [form] = Form.useForm<{
    name: string;
    score: number;
  }>();

  /**
   * 转换试卷结构数据格式
   * @param originalData 原始数据结构，包含大题顺序和大题详情
   * @param originalData.bigQuestionOrder 大题ID顺序数组
   * @param originalData.bigQuestions 大题详情对象，key为大题ID
   * @returns 转换后的试卷结构数组，包含排序和分数信息
   */
  function transformPaperStructure(originalData: {
    bigQuestionOrder: any[];
    bigQuestions: { [x: string]: any; sortOrder: number };
  }) {
    return originalData.bigQuestionOrder.map((id, index) => {
      const bigQuestion = originalData.bigQuestions[id];
      return {
        id: bigQuestion.id,
        name: bigQuestion.name,
        questionIds: bigQuestion.questionIds,
        sortOrder: bigQuestion.sortOrder ?? index + 1,
        score: 0,
      };
    });
  }
  const initHomeData = async () => {
    setListLoading(true);
    const result = await getCommonQuestions(
      newQuestionData?.map((item: any) => {
        return {
          question_id: item._id || item.questionBankId,
          source_table: item.sourceTable,
        };
      }),
    );
    if (result?.errCode) {
      setListLoading(false);
      message.error('获取试题详情失败，请联系管理员或稍后再试！' + result?.msg);
      return;
    }

    // 设置初始分数
    const questionsWithScore = result?.data?.map((item: any) => {
      const matchedQuestion = newQuestionData.find(
        (q: any) => q.questionBankId === item._id,
      );
      return {
        ...item,
        score: matchedQuestion?.score || 0,
      };
    });

    setQuestionData(questionsWithScore ?? []);
    const hideId = 'hidden_' + nanoid();
    let newData: any = {
      bigQuestionOrder: [hideId],
      bigQuestions: {},
    };
    newData.bigQuestions[hideId] = {
      id: hideId,
      name: '暂无分组',
      questionIds: result?.data?.map((item: any) => item?._id || null),
    };
    setQuestionData((result?.data as any) ?? []);
    setStrucData(newData);
    setListLoading(false);
  };
  const handleDownloadWord = async () => {
    // 获取所有questionIds并按顺序排序
    const sortedQuestionIds = strucData.bigQuestionOrder.flatMap(
      (bigQuestionId: string) =>
        strucData.bigQuestions[bigQuestionId].questionIds,
    );

    // 优化1：提前创建Map提高性能
    const questionMap = new Map(
      questionData.map((item: any) => [item._id, item]),
    );

    // 优化2：使用filter(Boolean)过滤掉undefined值
    const sortedQuestions = sortedQuestionIds
      .map((id: string) => questionMap.get(id))
      .filter(Boolean);
    const questions = sortedQuestions.map((item: any) => {
      return {
        question_id: item._id,
        source_table: item.source_table || item.tableName,
      };
    });
    const { bigQuestions, bigQuestionOrder } = strucData;
    const data = bigQuestionOrder?.map((key: string) => bigQuestions?.[key]);
    try {
      setIsWordDownloading(true);
      const {
        errCode,
        data: responseData,
        msg,
      } = await downPaperToWord({
        name: questionInfo?.name || '下载试卷',
        struct: data,
        questions: questions,
        options: {
          fontSize: 14,
          fontFamily:
            'Microsoft YaHei, Helvetica Neue, PingFang SC, sans-serif',
          lineHeight: 1.5,
        },
      });
      if (errCode) {
        setIsWordDownloading(false);
        return message.error('下载失败，请联系管理员后重试！' + msg);
      }
      const uint8Array = new Uint8Array(responseData.data);
      // 假设response.data是Buffer数据
      const blob = new Blob([uint8Array], { type: 'application/msword' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `${questionInfo?.name || '下载试卷'}.doc`;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error('下载失败:', error);
    } finally {
      setIsWordDownloading(false);
    }
  };
  /**
   * 获取作业详情
   *
   */
  const getHomeworkDetail = async () => {
    setListLoading(true);
    const result = await getCommonQuestions(
      newQuestionData?.map((item: any) => {
        return {
          question_id: item._id || item.questionBankId,
          source_table: item.sourceTable,
        };
      }),
    );
    if (result?.errCode) {
      setListLoading(false);
      message.error('获取试题详情失败，请联系管理员或稍后再试！' + result?.msg);
      return;
    }
    // 设置初始分数
    const questionsWithScore = result?.data?.map((item: any) => {
      const matchedQuestion = newQuestionData.find(
        (q: any) => q.questionBankId === item._id,
      );
      return {
        ...item,
        score: matchedQuestion?.score || 0,
      };
    });

    setQuestionData(questionsWithScore ?? []);
    const { errCode, data, msg } = await getComposerTestPaperStruct({
      composerTestPaperId: curHomeID,
    });
    if (errCode) {
      setListLoading(false);
      message.error('试卷结构获取失败，请联系管理员或稍后再试！' + msg);
      return;
    }
    let newData: any = data?.list;
    const bigQuestionOrder = newData?.map((v: any) => v.id);
    const bigQuestions = newData?.reduce((acc: any, item: any) => {
      acc[item.id] = item;
      return acc;
    }, {});
    newData = {
      bigQuestionOrder,
      bigQuestions,
    };
    setStrucData(newData);
    setListLoading(false);
  };
  /**
   * 更新作业信息
   *
   */
  const refreshHomework = () => {
    // 获取所有questionIds
    const allQuestionIds = Object.values(strucData.bigQuestions).flatMap(
      (bigQuestion: any) => bigQuestion.questionIds,
    );

    // 筛选questionData中_id匹配的数据
    const matchedQuestions = questionData.filter((item) =>
      allQuestionIds.includes(item._id),
    );

    setQuestionData(matchedQuestions); // 输出匹配的试题数据
  };
  /**
   * 关闭模态框
   */
  const closeModal = () => {
    setQuestionModal({
      open: false,
    });
  };
  /**
   * 选题
   */
  const chooseQuestion = () => {
    setQuestionModal({
      open: true,
      questionData,
      setQuestionData,
      strucData,
      setStrucData,
    });
  };

  /** 保存试题 */
  const handleSave = async () => {
    const details = questionData?.map((item) => {
      return {
        questionBankId: item._id,
        score: item.score || 0,
        sourceTable: item.tableName,
      };
    });
    Modal.confirm({
      title: '试卷基本信息',
      icon: <ExclamationCircleOutlined />,
      content: (
        <ProForm
          layout="horizontal"
          submitter={false}
          form={form}
          labelCol={{
            flex: '5.6em',
          }}
          style={{
            marginTop: 20,
          }}
        >
          <ProFormText
            name="name"
            label="试卷名称"
            rules={[{ required: true, message: '请输入试卷名称！' }]}
          />
          <ProFormDigit
            label="试卷总分"
            name="score"
            fieldProps={{
              value: totalScore,
              readOnly: true,
              bordered: false,
            }}
          />
        </ProForm>
      ),
      onOk: async () => {
        const { name } = form.getFieldsValue();
        let result: any;
        if (!name) {
          message.warning('请填写试卷名称和总分');
          return Promise.reject();
        }
        if (curHomeID) {
          result = await updateComposerTestPaper(curHomeID, {
            composerPaperId: id,
            ruleType: '手工',
            name,
            score: totalScore || 0,
            structs: transformPaperStructure(strucData),
            details,
          });
          await getHomeworkDetail();
        } else {
          result = await createComposerTestPaper({
            composerPaperId: id,
            ruleType: '手工',
            name,
            score: totalScore || 0,
            structs: transformPaperStructure(strucData),
            details,
          });
        }

        if (result?.errCode) {
          message.error(`试卷保存失败: ${result?.msg}`);
          return Promise.reject();
        }
        form.resetFields();
        setCurHomeID(result?.data?.id);
        message.success('试卷保存成功');
        onSave?.();
      },
      onCancel: () => {
        form.resetFields();
      },
    });
  };

  const handleScore = (questionScore: any, totalScore: number) => {
    const newQuestionData = questionData?.map((item: any) => {
      return {
        ...item,
        score: questionScore?.find((v: any) => v.id === item._id)?.score || 0,
      };
    });
    setQuestionData(newQuestionData);
    setTotalScore(totalScore);
  };

  useEffect(() => {
    if (type) {
      chooseQuestion();
    }
  }, [type]);

  useEffect(() => {
    if (strucData) {
      refreshHomework();
    }
  }, [strucData]);

  useEffect(() => {
    if (curHomeID) {
      getHomeworkDetail();
    } else {
      initHomeData();
    }
  }, [curHomeID]);

  useEffect(() => {
    if (homeworkId) {
      setCurHomeID(homeworkId);
    }
  }, [homeworkId]);

  /** 回填试卷名称和总分 */
  useEffect(() => {
    if (questionInfo) {
      form.setFieldsValue({
        name: questionInfo?.name,
        score: questionInfo?.score,
      });
    }
  }, [questionInfo, totalScore]);

  useEffect(() => {
    if (questionData.length > 0) {
      const sum = questionData.reduce(
        (total, item) => total + (item.score || 0),
        0,
      );
      setTotalScore(sum);
      form.setFieldsValue({ score: sum });
    }
  }, [questionData]);

  return (
    <div className={styles.homeworkTemplate}>
      <div title={onlyPreview ? '切换全部模式' : '切换预览模式'}>
        <ConditionalRender
          hasAccess={!onlyPreview}
          accessComponent={
            <FloatButton
              icon={
                onlyPreview ? (
                  <FullscreenExitOutlined />
                ) : (
                  <FullscreenOutlined />
                )
              }
              onClick={() => {
                setOnlyPreview(!onlyPreview);
              }}
            />
          }
        />
      </div>
      <CommonCard
        activeTitle
        title={
          <div className={styles.templateHeader}>
            <Button type="link" icon={<LeftOutlined />} onClick={onClick}>
              返回列表
            </Button>
          </div>
        }
        centerDom={
          questionInfo ? (
            <>
              <Space size="large">
                <span>预览试卷：{questionInfo?.name}</span>
                <span>
                  试卷总分：{totalScore ?? questionInfo?.score ?? 0} 分
                </span>
              </Space>
            </>
          ) : (
            '试卷预览'
          )
        }
      >
        <Space>
          <Button
            icon={<VerticalAlignBottomOutlined />}
            onClick={handleDownloadWord}
            loading={isWordDownloading}
          >
            下载试卷
          </Button>
          <AnswerSheet
            paperName={questionInfo?.name}
            strucData={strucData}
            questionData={questionData}
          />
          <ConditionalRender
            hasAccess={!onlyPreview}
            accessComponent={
              <>
                <ScoreSetting
                  selectedQuestion={{
                    strucData,
                    questionData,
                  }}
                  setScore={handleScore}
                />
                <Button icon={<FileDoneOutlined />} onClick={handleSave}>
                  保存试卷
                </Button>
              </>
            }
          />
        </Space>
      </CommonCard>
      <div className={styles.container}>
        <Spin tip="数据加载中..." spinning={listLoading}>
          <Row gutter={[16, 16]}>
            <ConditionalRender
              hasAccess={!onlyPreview}
              accessComponent={
                <Col flex="360px">
                  <div
                    className={classNames('commonWapper', styles.leftWrapper)}
                  >
                    <TitleBar
                      title="试卷分析"
                      style={{
                        padding: '0 0 14px',
                        borderRadius: 0,
                        borderBottom: '1px solid #dadada',
                      }}
                    />
                    <AnalysisPaper type="preview" questionList={questionData} />
                  </div>
                </Col>
              }
            />
            <Col flex="1">
              <div className={classNames('commonWapper', styles.centerWrapper)}>
                <HomeworkDesign
                  type={!onlyPreview ? undefined : 'check'}
                  showDuration={false}
                  settingScore={true}
                  questionConstrue={strucData}
                  questionList={questionData}
                  sortQuestion={setStrucData}
                  replaceQuestion={setQuestionData}
                />
              </div>
            </Col>
            <ConditionalRender
              hasAccess={!onlyPreview}
              accessComponent={
                <Col flex="360px">
                  <div
                    className={classNames('commonWapper', styles.rightWrapper)}
                  >
                    <PaperStructure
                      selectTopic={false}
                      showClear={false}
                      strucData={strucData}
                      setStrucData={setStrucData}
                      setQuestionData={setQuestionData}
                      chooseQuestion={chooseQuestion}
                    />
                  </div>
                </Col>
              }
            />
          </Row>
        </Spin>
      </div>
      <AddQuestion questionModal={questionModal} callback={closeModal} />
    </div>
  );
};
export default PaperPreviewEdit;
