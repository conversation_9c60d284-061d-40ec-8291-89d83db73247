/*
 * @Description: 课时作业范本管理
 * @Date: 2025-02-11 10:00:28
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-11 10:02:47
 */

import { workBook } from '@/assets';
import CommonCard from '@/components/CommonCard';
import ConditionalRender from '@/components/ConditionalRender';
import { DictionarieState } from '@/models/dictionarie';
import {
  complianceDesignCreate,
  complianceDesignIndex,
  complianceDesignRemove,
  complianceDesignUpdate,
} from '@/services/compliance_detection_design';
import { EditTwoTone, PlusOutlined } from '@ant-design/icons';
import { ActionType, ProList } from '@ant-design/pro-components';
import { connect, history, useLocation, useModel } from '@umijs/max';
import { Button, message, Popconfirm, Space } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import AddTemplate from '../AddTemplate';
import styles from './index.less';

type TemplateModalType = {
  width?: number;
  open: boolean;
  title: string | undefined;
  data: any;
};

type ParamsType = {
  pageSize?: number;
  current?: number;
};
const HomeworkTemplate = ({
  type,
  subType,
  dictionarie,
}: {
  type: string;
  subType: string;
  dictionarie: DictionarieState;
}) => {
  const { initialState: currentUser } = useModel('@@initialState');
  const actionRef = useRef<ActionType>();
  const { pathname } = useLocation();
  const [baseModal, setBaseModal] = useState<TemplateModalType>();
  const [curSection, setCurSection] = useState<any>();
  /* 初始化数据 */
  const initData = async (params?: ParamsType) => {
    const { pageSize, current } = params || {};
    const { errCode, data, msg } = await complianceDesignIndex({
      creator_id: currentUser?.id,
      grade_section_code: curSection?.code,
      template_type: subType,
      offset: ((current || 1) - 1) * (pageSize || 10),
      limit: pageSize || 10,
    });
    if (errCode) {
      message.warning('数据获取失败,请稍后重试' + msg);
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
    return {
      data: data?.list ?? [],
      success: true,
      total: data?.total,
    };
  };

  const deleteTemplate = async (id?: string) => {
    const { errCode, msg } = await complianceDesignRemove(id || '');
    if (errCode) {
      message.warning('删除失败,请稍后重试！' + msg);
      return;
    }
    message.success('删除成功');
    actionRef?.current?.reload();
  };

  const updateTemplate = async (id?: string, status?: string) => {
    const { errCode, msg } = await complianceDesignUpdate(id || '', {
      status: status || '草稿',
    });
    if (errCode) {
      message.warning('操作失败,请稍后重试！' + msg);
      return;
    }
    message.success('操作成功');
    actionRef?.current?.reload();
  };
  const addTemplate = () => {
    setBaseModal({
      width: 600,
      open: true,
      title: '新增达标检测设计',
      data: undefined,
    });
  };

  const onSubmit = async (values: any) => {
    if (values?.id) {
      const { id, ...rest } = values;
      const { errCode, msg } = await complianceDesignUpdate(id, {
        ...rest,
      });
      if (errCode) {
        message.warning('修改失败,请稍后重试！' + msg);
        return;
      }
      message.success('修改成功');
    } else {
      const { errCode, msg } = await complianceDesignCreate(values);
      if (errCode) {
        message.warning('创建失败,请稍后重试！' + msg);
        return;
      }
      message.success('创建成功');
    }
    setBaseModal({
      open: false,
      title: undefined,
      data: undefined,
    });
    actionRef?.current?.reload();
  };
  useEffect(() => {
    if (dictionarie && dictionarie?.currentList?.length) {
      const curSec = dictionarie.currentList.find(
        (item: { name: string }) => item.name === type,
      );
      if (curSec) {
        setCurSection(curSec);
      }
    }
  }, [type, dictionarie]);
  return (
    <>
      <CommonCard title={`${type}${subType}达标检测设计管理`}>
        <Button type="primary" icon={<PlusOutlined />} onClick={addTemplate}>
          新增达标检测设计
        </Button>
      </CommonCard>
      <div className={classNames('commonWapper', styles.listWapper)}>
        <ProList<any>
          actionRef={actionRef}
          toolBarRender={false}
          search={false}
          rowKey="id"
          params={{
            curSection,
            currentUser,
            subType,
          }}
          request={initData}
          pagination={{
            pageSize: 10,
            align: 'center',
            showTitle: false,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => {
              return `总共 ${total} 条`;
            },
          }}
          showActions="hover"
          metas={{
            title: {
              dataIndex: 'name',
              render: (_, row) => {
                return (
                  <>
                    {row?.name}
                    {/* <Tag
                      style={{
                        marginLeft: 16,
                      }}
                      color={row?.status === '草稿' ? 'blue' : 'green'}
                    >
                      {row?.status}
                    </Tag> */}
                    {/* {row?.status === '草稿' && ( */}
                    <EditTwoTone
                      title="修改达标检测设计名称"
                      className={styles.editTwoToneIcon}
                      onClick={() => {
                        setBaseModal({
                          width: 600,
                          open: true,
                          title: '编辑达标检测设计',
                          data: row,
                        });
                      }}
                    />
                    {/* // )} */}
                  </>
                );
              },
            },
            avatar: {
              dataIndex: 'avatar',
              render: () => {
                return <img alt="" src={workBook} style={{ width: 50 }} />;
              },
            },
            description: {
              dataIndex: 'updatedAt',
              render: (_, row) => {
                return (
                  <Space wrap>
                    <span>
                      学段：
                      {row?.grade_section_name}
                    </span>
                    <span>
                      年级：
                      {row?.grade_name}
                    </span>
                    <span>
                      科目：
                      {row?.subject_name}
                    </span>
                    <span>
                      版材：
                      {row?.textbook_version}
                    </span>
                    <span>
                      册次：
                      {row?.volume}
                    </span>
                    <ConditionalRender
                      hasAccess={INDEPENDENT_QUESTION_BANK}
                      accessComponent={
                        <span>
                          来源：
                          {row?.source}
                        </span>
                      }
                    />
                    <span>
                      更新：
                      {dayjs(row?.updatedAt).format('YYYY-MM-DD HH:mm')}
                    </span>
                  </Space>
                );
              },
            },
            actions: {
              render: (_dom, row) => {
                return (
                  <ConditionalRender
                    hasAccess={row?.status === '草稿'}
                    accessComponent={
                      <>
                        <Button
                          key="detail"
                          type="link"
                          onClick={() => {
                            history.push(`${pathname}/${row?.id}`, row);
                          }}
                        >
                          进入达标检测设计
                        </Button>
                        {/* <Popconfirm
                          key="publish"
                          title="发布后当前版本不可再做修改，确认发布吗?"
                          onConfirm={() => updateTemplate(row?.id, '发布')}
                          okText="确定"
                          cancelText="取消"
                          placement="topRight"
                        >
                          <Button color="cyan" variant="link">
                            发布
                          </Button>
                        </Popconfirm> */}
                        <Popconfirm
                          key="deletes"
                          title="删除后数据将无法恢复，确认删除吗?"
                          onConfirm={() => deleteTemplate(row?.id)}
                          okText="确定"
                          cancelText="取消"
                          placement="topRight"
                        >
                          <Button type="link" danger>
                            刪除
                          </Button>
                        </Popconfirm>
                      </>
                    }
                    noAccessComponent={
                      <>
                        <Button
                          key="detail"
                          type="link"
                          onClick={() => {
                            history.push(`${pathname}/${row?.id}`, row);
                          }}
                        >
                          进入达标检测设计
                        </Button>
                        <Popconfirm
                          key="withdraw"
                          title="确认撤销当前达标检测设计的发布状态吗?"
                          onConfirm={() => updateTemplate(row?.id, '草稿')}
                          okText="确定"
                          cancelText="取消"
                          placement="topRight"
                        >
                          <Button type="link" danger>
                            撤销
                          </Button>
                        </Popconfirm>
                      </>
                    }
                  />
                );
              },
            },
          }}
        />
      </div>
      <>
        <AddTemplate
          width={baseModal?.width ?? 450}
          open={baseModal?.open}
          title={baseModal?.title}
          initialValues={baseModal?.data}
          curSection={curSection}
          subType={subType}
          onFinish={onSubmit}
          layout="horizontal"
          modalProps={{
            destroyOnClose: true,
            onCancel: () => {
              setBaseModal({
                open: false,
                title: undefined,
                data: undefined,
              });
            },
            styles: {
              body: {
                marginTop: 20,
              },
            },
          }}
        />
      </>
    </>
  );
};
export default connect(({ dictionarie }) => ({ dictionarie }))(
  HomeworkTemplate,
);
