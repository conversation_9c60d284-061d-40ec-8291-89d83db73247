/* eslint-disable */
import dragImg from '@/assets/drag.png';
import { Image } from 'antd';
import { useRef } from 'react';
import { heightToWords } from '../util';
import styles from './questionItem.less';
const ChineseWriting = ({
  data,
  paperData,
  templateLayout,
  callBack,
}: {
  data: any;
  paperData: any;
  templateLayout: any;
  callBack: (data: any) => void;
}) => {
  const moveRef = useRef<HTMLDivElement>(null);
  const dividerRef = useRef<HTMLDivElement>(null);
  const wrapRef = useRef<HTMLDivElement>(null);
  let moveParams: any = {};
  const elementMoveChange = (e: any) => {
    const n = data.key.split('-')[0],
      i = paperData.reduce((e: any, t: any[]) => {
        const i = t.filter((e) => e.key.split('-')[0] === n);
        return [...e, ...i];
      }, []),
      r = i.reduce((e: any, t: { height: any }) => e + t.height, 0),
      o = templateLayout.find((e: { key: string }) => e.key === n);
    if (o) {
      const t = 69,
        n = r + e;

      if (((o.height = n < t ? t : n), 'chineseWriting' === o.type)) {
        const { words: e, rows: t } = heightToWords(
          o.height - 7 - (o.hasScoreBox ? 64 : 38),
        );
        (o.currentWords = e), (o.rows = t);
      }
      callBack(templateLayout);
    }
  };
  const mousedownMove = (e: any) => {
    e.preventDefault(), e.stopPropagation();
    moveParams = {
      moveStart: e.clientY,
      moveSpace: 0,
      contentHeight: data?.height,
      moveBorderWidth: 0,
    };
    if (dividerRef?.current && moveRef?.current && wrapRef?.current) {
      dividerRef.current.style.width =
        wrapRef?.current.getBoundingClientRect().width + 'px';
      document.body.addEventListener('mousemove', mousemove, !1),
        document.body.addEventListener('mouseup', mouseup, !1),
        (moveParams.end_y = data.contentHeight - wrapRef?.current?.offsetTop);
    }
  };
  const mousemove = (e: any) => {
    e.preventDefault();
    const t = e.clientY - moveParams.moveStart;
    data.height + t <= 0
      ? (moveParams.moveSpace = -data.height)
      : (moveParams.moveSpace = t);
    if (dividerRef.current && moveRef.current) {
      moveRef.current.style.bottom = 0 - moveParams.moveSpace + 'px';
      dividerRef.current.style.height =
        data.height + moveParams.moveSpace + 'px';
    }
  };
  const mouseup = () => {
    document.body.removeEventListener('mousemove', mousemove, !1),
      document.body.removeEventListener('mouseup', mouseup, !1);
    const e = data.height + moveParams.moveSpace;
    if (e >= 0) {
      const t = Math.abs(e - moveParams.end_y);
      t < 10
        ? elementMoveChange(moveParams.end_y - data.height)
        : elementMoveChange(
            0 === e ? moveParams.moveSpace - 2 : moveParams.moveSpace,
          );
    }
    (moveParams.moveSpace = 0), (moveParams.moveBorderWidth = 0);
  };
  return (
    <div
      className={styles.questionWriting}
      key={`section-${data.key}-${data?.height}`}
      ref={wrapRef}
    >
      {new Array(data.rows).fill(null).map((_, i) => {
        return (
          <div className="rows" key={`rows_${i}`}>
            {new Array(data.rowWords).fill(null).map((_, d) => {
              return (
                <span className="words" key={`words_${i}_${d}`}>
                  {(data.startWords + data.rowWords * i + d + 1) % 100 == 0 && (
                    <b>
                      <i />
                      {data.startWords + data.rowWords * i + d + 1}
                    </b>
                  )}
                </span>
              );
            })}
          </div>
        );
      })}

      <div className="moveBtn" ref={moveRef}>
        <Image
          src={dragImg}
          preview={false}
          onMouseDown={(e) => {
            mousedownMove(e);
          }}
        />
        <p
          className="moveBorder"
          ref={dividerRef}
          style={{
            width: 0,
            height: '32px',
          }}
        />
      </div>
    </div>
  );
};

export default ChineseWriting;
