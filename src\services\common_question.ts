import { request } from '@umijs/max';

/** 根据试题ids获取多个试题  POST /common_question/list */
export async function getCommonQuestions(
  body: { question_id: string; source_table: string }[],
) {
  return request<API.ResType<API.SystemQuestion>>('/common_question/list', {
    method: 'POST',
    data: body,
  });
}
/** 分析试题数据  POST /common_question/analysis */
export async function analysisQuestions(body: any) {
  return request<API.ResType<API.SystemQuestion>>('/common_question/analysis', {
    method: 'POST',
    data: body,
  });
}
/** 分析试题数据  POST /common_question/get_filter_ids/{type} */
export async function getSimilarQuestions(
  type: string,
  body: API.similarQuestionsParams,
) {
  return request<API.ResType<API.SystemQuestion>>(
    `/common_question/get_filter_ids/${type}`,
    {
      method: 'POST',
      data: body,
    },
  );
}
/** 获取公共试题列表  POST /common_question/get_list/{type} */
export async function getCommonAllQuestions(type: string, body: any) {
  return request<API.ResType<API.SystemQuestion>>(
    `/common_question/get_list/${type}`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 编辑系统试题  PUT /common_question/{id}/{type} */
export async function updatetCommonQuestion(
  id: string,
  type: string,
  body: Partial<API.SystemQuestion>,
) {
  return request<API.ResType<API.SystemQuestion>>(
    `/common_question/${id}/${type}`,
    {
      method: 'PUT',
      data: body,
    },
  );
}
/** 编辑父子题 POST /common_question/associate_update/{pid}/{type} */
export async function updateAssociatetCommonQuestion(
  pid: string,
  type: string,
  body: Partial<API.AssociateQuestion>,
) {
  return request<API.ResType<API.AssociateQuestion>>(
    `/common_question/associate_update/${pid}/${type}`,
    {
      method: 'POST',
      data: body,
    },
  );
}
/** 创建公共试题  POST /common_question/{type} */
export async function createCommonQuestion(type: string, body: any) {
  return request<any>(`/common_question/${type}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}
/** 创建公共父子试题  POST /common_question/associate/{type} */
export async function createAssociatetCommonQuestion(type: string, body: any) {
  return request<any>(`/common_question/associate/${type}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}
/** 批量创建试题标记信息  POST /common_question/bulk_add_user_tag */
export async function editUserTag(body: any) {
  return request<any>(`/common_question/bulk_add_user_tag`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

// /** 移除试题标记  POST /common_question/remove_user_tag{type} */
// export async function removeUserTag(type: string, body: any) {
//   // return request<any>(`/common_question/remove_user_tag${type}`, {
//   return request<any>(`/system_question/remove_user_tag`, {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     data: body,
//   });
// }

/** 根据用户id获取用户标记信息  GET /common_question/get_user_tag/{userId} */
export async function getUserTags(params: Record<string, any>) {
  const { userId } = params;
  return request<any>(`/common_question/get_user_tag/${userId}`, {
    method: 'GET',
    params,
  });
}
/** 查询列表  GET /common_question/get_count */
export async function getQuestionsCount(
  path: string,
  params: Record<string, any>,
) {
  return request<API.ResType<any>>(`/common_question/get_count/${path}`, {
    method: 'GET',
    params,
  });
}
