.titleBar {
  width: 100%;
  padding: 16px 0;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: var(--border-radius-base);

  .title {
    font-weight: bold;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    color: #666;

    &.prefix {
      color: #333;

      &::before {
        width: 4px;
        height: 20px;
        background: var(--primary-color);
        display: inline-block;
        content: '';
        border-radius: 2px;
        margin-right: 8px;
      }
    }

    :global(.anticon) {
      color: var(--primary-color);
      margin-right: 8px;
      font-size: 16px;
      vertical-align: middle;
    }
  }
}
