.construction {
  padding: 10px 6px 0 0;

  .questionArea {
    padding: 0;
    background: white;

    .titleArea {
      .titleNum {
        display: inline-block;
        max-width: calc(100% - 130px);
        font-weight: 400;
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: grab;
      }

      .action {
        float: right;
        margin-left: 10px;

        button {
          padding: 0 8px;
        }
      }
    }

    .question {
      width: 100%;
      // min-height: 40px;
      padding: 10px 0;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: start;

      div {
        text-align: center;
        line-height: 28px;
        margin-right: 7px;
        margin-bottom: 7px;
        cursor: pointer;
        user-select: none;

        span {
          height: 30px;
          padding: 0 8px;
          background-color: #fff;
          border: 1px solid #dfdfdf;
          display: block;
          width: 100%;

          &:hover {
            border: 1px solid #1890ff;
          }
        }
      }

      :global(.invisible) {
        display: none;
      }
    }
  }
}
