/*
 * @Description: Col 包裹，可按条件切换是否包裹
 * @Date: 2025-02-22 15:09:23
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-22 15:16:19
 */
import { Col, ColProps } from 'antd';
import React from 'react';

const ColWrapper: React.FC<{
  colProps?: ColProps;
  children: React.ReactNode;
}> = ({ colProps, children }) => {
  if (colProps) {
    return <Col {...colProps}>{children}</Col>;
  }
  return children;
};

export default ColWrapper;
