import { useCurrentAccess } from '@/common/useCurrentAccess';
import ConditionalRender from '@/components/ConditionalRender';
import DiffModal from '@/components/DiffModal';
import TopicContent from '@/components/QuestionBank/TopicContent';
import { bulkCheck, getPersonalQuestion } from '@/services/personal';
import { CheckOutlined, CloseOutlined, DiffOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Button,
  Checkbox,
  Empty,
  message,
  Modal,
  Pagination,
  PaginationProps,
  Popover,
  Space,
  Spin,
  Tag,
} from 'antd';
import TextArea from 'antd/es/input/TextArea';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import styles from './index.less';

export enum QuestionCheckStatus {
  '待审核' = '待审核',
  '已通过' = '已通过',
  '已拒绝' = '已拒绝',
}
interface UnAuditedProps {
  subject?: number;
}
export interface UnAuditedRef {
  /** 展开或收起所有试题 */
  expandAll: (expand: boolean) => void;
  /** 是否所有试题都已展开 */
  isAllExpanded: boolean;
}

const UnAudited = React.forwardRef<UnAuditedRef, UnAuditedProps>(
  ({ subject }, ref) => {
    const { isAdmin } = useCurrentAccess();
    const { initialState } = useModel('@@initialState');
    const [dataSource, setDataSource] = React.useState<any[]>([]);
    const [isAllExpanded, setIsAllExpanded] = React.useState(false);
    const [pagination, setPagination] = React.useState<PaginationProps>({
      current: 1,
      pageSize: 10,
    });
    const [total, setTotal] = React.useState(0);
    const [loading, setLoading] = React.useState(false);
    const [selectedIds, setSelectedIds] = React.useState<string[]>([]);
    const [diffVisible, setDiffVisible] = React.useState(false);
    const [currentQuestion, setCurrentQuestion] = React.useState<any>(null);

    React.useImperativeHandle(ref, () => ({
      expandAll: (expand: boolean) => {
        setDataSource((prev) =>
          prev.map((item) => ({ ...item, expanded: expand })),
        );
        setIsAllExpanded(expand);
      },
      isAllExpanded,
    }));
    /** 获取待审试题列表 */
    const getUnAuditedQuestionList = async () => {
      setLoading(true);
      try {
        const { errCode, data, msg } = await getPersonalQuestion({
          offset: (pagination?.current || 1) - 1,
          limit: pagination?.pageSize || 10,
          checkStatus: QuestionCheckStatus['待审核'],
          subject,
        });
        if (errCode) {
          message.warning(`获取待审试题列表失败 ${msg}`);
          return;
        }
        setTotal(data?.total || 0);
        setDataSource(
          data.list?.map((item, index) => ({
            ...item,
            key: item.key || item.id || `item-${index}`,
            expanded: false,
          })) || [],
        );
      } finally {
        setLoading(false);
      }
    };

    /** 单个同意 */
    const auditQuestion = async ({
      data,
      checkStatus,
      isSingle,
    }: {
      data: any;
      checkStatus?: QuestionCheckStatus;
      isSingle?: boolean;
    }) => {
      let singleSubmitData = {};
      setLoading(true);
      if (isSingle) {
        const {
          _id,
          type,
          difficulty,
          author,
          commitAt,
          subject,
          gradeSection,
          rejectReason,
        } = data || {};

        singleSubmitData = {
          questions: [
            {
              questionBankId: _id,
              type,
              difficulty,
              author,
              commitAt: commitAt ?? new Date(),
            },
          ],
          checkStatus: checkStatus ?? QuestionCheckStatus['已通过'],
          suggestion:
            (checkStatus ?? QuestionCheckStatus['已通过']) === '已拒绝'
              ? rejectReason
              : undefined,
          checkerId: initialState?.id,
          checker: initialState?.nickname,
          enterpriseCode: initialState?.enterprise?.code,
          gradeSectionCode: gradeSection?.code,
          gradeSectionName: gradeSection?.name,
          subjectId: subject?.id,
          subjectName: subject?.name,
        };
      } else {
        singleSubmitData = data;
      }
      const { errCode, msg } = await bulkCheck(singleSubmitData);
      if (errCode) {
        setLoading(false);
        message.warning(`审核试题失败 ${msg}`);
        return;
      }
      setLoading(false);
      message.success('审核成功');
      getUnAuditedQuestionList();
    };

    /** 单个拒绝 */
    const rejectQuestion = async (data: any) => {
      let localRejectReason = '';
      Modal.confirm({
        title: '拒绝原因',
        content: (
          <>
            <div>请输入拒绝原因：</div>
            <TextArea
              rows={4}
              maxLength={200}
              placeholder="最多可输入200字"
              onChange={(e) => {
                localRejectReason = e.target.value.trim();
              }}
            />
          </>
        ),
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          if (localRejectReason.length === 0) {
            message.warning('拒绝原因不能为空');
            return Promise.reject();
          }
          data.rejectReason = localRejectReason;
          await auditQuestion({
            data,
            checkStatus: QuestionCheckStatus['已拒绝'],
            isSingle: true,
          });
        },
      });
    };

    // 批量同意
    const batchApprove = async () => {
      if (selectedIds.length === 0) {
        message.warning('请至少选择一道试题');
        return;
      }
      setLoading(true);
      try {
        const selectedItems = dataSource.filter((item) =>
          selectedIds.includes(item._id),
        );
        const { gradeSection, subject } = selectedItems?.[0] || {};
        const questions = selectedItems.map((item) => {
          const { _id, type, difficulty, author, commitAt } = item || {};
          return {
            questionBankId: _id,
            type,
            difficulty,
            author,
            commitAt: commitAt ?? new Date(),
          };
        });
        const newData = {
          questions,
          checkStatus: QuestionCheckStatus['已通过'],
          suggestion: undefined,
          checkerId: initialState?.id,
          checker: initialState?.nickname,
          enterpriseCode: initialState?.enterprise?.code,
          gradeSectionCode: gradeSection?.code,
          gradeSectionName: gradeSection?.name,
          subjectId: subject?.id,
          subjectName: subject?.name,
        };
        await auditQuestion({
          data: newData,
          isSingle: false,
        });
        setSelectedIds([]);
      } finally {
        setLoading(false);
      }
    };

    /** 批量拒绝 */
    const batchReject = async () => {
      if (selectedIds.length === 0) {
        message.warning('请至少选择一道试题');
        return;
      }
      let localRejectReason = '';
      Modal.confirm({
        title: '批量拒绝原因',
        content: (
          <>
            <div>请输入拒绝原因（将应用于所有选中试题）：</div>
            <TextArea
              rows={4}
              maxLength={200}
              placeholder="最多可输入200字"
              onChange={(e) => {
                localRejectReason = e.target.value.trim();
              }}
            />
          </>
        ),
        onOk: async () => {
          const selectedItems = dataSource.filter((item) =>
            selectedIds.includes(item._id),
          );
          const { gradeSection, subject } = selectedItems?.[0] || {};
          const questions = selectedItems.map((item) => {
            const { _id, type, difficulty, author, commitAt } = item || {};
            return {
              questionBankId: _id,
              type,
              difficulty,
              author,
              commitAt: commitAt ?? new Date(),
            };
          });
          const newData = {
            questions,
            checkStatus: QuestionCheckStatus['已拒绝'],
            suggestion: localRejectReason,
            checkerId: initialState?.id,
            checker: initialState?.nickname,
            enterpriseCode: initialState?.enterprise?.code,
            gradeSectionCode: gradeSection?.code,
            gradeSectionName: gradeSection?.name,
            subjectId: subject?.id,
            subjectName: subject?.name,
          };
          await auditQuestion({
            data: newData,
            isSingle: false,
          });
          setSelectedIds([]);
        },
      });
    };

    const showDiffModal = (item: any) => {
      setCurrentQuestion(item);
      setDiffVisible(true);
    };

    useEffect(() => {
      if (subject && pagination) {
        getUnAuditedQuestionList();
      }
    }, [subject, pagination]);

    return (
      <>
        {/* <StickyBox offsetTop={148} offsetBottom={20} style={{ zIndex: 1 }}> */}
        <header className={styles.options}>
          <div>待审核题目数量：{total}</div>
          <ConditionalRender
            hasAccess={isAdmin}
            accessComponent={
              <Space>
                <Checkbox
                  indeterminate={
                    selectedIds.length > 0 &&
                    selectedIds.length < dataSource.length
                  }
                  checked={
                    selectedIds.length === dataSource.length &&
                    dataSource.length > 0
                  }
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedIds(dataSource.map((item) => item._id));
                    } else {
                      setSelectedIds([]);
                    }
                  }}
                >
                  全选
                </Checkbox>
                <Button
                  color="green"
                  variant="solid"
                  icon={<CheckOutlined />}
                  onClick={batchApprove}
                >
                  批量允许 ({selectedIds.length})
                </Button>
                <Button danger icon={<CloseOutlined />} onClick={batchReject}>
                  批量拒绝 ({selectedIds.length})
                </Button>
              </Space>
            }
          />
        </header>
        {/* </StickyBox> */}
        <Spin spinning={loading} tip="数据加载中...">
          <div className={styles.wapper}>
            <ConditionalRender
              hasAccess={dataSource.length !== 0}
              accessComponent={
                <>
                  {dataSource.map((item) => {
                    return (
                      <div
                        className={styles.topicContent}
                        style={{
                          maxHeight: item.expanded ? 'none' : '280px',
                        }}
                        key={item.key}
                      >
                        <div className={styles.header}>
                          <Space size="large">
                            <span>
                              <Checkbox
                                checked={selectedIds.includes(item._id)}
                                onChange={(e) => {
                                  setSelectedIds((prev) =>
                                    e.target.checked
                                      ? [...prev, item._id]
                                      : prev.filter((id) => id !== item._id),
                                  );
                                }}
                              />
                              <Tag
                                color="blue"
                                style={{
                                  marginLeft: 8,
                                }}
                              >
                                {item?.checkStatus}
                              </Tag>
                            </span>
                            <span>类型：{item?.type?.name ?? '-'}</span>
                            <span>难度：{item?.difficulty?.name ?? '-'}</span>
                            <span>年级：{item?.grade?.name ?? '-'}</span>
                            <span>
                              版本：{item?.textbookVersion?.name ?? '-'}
                            </span>
                            <span>课时：{item?.catalog?.name}</span>
                            <ConditionalRender
                              hasAccess={item?.duration}
                              accessComponent={
                                <div>时长：{item?.duration} 分钟</div>
                              }
                            />
                          </Space>
                          <ConditionalRender
                            hasAccess={isAdmin}
                            accessComponent={
                              <Space>
                                <Popover
                                  placement="topLeft"
                                  title="系统检测到试题内容有修改（与学校题库版本不一致），为确保题库内容准确，请确认是否需要重新提交？"
                                >
                                  {item.commitAt &&
                                    dayjs(item?.updatedAt).isAfter(
                                      dayjs(item?.commitAt),
                                    ) && (
                                      <span
                                        onClick={() => {
                                          showDiffModal(item);
                                        }}
                                        style={{
                                          color: '#722ed1',
                                          cursor: 'pointer',
                                        }}
                                      >
                                        <DiffOutlined /> 查看修改
                                      </span>
                                    )}
                                </Popover>
                                <Button
                                  size="small"
                                  color="green"
                                  variant="solid"
                                  icon={<CheckOutlined />}
                                  onClick={() =>
                                    auditQuestion({
                                      data: item,
                                      checkStatus:
                                        QuestionCheckStatus['已通过'],
                                      isSingle: true,
                                    })
                                  }
                                >
                                  允许入库
                                </Button>
                                <Button
                                  size="small"
                                  danger
                                  icon={<CloseOutlined />}
                                  onClick={() => rejectQuestion(item)}
                                >
                                  拒绝入库
                                </Button>
                              </Space>
                            }
                          />
                        </div>
                        <TopicContent info={item} allShowAnalysis />
                        {item.expanded ? (
                          <div style={{ textAlign: 'center', marginTop: 8 }}>
                            <Button
                              type="link"
                              onClick={(e) => {
                                e.stopPropagation();
                                setDataSource((prev) =>
                                  prev.map((i) => ({
                                    ...i,
                                    expanded:
                                      i.key === item.key ? false : i.expanded,
                                  })),
                                );
                              }}
                            >
                              收起当前试题
                            </Button>
                          </div>
                        ) : (
                          <div className={styles.footer}>
                            <Button
                              type="link"
                              onClick={(e) => {
                                e.stopPropagation();
                                setDataSource((prev) =>
                                  prev.map((i) => ({
                                    ...i,
                                    expanded:
                                      i.key === item.key ? true : i.expanded,
                                  })),
                                );
                              }}
                            >
                              展开当前试题
                            </Button>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </>
              }
              noAccessComponent={
                <Empty
                  style={{
                    margin: '30px',
                  }}
                  description="暂无内容"
                />
              }
            />

            <ConditionalRender
              hasAccess={dataSource.length !== 0}
              accessComponent={
                <Pagination
                  align="center"
                  current={pagination?.current}
                  pageSize={pagination?.pageSize}
                  total={total}
                  onChange={(page, pageSize) => {
                    setPagination({
                      current: page,
                      pageSize,
                    });
                  }}
                  showSizeChanger
                  showQuickJumper
                />
              }
            />
          </div>
        </Spin>
        <DiffModal
          diffVisible={diffVisible}
          onCancel={() => {
            setDiffVisible(false);
          }}
          currentQuestion={currentQuestion}
        />
      </>
    );
  },
);

export default UnAudited;
