import AnswerSheet from '@/components/AnswerSheet';
import CommonCard from '@/components/CommonCard';
import HomeworkDesign from '@/components/HomeworkDesign';
import AnalysisPaper from '@/components/HomeworkDesign/PaperEditPage/AnalysisPaper';
import TitleBar from '@/components/TitleBar';
import { classWorkQuestionIndex } from '@/services/class_work_question';
import { classWorkStructIndex } from '@/services/class_work_struct';
import { downPaperToWord } from '@/services/htmlToWord';
import { LeftOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons';
import { Button, Col, Row, Spin, message } from 'antd';
import classNames from 'classnames';
import { nanoid } from 'nanoid';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import styles from './index.less';

const HomeworkTemplateEdit = ({
  curHomework,
  setIsCheck,
}: {
  curHomework: any;
  setIsCheck: Dispatch<SetStateAction<boolean>>;
}) => {
  const { id: homeworkId, name: homeworkName } = curHomework || {};
  const [listLoading, setListLoading] = useState<boolean>(false);
  const [isWordDownloading, setIsWordDownloading] = useState<boolean>(false);
  const [questionData, setQuestionData] = useState<API.SystemQuestion[]>([]);
  const [strucData, setStrucData] = useState<any>();
  /**
   * 获取作业详情
   *
   * @param homeworkId 作业ID
   */
  const getHomeworkDetail = async (homeworkId: any) => {
    setListLoading(true);
    const { errCode, data, msg } = await classWorkQuestionIndex({
      classworkDetail_id: homeworkId,
    });
    if (errCode) {
      message.error('试卷内容获取失败，请联系管理员或稍后再试！' + msg);
      return;
    }
    setQuestionData(data);
    const {
      errCode: errCode1,
      data: data1,
      msg: msg1,
    } = await classWorkStructIndex({
      classworkDetail_id: homeworkId,
    });
    if (errCode1) {
      message.error('试卷结构获取失败，请联系管理员或稍后再试！' + msg1);
      return;
    }
    let newData = data1?.list || [];
    if (newData?.length === 0 && data?.length > 0) {
      const hideId = 'hidden_' + nanoid();
      newData = {
        bigQuestionOrder: [hideId],
        bigQuestions: {},
      };
      newData.bigQuestions[hideId] = {
        id: hideId,
        name: '暂无分组',
        questionIds: data.map((item: any) => item?._id || null),
      };
    } else {
      const bigQuestionOrder = newData?.map((v: any) => v.id);
      const bigQuestions = newData?.reduce((acc: any, item: any) => {
        acc[item.id] = item;
        return acc;
      }, {});
      newData = {
        bigQuestionOrder,
        bigQuestions,
      };
    }
    setStrucData(newData);
    setListLoading(false);
  };
  const handleDownloadWord = async () => {
    // 获取所有questionIds并按顺序排序
    const sortedQuestionIds = strucData.bigQuestionOrder.flatMap(
      (bigQuestionId: string) =>
        strucData.bigQuestions[bigQuestionId].questionIds,
    );

    // 优化1：提前创建Map提高性能
    const questionMap = new Map(
      questionData.map((item: any) => [item._id, item]),
    );

    // 优化2：使用filter(Boolean)过滤掉undefined值
    const sortedQuestions = sortedQuestionIds
      .map((id: string) => questionMap.get(id))
      .filter(Boolean);
    const questions = sortedQuestions.map((item: any) => {
      return {
        question_id: item._id,
        source_table: item.source_table || item.tableName,
      };
    });
    const { bigQuestions, bigQuestionOrder } = strucData;
    const data = bigQuestionOrder?.map((key: string) => bigQuestions?.[key]);
    try {
      setIsWordDownloading(true);
      const {
        errCode,
        data: responseData,
        msg,
      } = await downPaperToWord({
        name: homeworkName,
        struct: data,
        questions: questions,
        options: {
          fontSize: 14,
          fontFamily:
            'Microsoft YaHei, Helvetica Neue, PingFang SC, sans-serif',
          lineHeight: 1.5,
        },
      });
      if (errCode) {
        setIsWordDownloading(false);
        return message.error('下载失败，请联系管理员后重试！' + msg);
      }
      const uint8Array = new Uint8Array(responseData.data);
      // 假设response.data是Buffer数据
      const blob = new Blob([uint8Array], { type: 'application/msword' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `${homeworkName}.doc`;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error('下载失败:', error);
    } finally {
      setIsWordDownloading(false);
    }
  };
  useEffect(() => {
    if (homeworkId) {
      getHomeworkDetail(homeworkId);
    }
  }, [homeworkId]);
  return (
    <div className={styles.homeworkTemplate}>
      <CommonCard
        activeTitle
        title={
          <div className={styles.templateHeader}>
            <Button
              type="link"
              icon={<LeftOutlined />}
              onClick={() => setIsCheck(false)}
            >
              返回列表
            </Button>
          </div>
        }
        centerDom={homeworkName}
      >
        <Button
          icon={<VerticalAlignBottomOutlined />}
          onClick={handleDownloadWord}
          loading={isWordDownloading}
        >
          下载试卷
        </Button>
        <AnswerSheet
          paperName={homeworkName}
          strucData={strucData}
          questionData={questionData}
        />
        {/* {status === '草稿' && (
          <Link
            to={`${pathname}/edit?ddtab=true&homeworkId=${homeworkId}`}
            target="_blank"
            type="text"
          >
            <Button color="primary" variant="outlined" icon={<EditOutlined />}>
              编辑试卷
            </Button>
          </Link>
        )} */}
        {/* <Button icon={<ContainerOutlined />}>制作答题卡</Button>
        <Button icon={<FileSyncOutlined />}>版本对比</Button> */}
      </CommonCard>
      <div className={styles.container}>
        <Spin tip="数据加载中..." spinning={listLoading}>
          <Row gutter={[16, 16]}>
            <Col flex="360px">
              <div className={classNames('commonWapper', styles.leftWrapper)}>
                <TitleBar
                  title="试卷分析"
                  style={{
                    padding: '0 0 14px',
                    borderRadius: 0,
                    borderBottom: '1px solid #dadada',
                  }}
                />
                <AnalysisPaper type="analysis" questionList={questionData} />
              </div>
            </Col>
            <Col flex="1">
              <div className={classNames('commonWapper', styles.centerWrapper)}>
                <HomeworkDesign
                  type="check"
                  questionConstrue={strucData}
                  questionList={questionData}
                  sortQuestion={setStrucData}
                  replaceQuestion={setQuestionData}
                />
              </div>
            </Col>
          </Row>
        </Spin>
      </div>
    </div>
  );
};
export default HomeworkTemplateEdit;
