import { DictionarieState } from '@/models/dictionarie';
import { connect } from '@umijs/max';
import { Select } from 'antd';
import React, { useEffect } from 'react';
import styles from './index.less';

interface QuestionBankSourceProps {
  dictionarie?: DictionarieState;
  defaultValue?: string;
  onChange?: (value: any) => void;
}

type OptionsType = {
  label: string;
  value: string;
};
const QuestionBankSource: React.FC<QuestionBankSourceProps> = ({
  dictionarie,
  onChange,
  defaultValue,
}) => {
  const [selectedCode, setSelectedCode] = React.useState<string>(
    defaultValue || 'all',
  );
  const [options, setOptions] = React.useState<OptionsType[]>([]);
  useEffect(() => {
    if (onChange) {
      const selectInfo = options.find(
        (item: { value: string }) => item.value === selectedCode,
      );
      if (!selectInfo || selectInfo.value === 'all') {
        return onChange(null);
      }
      onChange(selectInfo);
    }
  }, [selectedCode]);
  useEffect(() => {
    if (
      !dictionarie ||
      !Array.isArray(dictionarie.list) ||
      dictionarie.list.length === 0
    )
      return;

    const options = dictionarie.list
      .filter((item: { type: string }) => item.type === 'label')
      .map((item: { name: string; code: string }) => {
        return {
          label: item.name,
          value: item.code,
        };
      });
    setOptions([
      {
        label: '全部',
        value: 'all',
      },
      ...options,
    ]);
    return;
  }, [dictionarie]);
  return (
    <div className={styles.wapper}>
      <label>版本：</label>
      <div className={styles.selectWapper}>
        <Select
          placeholder="请选择版本"
          className={styles.select}
          onChange={setSelectedCode}
          defaultValue={defaultValue}
          options={options}
        />
      </div>
    </div>
  );
};
export default connect(({ dictionarie }) => ({
  dictionarie,
}))(QuestionBankSource);
