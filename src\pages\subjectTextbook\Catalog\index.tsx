import CatalogTree from '@/components/CatalogTree';
import CommonSelect from '@/components/CommonSelect';
import CustomCollapse from '@/components/CustomCollapse';
import React, { useState } from 'react';

const Catalog: React.FC = () => {
  // 当前选中的教材
  const [currentTextbookChecklist, setCurrentTextbookChecklist] =
    useState<API.TextbookChecklist>();
  const [title, setTitle] = useState<string>('选择教材');

  const onSelectVolume = async (info: {
    section?: API.Dictionarie;
    subject?: API.Subject;
    textbook?: API.Textbook;
    grade?: API.Dictionarie;
    volume?: API.TextbookChecklist;
  }) => {
    const { section, subject, textbook, grade, volume } = info;
    if (section && subject && textbook && grade && volume) {
      setTitle(
        `${section.name} / ${subject.subject} / ${textbook.textbook_version} / ${grade.name} / ${volume.volume}`,
      );
      setCurrentTextbookChecklist(volume);
    } else {
      setTitle('选择教材');
      setCurrentTextbookChecklist(undefined);
    }
  };

  return (
    <>
      <CustomCollapse
        items={[
          {
            key: '1',
            label: title,
            children: <CommonSelect level="volume" onChange={onSelectVolume} />,
          },
        ]}
        defaultActiveKey={['1']}
        style={{ marginBottom: 16 }}
      />
      <CatalogTree currentTextbookChecklist={currentTextbookChecklist} />
    </>
  );
};

export default Catalog;
