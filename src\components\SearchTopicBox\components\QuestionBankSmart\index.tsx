import { index as knowledgePointListAPI } from '@/services/knowledge_point';
import { convertToTree } from '@/utils/calc';
import { message, TreeSelect } from 'antd';
import React, { useEffect } from 'react';
import styles from './index.less';

interface QuestionBankSmartProps {
  onChange?: (value: string) => void;
  subjectId?: number;
}

const QuestionBankSmart: React.FC<QuestionBankSmartProps> = ({
  onChange,
  subjectId,
}) => {
  /** 知识点列表 */
  const [knowledgePointList, setKnowledgePointList] = React.useState<any[]>([]);

  /** 获取知识点 */
  const getKnowledgePointList = async (subjectId: number) => {
    const { errCode, data, msg } = await knowledgePointListAPI({
      subjectId,
    });
    if (errCode) {
      message.warning(`获取知识点失败，请稍后重试 ${msg}`);
      setKnowledgePointList([]);
    } else {
      setKnowledgePointList(convertToTree(data.list ?? []));
    }
  };

  useEffect(() => {
    if (!subjectId) return;
    getKnowledgePointList(subjectId);
  }, [subjectId]);
  return (
    <div className={styles.wapper}>
      <label>知识点：</label>
      <div className={styles.selectWapper}>
        <TreeSelect
          multiple
          showSearch
          allowClear
          treeNodeFilterProp="title"
          filterTreeNode={(input, node) =>
            typeof node?.title === 'string' &&
            node.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
          treeData={knowledgePointList}
          className={styles.select}
          placeholder="请选择知识点"
          onChange={onChange}
        />
      </div>
    </div>
  );
};
export default QuestionBankSmart;
