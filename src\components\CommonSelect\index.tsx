/*
 * @Description: 公共数据选择组件，如学段、年级、学科、版材，支持回填，支持指定选择的层级
 * @Date: 2025-02-22 16:19:28
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-21 16:02:19
 */
import { connect } from '@umijs/max';
import { ColProps } from 'antd';
import React, { useEffect, useState } from 'react';
import Grade from './Grade';
import GradeSection from './GradeSection';
import Subject from './Subject';
import Textbook from './Textbook';
import Volume from './Volume';

export type CommonSelectLevel =
  | 'section'
  | 'subject'
  | 'textbook'
  | 'grade'
  | 'volume';

const CommonSelect: React.FC<{
  /** 选择器显示级别，默认为grade */
  level?: CommonSelectLevel;
  /** 默认选中的学段 */
  sectionCode?: string;
  /** 默认选中的年级，若与学段不匹配，则不会选中 */
  gradeCode?: string;
  /** 默认选中的科目，若与年级不匹配，则不会选中 */
  subjectId?: number;
  /** 默认选中的教材，若与科目不匹配，则不会选中 */
  textbookId?: number;
  /** 默认选中的册次，若与教材不匹配，则不会选中 */
  volumeId?: number;
  /** 组件选择回调 */
  onChange?: (info: {
    section?: API.Dictionarie;
    subject?: API.Subject;
    textbook?: API.Textbook;
    grade?: API.Dictionarie;
    volume?: API.TextbookChecklist;
  }) => void;
  /** 标题列属性 */
  labelCol?: ColProps;
  /** 自定义学段行样式 */
  sectionRowStyles?: React.CSSProperties;
  /** 自定义行样式 */
  rowStyles?: React.CSSProperties;
}> = ({
  level = 'section',
  sectionCode,
  gradeCode,
  subjectId,
  textbookId,
  volumeId,
  onChange,
  labelCol,
  sectionRowStyles,
  rowStyles,
}) => {
  const [currentSection, setCurrentSection] = useState<API.Dictionarie>();
  const [currentSubject, setCurrentSubject] = useState<API.Subject>();
  const [currentTextbook, setCurrentTextbook] = useState<API.Textbook>();
  const [currentGrade, setCurrentGrade] = useState<API.Dictionarie>();
  const [currentVolume, setCurrentVolume] = useState<API.TextbookChecklist>();

  // 触发外部回调，分开监听防止过多触发回调造成性能浪费
  useEffect(() => {
    if (level === 'section') {
      onChange?.({ section: currentSection });
    }
  }, [currentSection]);
  useEffect(() => {
    if (level === 'subject') {
      onChange?.({
        section: currentSection,
        subject: currentSubject,
      });
    }
  }, [currentSubject]);
  useEffect(() => {
    if (level === 'textbook') {
      onChange?.({
        section: currentSection,
        subject: currentSubject,
        textbook: currentTextbook,
      });
    }
  }, [currentTextbook]);
  useEffect(() => {
    if (level === 'grade') {
      onChange?.({
        section: currentSection,
        subject: currentSubject,
        textbook: currentTextbook,
        grade: currentGrade,
      });
    }
    // 年级只会跟着学段更新，所以这里要多监听一下教材
  }, [currentTextbook, currentGrade]);
  useEffect(() => {
    if (level === 'volume') {
      onChange?.({
        section: currentSection,
        subject: currentSubject,
        textbook: currentTextbook,
        grade: currentGrade,
        volume: currentVolume,
      });
    }
  }, [currentVolume]);

  return (
    <>
      <GradeSection
        initalValue={sectionCode}
        onChange={setCurrentSection}
        rowStyles={sectionRowStyles || rowStyles}
        labelCol={labelCol}
      />
      {['subject', 'textbook', 'grade', 'volume'].includes(level) && (
        <Subject
          initalValue={subjectId}
          onChange={setCurrentSubject}
          sectionCode={currentSection?.code}
          rowStyles={rowStyles}
          labelCol={labelCol}
        />
      )}
      {['textbook', 'grade', 'volume'].includes(level) && (
        <Textbook
          initalValue={textbookId}
          onChange={setCurrentTextbook}
          subjectId={currentSubject?.id}
          rowStyles={rowStyles}
          labelCol={labelCol}
        />
      )}
      {['grade', 'volume'].includes(level) && (
        <Grade
          initalValue={gradeCode}
          onChange={setCurrentGrade}
          sectionCode={currentSection?.code}
          rowStyles={rowStyles}
          labelCol={labelCol}
        />
      )}
      {['volume'].includes(level) && (
        <Volume
          initalValue={volumeId}
          onChange={setCurrentVolume}
          textbookId={currentTextbook?.id}
          grade={currentGrade?.code}
          rowStyles={rowStyles}
          labelCol={labelCol}
        />
      )}
    </>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(CommonSelect);
