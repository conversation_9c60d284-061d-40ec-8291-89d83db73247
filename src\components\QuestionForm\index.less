.loadingWrap {
  position: absolute;
  left: 50%;
  top: 150%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: translate(-50%, -50%);
  color: #2979ff;

  .loading {
    width: 30px;
    height: 30px;
    border: 2px solid #2979ff;
    border-top-color: rgba(0, 0, 0, 20%);
    border-right-color: rgba(0, 0, 0, 20%);
    border-bottom-color: rgba(0, 0, 0, 20%);
    border-radius: 100%;
    animation: circle infinite 0.75s linear;
    margin-bottom: 10px;
  }

  @keyframes circle {
    0% {
      transform: rotate(0);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}
