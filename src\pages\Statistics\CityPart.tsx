import {
  AssignmentsTotal,
  DifficultyAnalysis,
  DurationAnalysis,
  QuantitativeAnalysis,
  QuestionsTotalTrend,
  ShareSchoolRankings,
  TrendHomeworkQuantity,
} from './components/Chart';
import StatisticsContext from './components/Context';

const CityPart = ({ params }: { params: any }) => {
  return (
    <StatisticsContext.Provider value={params}>
      <AssignmentsTotal
        span={16}
        type="city"
        title={'各区县分科目作业数量分析'}
      />
      <ShareSchoolRankings type="city" title={'范本共享学校排名'} span={8} />
      <TrendHomeworkQuantity
        type="city"
        title={'全市月度作业数量趋势'}
        span={24}
      />
      <DurationAnalysis
        type="city"
        title={'各区县分科目作业平均时长分析'}
        span={24}
      />
      <QuantitativeAnalysis
        type="city"
        title={'各区县分科目试题数量分析'}
        span={12}
      />
      <DifficultyAnalysis type="city" title={'全市试题难度分析'} span={12} />
      <QuestionsTotalTrend type="city" title={'全市月度试题量趋势'} span={24} />
    </StatisticsContext.Provider>
  );
};
export default CityPart;
