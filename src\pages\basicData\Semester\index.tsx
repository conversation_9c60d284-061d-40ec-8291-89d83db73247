import { SemesterState } from '@/models/semester';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { AnyAction, connect, Dispatch } from '@umijs/max';
import { <PERSON><PERSON>, Popconfirm, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import EditModal from './EditModal';

const Semester: React.FC<{
  semester: SemesterState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ semester, dispatch, loading }) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Semester | undefined>(undefined);

  const handleSave = async (values: Partial<API.Semester>) => {
    if (current) {
      const { id, ...info } = values;
      dispatch({
        type: 'semester/update',
        payload: {
          id,
          info,
        },
      });
    } else {
      dispatch({
        type: 'semester/add',
        payload: values,
      });
    }
  };

  const handleDel = async (record: API.Semester) => {
    const { id } = record;
    dispatch({
      type: 'semester/remove',
      payload: {
        id,
      },
    });
  };

  const columns: ProColumns<API.Semester, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '学年',
      dataIndex: 'year',
      key: 'year',
      align: 'center',
    },
    {
      title: '学期',
      dataIndex: 'term',
      key: 'term',
      align: 'center',
      render: (text) => {
        switch (text) {
          case 1:
          case '1':
            return '第一学期';
          case 2:
          case '2':
            return '第二学期';
          default:
            return '-';
        }
      },
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
      copyable: true,
    },
    {
      title: '开始日期',
      dataIndex: 'start_date',
      key: 'start_date',
      valueType: 'date',
      align: 'center',
    },
    {
      title: '结束日期',
      dataIndex: 'end_date',
      key: 'end_date',
      valueType: 'date',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        0: {
          text: '未激活',
          status: 'Default',
        },
        1: {
          text: '已激活',
          status: 'Success',
        },
      },
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          {!record.status && (
            <Button
              type="link"
              onClick={() => {
                handleSave({ id: record.id, status: 1 });
              }}
            >
              激活
            </Button>
          )}
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          {!record.status && (
            <Popconfirm
              title="确认删除？"
              onConfirm={() => {
                handleDel(record);
              }}
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setModalVisible(false);
  }, [semester]);

  return (
    <>
      <ProTable<API.Semester>
        actionRef={actionRef}
        rowKey="id"
        headerTitle="学年学期"
        columns={columns}
        loading={loading.models.semester}
        dataSource={semester.currentList}
        onChange={(_pagination, filters, sorter) => {
          dispatch({
            type: 'semester/onChange',
            payload: {
              filters,
              sorter,
            },
          });
        }}
        beforeSearchSubmit={(params) => {
          dispatch({
            type: 'semester/onSearch',
            payload: params,
          });
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
        options={false}
        search={false}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ semester, loading }) => ({ semester, loading }))(
  Semester,
);
