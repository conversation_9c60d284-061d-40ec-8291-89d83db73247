import { index } from '@/services/city_homework_case';
import { Flex, message, Tag, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
const { Title } = Typography;

const Case: React.FC = () => {
  const [curSection, setCurSection] = useState<any>();
  const getData = async () => {
    const { errCode, msg, data } = await index({});
    if (errCode) {
      message.error(msg || '培养目标列表查询失败');
    }
    setCurSection(data?.list);
  };
  useEffect(() => {
    getData();
  }, []);
  return (
    <div className="commonWapper">
      <div className={styles.caseWrapper}>
        {curSection?.map((item: any) => {
          return (
            <div key={item.id}>
              <Title level={5}>
                {item.case_name}
                <Tag
                  style={{
                    marginLeft: '10px',
                  }}
                >
                  {item.grade_section_name}
                  {item.subject_name}
                </Tag>
                {item.case_type && <Tag color="blue">{item.case_type}</Tag>}
              </Title>
              <div className={styles.caseContent}>
                <Flex>
                  <div
                    style={{
                      width: '80px',
                    }}
                  >
                    案例内容
                  </div>
                  <div
                    style={{
                      flex: 1,
                    }}
                    dangerouslySetInnerHTML={{ __html: item.case_example }}
                  ></div>
                </Flex>
                <Flex align="baseline">
                  <div
                    style={{
                      width: '80px',
                    }}
                  >
                    案例分析
                  </div>
                  <div
                    style={{
                      flex: 1,
                    }}
                    dangerouslySetInnerHTML={{ __html: item.case_analysis }}
                  ></div>
                </Flex>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Case;
