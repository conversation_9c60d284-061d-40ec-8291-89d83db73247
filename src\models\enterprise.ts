import { enterprises } from '@/services';
import {
  OnChangePayload,
  onProTableChange,
  onProTableSearch,
} from '@/utils/calc';
import { message } from 'antd';

export type EnterpriseState = {
  total: number;
  list: API.Enterprise[];
  currentList: API.Enterprise[];
  inited: boolean;
};

export default {
  state: {
    total: 0,
    list: [],
    currentList: [],
    inited: false,
  } as EnterpriseState,

  effects: {
    /** 查询列表 **/
    *queryList(
      { payload }: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(enterprises.index, payload);
      if (errCode) {
        message.error(msg || '单位列表查询失败');
      } else {
        yield put({ type: 'querySuccess', payload: data });
      }
    },
    *add(
      { payload }: { payload: API.Enterprise },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(enterprises.create, payload);
      if (errCode) {
        message.error(msg || '单位创建失败');
      } else {
        message.success('单位创建成功');
        yield put({ type: 'addSuccess', payload: data });
      }
    },
    *update(
      { payload }: { payload: { id: number; info: Partial<API.Enterprise> } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(
        enterprises.update,
        payload.id,
        payload.info,
      );
      if (errCode) {
        message.error(msg || '单位更新失败');
      } else {
        message.success('单位更新成功');
        yield put({ type: 'updateSuccess', payload });
      }
    },
    *remove(
      { payload }: { payload: { id: number } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(enterprises.remove, payload.id);
      if (errCode) {
        message.error(msg || '单位删除失败');
      } else {
        message.success('单位删除成功');
        yield put({ type: 'removeSuccess', payload });
      }
    },
  },

  reducers: {
    /** 查询列表成功后更新数据，触发渲染 */
    querySuccess(
      _state: EnterpriseState,
      { payload }: { payload: EnterpriseState },
    ) {
      return { ...payload, currentList: payload.list, inited: true };
    },
    /** proTable组件的过滤和排序操作，分页不用处理，组件自己会完成 */
    onChange(
      state: EnterpriseState,
      { payload }: { payload: OnChangePayload<API.Enterprise> },
    ) {
      const filteredList = onProTableChange(state.list, payload);
      return {
        ...state,
        total: filteredList.length,
        currentList: filteredList,
      };
    },
    /** proTable组件的查询操作 */
    onSearch(
      state: EnterpriseState,
      { payload }: { payload: Partial<API.Enterprise> },
    ) {
      const newList = onProTableSearch(state.list, payload);
      return {
        ...state,
        total: newList.length,
        currentList: newList,
      };
    },
    addSuccess(
      state: EnterpriseState,
      { payload }: { payload: API.Enterprise },
    ) {
      const newList = [...state.list, payload].sort((a, b) =>
        a.code.localeCompare(b.code),
      );
      const currentList = [...state.currentList, payload].sort((a, b) =>
        a.code.localeCompare(b.code),
      );
      return {
        ...state,
        total: newList.length,
        list: newList,
        currentList,
      };
    },
    updateSuccess(
      state: EnterpriseState,
      { payload }: { payload: { id: number; info: Partial<API.Enterprise> } },
    ) {
      const newList = state.list
        .map((item) => {
          if (item.id === payload.id) {
            return { ...item, ...payload.info };
          }
          return item;
        })
        .sort((a, b) => a.code.localeCompare(b.code));
      const currentList = state.currentList.map((item) => {
        if (item.id === payload.id) {
          return { ...item, ...payload.info };
        }
        return item;
      });
      return {
        ...state,
        list: newList,
        currentList,
      };
    },
    removeSuccess(
      state: EnterpriseState,
      { payload }: { payload: { id: number } },
    ) {
      const newList = state.list.filter((item) => item.id !== payload.id);
      const currentList = state.currentList.filter(
        (item) => item.id !== payload.id,
      );
      return {
        ...state,
        list: newList,
        currentList,
      };
    },
  },
};
