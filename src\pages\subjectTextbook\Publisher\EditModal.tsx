import {
  ModalForm,
  ProFormGroup,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Publisher;
  onSave: (info: API.Publisher) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<API.Publisher>
      title={info ? '编辑出版单位' : '注册出版单位'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormGroup>
        <ProFormText
          name="name"
          label="名称"
          rules={[{ required: true, message: '请输入名称！' }]}
          colProps={{ span: 12 }}
        />
      </ProFormGroup>
      <ProFormText name="address" label="地址" />
      <ProFormText name="contact" label="联系人" colProps={{ span: 12 }} />
      <ProFormText name="phone" label="联系电话" colProps={{ span: 12 }} />
      <ProFormText name="nature" label="单位性质" colProps={{ span: 12 }} />
      <ProFormTextArea
        name="description"
        label="单位简介"
        fieldProps={{
          maxLength: 100,
          showCount: true,
        }}
      />
    </ModalForm>
  );
};

export default EditModal;
