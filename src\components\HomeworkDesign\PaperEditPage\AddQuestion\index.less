.customQuestionDrawer {
  :global {
    .ant-drawer-header {
      text-align: center;
    }

    .ant-drawer-body {
      padding: 0;
      background-color: #f1f1f1;
    }
  }

  .leftWrapper,
  .rightWrapper {
    background-color: #fff;
    width: calc(50vw - 12px);
    height: calc(100vh - 60px);
    padding: 0 16px;

    header {
      height: 45px;
      line-height: 45px;
      font-size: 16px;
      font-weight: bold;
      border-bottom: #f1f1f1 1px solid;
    }

    .selectedQuestion {
      position: relative;
      height: calc(100vh - 105px);
      overflow-y: auto;
    }
  }

  .questionList {
    height: calc(100vh - 125px);
    overflow-y: auto;

    div[class*='commonWapper'] {
      box-shadow: 0 0 8px 0 rgba(0, 0, 0, 10%);
      border-radius: 4px;
      border: #f1f1f1 1px solid;
    }
  }
}
