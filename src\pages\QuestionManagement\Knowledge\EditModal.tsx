import * as knowledgePointService from '@/services/knowledge_point';
import {
  ModalForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.KnowledgePoint;
  onSave: (info: API.KnowledgePoint) => Promise<void>;
  onClose: () => void;
  subjectId?: number;
  parentId?: number;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  subjectId,
  parentId = 0,
}) => {
  const [parentOptions, setParentOptions] = useState<
    { label: string; value: number }[]
  >([]);

  // 获取父级知识点选项
  useEffect(() => {
    const fetchParentOptions = async () => {
      if (!subjectId || !open) return;

      const res = await knowledgePointService.tree({ subjectId });
      if (res.errCode === 0 && res.data) {
        // 将树形结构扁平化为选项列表
        const options: { label: string; value: number }[] = [
          { label: '顶级知识点', value: 0 },
        ];
        const flattenTree = (nodes: API.KnowledgePoint[], prefix = '') => {
          nodes.forEach((node) => {
            // 编辑时排除自身及其子节点作为父节点选项
            if (!info || node.id !== info.id) {
              options.push({
                label: `${prefix}${node.name}`,
                value: node.id,
              });
              if (node.children) {
                flattenTree(node.children, `${prefix}${node.name} > `);
              }
            }
          });
        };
        flattenTree(res.data);
        setParentOptions(options);
      }
    };

    fetchParentOptions();
  }, [subjectId, open, info]);

  return (
    <ModalForm<API.KnowledgePoint>
      title={info ? '编辑知识点' : '新增知识点'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        maskClosable: false,
        width: 500,
      }}
      open={open}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
      onFinish={onSave}
      initialValues={{
        ...info,
        pid: info?.pid || parentId,
      }}
      submitter={{
        searchConfig: {
          submitText: '保存',
          resetText: '取消',
        },
      }}
    >
      <ProFormText name="id" hidden />
      <ProFormText
        name="name"
        label="知识点名称"
        rules={[{ required: true, message: '请输入知识点名称！' }]}
        placeholder="请输入知识点名称"
      />
      <ProFormSelect
        name="pid"
        label="父级知识点"
        options={parentOptions}
        placeholder="请选择父级知识点"
        disabled={!subjectId}
        help="不选择则默认为顶级知识点"
      />
    </ModalForm>
  );
};

export default EditModal;
