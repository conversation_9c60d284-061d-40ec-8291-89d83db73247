import { convertNumber } from '@/utils/calc';
import { SettingOutlined } from '@ant-design/icons';
import { ModalForm, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Empty, Form, InputNumber } from 'antd';
import { useEffect, useState } from 'react';

const ScoreSetting = ({
  selectedQuestion,
  setScore,
}: {
  selectedQuestion?: any;
  setScore: (data: any, score: any) => void;
}) => {
  const [form] = Form.useForm<any>();
  const [questionScore, setQuestionScore] = useState<any>();
  const [totalScore, setTotalScore] = useState<any>();
  /** 各个答题小题以table形式展现 */
  const columns: ProColumns<any>[] = [
    {
      title: '题序',
      dataIndex: 'index',
      valueType: 'index',
      width: 68,
      align: 'center',
    },
    {
      disable: true,
      title: '题型',
      dataIndex: 'typeName',
      key: 'typeName',
      align: 'center',
    },
    {
      title: '难度',
      dataIndex: 'difficultyName',
      key: 'difficultyName',
      align: 'center',
      width: 120,
    },
    {
      title: '分值',
      dataIndex: 'score',
      align: 'center',
      width: 80,
      render: (text, record) => {
        return (
          <InputNumber
            min={1}
            max={100}
            value={record.score}
            precision={1}
            onChange={(value) => {
              const id = record?.pId;

              setQuestionScore((prevData: any[]) => {
                return prevData.map((tar) => {
                  if (tar.id === id) {
                    let scoreTotal = 0;
                    const scoreArr = tar.children.map((val: any) => {
                      const isCurrent = val._id === record._id;
                      if (isCurrent) {
                        scoreTotal += value;
                        return {
                          ...val,
                          score: value,
                        };
                      } else {
                        scoreTotal += val.score;
                        return {
                          ...val,
                        };
                      }
                    });
                    return { ...tar, children: scoreArr, score: scoreTotal };
                  }
                  return tar;
                });
              });
            }}
          />
        );
      },
    },
  ];
  const convertPaperData = (structure: any, list: any) => {
    if (!structure || !list) return [];
    const layout = [];
    const isHidden = structure?.find(
      (v: { id: string }) => v?.id?.indexOf('hidden') !== -1,
    );

    for (let index = 0; index < structure.length; index++) {
      const { id, name, questionIds } = structure[index];
      let scoreTotal = 0;
      const groupedData = questionIds?.reduce((acc: any, item: string) => {
        const curQz = list.find((v: { _id?: string }) => v?._id === item);
        scoreTotal += curQz?.score || 0;
        acc.push({
          typeName: curQz?.type?.name,
          difficultyName: curQz?.difficulty?.name,
          score: curQz?.score,
          pId: id,
          _id: curQz?._id,
        });
        return acc;
      }, []);
      layout.push({
        id,
        name,
        score: scoreTotal,
        orderIndex: isHidden ? index - 1 : index,
        children: groupedData,
      });
    }
    return layout;
  };
  useEffect(() => {
    if (questionScore) {
      let scoreTotal = 0;
      questionScore?.forEach((item: { score: any }) => {
        scoreTotal += item.score || 0;
      });
      setTotalScore(scoreTotal);
    }
  }, [questionScore]);
  return (
    <ModalForm<any>
      title={
        <div>
          分值设置
          <span
            style={{
              paddingLeft: '16px',
              color: 'var(--tip-color)',
              fontWeight: 'normal',
            }}
          >
            （分值设置完成后需要保存试卷！）
          </span>
        </div>
      }
      trigger={<Button icon={<SettingOutlined />}>分值设置</Button>}
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        maskClosable: false,
        centered: true,
        onCancel: () => {
          setTotalScore(0);
          setQuestionScore(undefined);
        },
        styles: {
          body: {
            maxHeight: '70vh',
            overflowY: 'auto',
          },
        },
      }}
      onOpenChange={(visible) => {
        if (!visible) {
          setTotalScore(0);
          setQuestionScore(undefined);
        } else {
          const { strucData, questionData = [] } = selectedQuestion || {};
          if (questionData?.length) {
            const { bigQuestions, bigQuestionOrder = [] } = strucData || {};
            if (bigQuestions && bigQuestionOrder?.length > 0) {
              // 试题结构根据 order 数组的顺序重新排序
              const sortedArray = [
                ...bigQuestionOrder.map((key: string) => bigQuestions[key]),
              ];
              const partData = convertPaperData(sortedArray, questionData);

              setQuestionScore(partData);
            }
          } else {
            setQuestionScore([]);
          }
        }
      }}
      submitTimeout={2000}
      onFinish={async () => {
        const extractQuestionInfo = () => {
          return questionScore.flatMap((item: any) =>
            item.children.map((child: any) => ({
              id: child._id,
              score: child.score,
            })),
          );
        };
        setScore(extractQuestionInfo(), totalScore);
        return true;
      }}
    >
      <div
        style={{
          lineHeight: '48px',
          textAlign: 'center',
          fontSize: '16px',
        }}
      >
        <span>总分：{totalScore}</span>
      </div>
      {questionScore?.length ? (
        <div>
          {questionScore?.map((item: any) => {
            if (item.children?.length) {
              return (
                <div
                  key={item.id}
                  style={{
                    marginBottom: '16px',
                  }}
                >
                  <div
                    style={{
                      paddingBottom: '16px',
                    }}
                  >
                    {item.orderIndex !== -1 &&
                      `${convertNumber(item.orderIndex + 1)}、`}{' '}
                    {item.name}（共{item.children?.length}小题）总计
                    {item?.score || 0}
                    分。批量设置为
                    <InputNumber
                      controls={false}
                      min={1}
                      style={{
                        marginInline: '12px',
                      }}
                      value={null}
                      onChange={(value) => {
                        setQuestionScore((prevData: any[]) => {
                          return prevData.map((tar) => {
                            if (tar.id === item.id) {
                              const scoreArr = tar.children.map((val: any) => {
                                return {
                                  ...val,
                                  score: value,
                                };
                              });
                              return {
                                ...tar,
                                children: scoreArr,
                                score: (value || 0) * tar.children.length,
                              };
                            }
                            return tar;
                          });
                        });
                      }}
                    />
                    分
                  </div>
                  <ProTable
                    rowKey={'id'}
                    search={false}
                    options={false}
                    dataSource={item.children}
                    columns={columns}
                    pagination={false}
                    bordered
                  />
                </div>
              );
            } else {
              return null;
            }
          })}
        </div>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </ModalForm>
  );
};

export default ScoreSetting;
