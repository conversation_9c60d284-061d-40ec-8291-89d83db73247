import {
  getQuestionDifficultyStatistic,
  getQuestionWork,
} from '@/services/statistics';
import { useModel } from '@umijs/max';
import { Card, Col, message } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import SunburstChart from '../BaseChart/Sunburst';
import StatisticsContext from '../Context';
import styles from './styles/index.less';

interface DifficultyAnalysisProps {
  span: number;
  type: 'city' | 'county' | 'school';
  title: string;
}

const DifficultyAnalysis: React.FC<DifficultyAnalysisProps> = ({
  span,
  type,
  title,
}) => {
  const { initialState } = useModel('@@initialState');
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const { info } = useContext(StatisticsContext);
  const { yearTerm, county, schoolType } = info || {};

  const buildParams = () => {
    const baseParams = {
      semester_code: yearTerm,
      grade_section_code: schoolType,
    };

    switch (type) {
      case 'city':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county,
        };
      case 'county':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county || initialState?.enterprise?.area,
        };
      case 'school':
        return {
          ...baseParams,
          enterprise_code: initialState?.enterprise?.code,
        };
      default:
        return baseParams;
    }
  };

  const getInitData = async () => {
    const params = buildParams();
    setLoading(true);
    if (!yearTerm) return;
    const res = await (type === 'school'
      ? getQuestionWork
      : getQuestionDifficultyStatistic)(params);
    if (res?.errCode) {
      setLoading(false);
      message.warning(`获取数据失败，请稍后重试 ${res?.msg}`);
      return;
    }
    setLoading(false);
    setData(res.data || []);
  };

  useEffect(() => {
    if (type) getInitData();
  }, [type, yearTerm, county, schoolType]);

  return (
    <Col span={span}>
      <Card title={title} className={styles.card}>
        <SunburstChart data={data} loading={loading} type={type} />
      </Card>
    </Col>
  );
};
export default DifficultyAnalysis;
