import { getQuestionWork } from '@/services/statistics';
import { useModel } from '@umijs/max';
import { Card, Col, message } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import ColumnChart from '../BaseChart/Column';
import FilterSelect from '../BaseChart/FilterSelect';
import StatisticsContext from '../Context';
import styles from './styles/index.less';

interface QuantitativeAnalysisProps {
  span: number;
  type: 'city' | 'county' | 'school';
  title: string;
}

const QuantitativeAnalysis: React.FC<QuantitativeAnalysisProps> = ({
  span,
  type,
  title,
}) => {
  const { initialState } = useModel('@@initialState');
  const [data, setData] = useState<any>([]);
  const [filterInfo, setFilterInfo] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const { info, options } = useContext(StatisticsContext);
  const { yearTerm, county, schoolType } = info || {};

  const buildParams = () => {
    const baseParams = {
      semester_code: yearTerm,
      grade_code: filterInfo?.grade,
      subject_id: filterInfo?.subject,
      grade_section_code: schoolType,
    };

    switch (type) {
      case 'city':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county,
        };
      case 'county':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county || initialState?.enterprise?.area,
        };
      case 'school':
        return {
          ...baseParams,
          enterprise_code: initialState?.enterprise?.code,
        };
      default:
        return baseParams;
    }
  };
  const getLabel = (type: 'city' | 'county' | 'school', item: any) => {
    switch (type) {
      case 'city':
        return item?.area_name?.includes('市辖区')
          ? item?.city_name + item?.area_name
          : item?.area_name;
      case 'county':
        return item?.enterprise_name;
      case 'school':
        return item?.grade_name;
    }
  };

  const getInitData = async () => {
    const params = buildParams();
    setLoading(true);
    if (!yearTerm) return;

    const res = await getQuestionWork(params);
    if (res?.errCode) {
      setLoading(false);
      message.warning(`获取数据失败，请稍后重试 ${res?.msg}`);
      return;
    }
    setLoading(false);
    const newDatta = res?.data?.map((item: any) => {
      return {
        ...item,
        paramkey: 'total_homework_number',
        label: getLabel(type, item),
        value: parseFloat(item.total_homework_number),
      };
    });

    setData(newDatta || []);
  };

  useEffect(() => {
    if (type) getInitData();
  }, [type, yearTerm, county, schoolType, filterInfo]);

  return (
    <Col span={span}>
      <Card
        className={styles.card}
        title={title}
        extra={
          <FilterSelect
            mode="grade"
            type={type}
            gradeSection={schoolType}
            gradeOption={options}
            onChange={(value) => {
              setFilterInfo(value);
            }}
          />
        }
      >
        <ColumnChart loading={loading} data={data} />
      </Card>
    </Col>
  );
};
export default QuantitativeAnalysis;
