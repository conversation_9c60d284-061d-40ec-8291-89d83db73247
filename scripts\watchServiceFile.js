const fs = require('fs');
const path = require('path');

// 定义监视规则
const watchConfig = [
  {
    /** 监视的目录 */
    path: path.join(__dirname, '..', 'src', 'services'),
    /** 生成的文件名 */
    deposit: 'index.ts',
    /** 匹配的文件后缀 */
    matching: ['.ts'],
    /** 需要排除的文件后缀 */
    exclude: ['.d.ts'],
    /** 需要过滤的文件  以.ts 结尾的文件 */
    filter: [],
  },
  {
    /** 监视的目录 */
    path: path.join(__dirname, '..', 'src', 'assets'),
    /** 生成的文件名 */
    deposit: 'index.ts',
    /** 匹配的文件后缀 */
    matching: ['.png', '.jpg', '.jpeg', '.gif', '.svg'],
    /** 需要排除的文件后缀 */
    exclude: [],
    /** 需要过滤的文件  以'.png', '.jpg', '.jpeg', '.gif', '.svg' 结尾的文件 */
    filter: [],
    /** 生成需要写入文件的内容 */
    generate: (file) => {
      return `export { default  as ${path.basename(
        file,
        path.extname(file),
      )} } from './${file}';`;
    },
  },
];

// 默认生成规则
function defaultGenerate(file) {
  return `import * as ${path.basename(
    file,
    path.extname(file),
  )} from './${path.basename(file, path.extname(file))}';`;
}

watchConfig.forEach((config) => {
  // 确保 deposit 在 filter 中，防止出现死循环
  if (!config.filter.includes(config.deposit)) {
    config.filter.push(config.deposit);
  }

  // 如果没有设置 generate 规则，使用默认规则
  if (!config.generate) {
    config.generate = defaultGenerate;
  }
});

// 输出监视规则
console.log('-------------------');
console.log('监视规则:');
watchConfig.forEach((config) => {
  console.log(`  目录: ${config.path}`);
  console.log(`  匹配: 以 ${config.matching.join(', ')} 后缀结尾的文件`);
  console.log(
    `  排除: ${
      config.exclude.join(', ')
        ? `以 ${config.exclude.join(', ')} 后缀结尾的文件`
        : '未设置排除文件'
    }`,
  );
  console.log(`  过滤: ${config.filter.join(', ')} 文件`);
  console.log('-------------------');
});

// 生成导入和导出语句的函数
function generateIndex(config) {
  const sourceDir = config.path;
  const targetFile = path.join(sourceDir, config.deposit);

  fs.readdir(sourceDir, (err, files) => {
    if (err) {
      return console.error(`无法扫描目录 ${sourceDir}: ${err}`);
    }

    // 过滤出匹配的文件，排除不需要的文件
    const matchedFiles = files.filter(
      (file) =>
        config.matching.some((pattern) => file.endsWith(pattern)) &&
        !config.exclude.some((pattern) => file.endsWith(pattern)) &&
        !config.filter.includes(file),
    );

    // 根据文件类型生成不同的导入和导出语句
    let content = '';
    if (config.matching.includes('.ts')) {
      const importStatements = matchedFiles
        .map(
          (file) =>
            `import * as ${path.basename(file, '.ts')} from './${path.basename(
              file,
              '.ts',
            )}';`,
        )
        .join('\n');
      const exportStatements = `export {\n  ${matchedFiles
        .map((file) => path.basename(file, '.ts'))
        .join(',\n  ')},\n};`;
      content = `${importStatements}\n\n${exportStatements}`;
    } else {
      const exportStatements = matchedFiles
        .map((file) => config.generate(file))
        .join('\n');
      content = exportStatements;
    }

    // 写入 index.ts 文件
    fs.writeFile(targetFile, content, (err) => {
      if (err) {
        console.error(`写入文件错误 ${targetFile}: ${err}`);
        throw `写入文件错误 ${targetFile}: ${err}`;
      }
      console.log(`已更新 ${targetFile}`);
    });
  });
}

// 初始化生成 index.ts 文件
watchConfig.forEach((config) => {
  generateIndex(config);
});

// 使用 fs.watch 监视目录
watchConfig.forEach((config) => {
  const sourceDir = config.path;

  fs.watch(sourceDir, { recursive: true }, (eventType, filename) => {
    if (filename) {
      // 检查文件是否匹配监视规则
      const isMatching = config.matching.some((pattern) =>
        filename.endsWith(pattern),
      );
      const isExcluded = config.exclude.some((pattern) =>
        filename.endsWith(pattern),
      );
      const isFiltered = config.filter.includes(filename);

      if (isMatching && !isExcluded && !isFiltered) {
        if (eventType === 'rename') {
          generateIndex(config);
        }
      }
    }
  });
});
