/*
 * @Description: 已废弃
 * @Description: 搜索题库框
 */
import ConditionalRender from '@/components/ConditionalRender';
import { DictionarieState } from '@/models/dictionarie';
import { QuestionTypesState } from '@/models/questionTypes';
import {
  ProForm,
  ProFormInstance,
  ProFormSegmented,
  ProFormSelect,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Input, message, Space } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import styles from './common.less';

const ProFormSearch = (props: any) => {
  const { Search } = Input;
  return (
    <ProForm.Item {...props}>
      <Search
        style={{
          width: props?.width,
        }}
        {...props?.fieldProps}
        allowClear={props?.allowClear}
        placeholder={props?.placeholder}
        onSearch={props?.onSearch}
      />
    </ProForm.Item>
  );
};

enum AllowedFields {
  SOURCE = 'source',
  QUESTION_TYPES = 'questionTypes',
  DIFFICULTY = 'difficulty',
  KNOWLEDGE_POINT = 'knowledgePoint',
  SEARCH_TOPIC = 'searchTopic',
  SEARCH_CATEGORY_LABELS = 'searchCategoryLabels',
}

type AllFields =
  | 'source'
  | 'questionTypes'
  | 'difficulty'
  | 'knowledgePoint'
  | 'searchTopic'
  | 'searchCategoryLabels';

const defaultShowFields: AllowedFields[] = [
  AllowedFields.SOURCE,
  AllowedFields.QUESTION_TYPES,
  AllowedFields.DIFFICULTY,
  AllowedFields.KNOWLEDGE_POINT,
  AllowedFields.SEARCH_TOPIC,
  AllowedFields.SEARCH_CATEGORY_LABELS,
];

export type SearchTopicType = Partial<{
  source: string;
  questionTypes: string;
  difficulty: string;
  knowledgePoint: string;
  topic: string;
  categoryLabels: string;
}>;

interface SearchTopicBoxProps {
  onChange?: (data: SearchTopicType) => void;
  showFields?: AllFields[];
  className?: string;
  style?: React.CSSProperties;
  dictionarie: DictionarieState;
  questionTypes: QuestionTypesState;
}

type OptionsType = {
  label: string;
  value: string;
  title?: string;
};

const SearchTopicBox: React.FC<SearchTopicBoxProps> = ({
  onChange,
  showFields = defaultShowFields,
  className,
  style,
  dictionarie,
  questionTypes,
}) => {
  const formRef = React.useRef<ProFormInstance>();
  const [searchState, setSearchState] = useState<SearchTopicType>({});
  const [options, setOptions] = useState({
    difficulty: [] as OptionsType[],
    questionTypes: [] as any[],
    source: [] as OptionsType[],
  });

  // 初始化选项和表单值
  useEffect(() => {
    const findOPtions = (list: any[], type: string) => {
      const baseOptions =
        list
          ?.filter((item) => item.type === type)
          .sort((a, b) => a.sortOrder - b.sortOrder)
          .map((item) => ({ label: item.name, value: item.code })) ?? [];
      return [{ label: '全部', value: '全部' }, ...baseOptions];
    };
    try {
      const difficultyOptions = findOPtions(
        dictionarie?.list ?? [],
        'difficulty_type',
      );
      const questionTypeOptions = [
        {
          label: '全部',
          value: '全部',
        },
        ...(questionTypes?.list.map((item) => {
          return {
            id: item.id,
            label: item.name,
            value: `${item.sourceCode}_${item.code}`,
            originalCode: item.code,
            sourceCode: item.sourceCode,
            sourceName: item.sourceName,
          };
        }) ?? []),
      ];
      const sourceOptions = findOPtions(
        dictionarie?.list ?? [],
        'question_source',
      );
      const initialValues = {
        source:
          showFields.includes(AllowedFields.SOURCE) && sourceOptions.length
            ? sourceOptions[0].value
            : undefined,
        questionTypes:
          showFields.includes(AllowedFields.QUESTION_TYPES) &&
          questionTypeOptions.length
            ? questionTypeOptions[0].value
            : undefined,
        difficulty:
          showFields.includes(AllowedFields.DIFFICULTY) &&
          difficultyOptions.length
            ? difficultyOptions[0].value
            : undefined,
        knowledgePoint: undefined,
        topic: undefined,
        categoryLabels: undefined,
      };

      setOptions({
        difficulty: difficultyOptions,
        questionTypes: questionTypeOptions,
        source: sourceOptions,
      });
      formRef.current?.setFieldsValue(initialValues);
      setSearchState(initialValues);
    } catch (error) {
      console.error('初始化搜索框失败:', error);
      message.warning('初始化搜索框失败');
    }
  }, [dictionarie]);

  // 处理字段变化
  const handleChange = (field: keyof SearchTopicType) => (value: any) => {
    const newState = { ...searchState, [field]: value };
    setSearchState(newState);
  };

  // 计算返回给外部的值
  const memoizedValues = React.useMemo(() => {
    const getLabelFromValue = (
      value: string | undefined,
      options: OptionsType[],
    ) => {
      return value
        ? options.find((opt) => opt.value === value)?.label
        : undefined;
    };

    const questionTypesLabel = getLabelFromValue(
      searchState.questionTypes,
      options.questionTypes,
    );
    const difficultyLabel = getLabelFromValue(
      searchState.difficulty,
      options.difficulty,
    );

    return {
      ...searchState,
      source: getLabelFromValue(searchState.source, options.source),
      questionTypes:
        questionTypesLabel === '全部' ? undefined : questionTypesLabel,
      difficulty: difficultyLabel === '全部' ? undefined : difficultyLabel,
    };
  }, [searchState, options]);

  // 触发外部回调
  useEffect(() => {
    onChange?.(memoizedValues);
  }, [memoizedValues]);

  return (
    <div className={classNames(styles.searchTopicBox, className)} style={style}>
      <ProForm submitter={false} formRef={formRef} layout="horizontal">
        <ConditionalRender
          hasAccess={showFields.includes(AllowedFields.SOURCE)}
          accessComponent={
            <ProFormSelect
              label="来源"
              name="source"
              width="sm"
              showSearch
              onChange={handleChange('source')}
              request={async () => options.source}
            />
          }
        />
        <ProFormSegmented
          name="difficulty"
          label="难度"
          hidden={!showFields.includes(AllowedFields.DIFFICULTY)}
          fieldProps={{
            options: options.difficulty,
            onChange: handleChange('difficulty'),
          }}
        />
        <ProForm.Item
          name="questionTypes"
          label="题型"
          className={styles.questionTypes}
          hidden={!showFields.includes(AllowedFields.QUESTION_TYPES)}
        >
          <div className={styles.segmentedContainer}>
            {options.questionTypes.map((item) => {
              return (
                <div
                  key={item.id}
                  className={classNames(styles.segmentedItem, {
                    [styles.segmentedItemActive]:
                      searchState.questionTypes === item.originalCode ||
                      `${searchState.questionTypes}` === item.value,
                  })}
                  onClick={() => handleChange('questionTypes')(item.value)}
                  title={item.title}
                >
                  {item.label}
                </div>
              );
            })}
          </div>
        </ProForm.Item>

        <Space size="large" className={styles.other}>
          <ConditionalRender
            hasAccess={showFields.includes(AllowedFields.KNOWLEDGE_POINT)}
            accessComponent={
              <ProFormTreeSelect
                width="sm"
                label="知识点"
                name="knowledgePoint"
                placeholder="请选择知识点"
                fieldProps={{
                  onChange: handleChange('knowledgePoint'),
                }}
              />
            }
          />
          <ConditionalRender
            hasAccess={showFields?.includes(AllowedFields.SEARCH_TOPIC)}
            accessComponent={
              <ProFormSearch
                width={216}
                name="topic"
                label="搜索"
                allowClear
                placeholder="请输入题目关键词"
                onSearch={(value: string) => {
                  setSearchState({
                    ...searchState,
                    topic: value,
                  });
                }}
              />
            }
          />
          <ConditionalRender
            hasAccess={showFields?.includes(
              AllowedFields.SEARCH_CATEGORY_LABELS,
            )}
            accessComponent={
              <ProFormSearch
                width={216}
                name="categoryLabels"
                label="类题标签"
                allowClear
                placeholder="请输入要搜索的类题标签"
                onSearch={(value: string) => {
                  setSearchState({
                    ...searchState,
                    categoryLabels: value,
                  });
                }}
              />
            }
          />
        </Space>
      </ProForm>
    </div>
  );
};

export default connect(({ dictionarie, questionTypes }) => ({
  dictionarie,
  questionTypes,
}))(SearchTopicBox);
