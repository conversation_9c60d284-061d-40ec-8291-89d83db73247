import {
  DrawerForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <DrawerForm<any>
      width={'50vw'}
      title={info ? '编辑课程目标' : '新增课程目标'}
      autoFocusFirstInput
      drawerProps={{
        onClose,
        afterOpenChange: (open) => {
          if (!open) {
            onClose();
          }
        },
        destroyOnClose: true,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ span: 24 }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText
        name="name"
        label="名称"
        fieldProps={{ maxLength: 100 }}
        rules={[{ required: true, message: '请输入名称！' }]}
      />
      <ProFormTextArea
        name="describe"
        label="描述"
        fieldProps={{
          autoSize: { minRows: 2, maxRows: 12 },
        }}
        rules={[{ required: true, message: '请输入描述！' }]}
      />
      <ProFormTextArea name="analy" label="解析" />
    </DrawerForm>
  );
};

export default EditModal;
