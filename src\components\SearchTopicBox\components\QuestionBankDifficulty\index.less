.wapper {
  display: flex;
  align-items: center;

  .options {
    display: flex;
    flex-wrap: wrap;

    .optionsItem {
      position: relative;
      min-height: 28px;
      line-height: 28px;
      padding: 0 11px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      border-radius: 4px;
      color: rgba(0, 0, 0, 65%);
      backface-visibility: hidden;

      &:hover:not(.selected) {
        color: #000;
        background-color: #e0e0e0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
      }
    }

    .selected {
      color: #fff;
      background-color: var(--primary-color);
    }
  }
}
