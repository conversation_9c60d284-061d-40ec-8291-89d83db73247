import {
  ModalForm,
  ProFormSegmented,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.TextbookChecklist;
  textbookList: API.Textbook[];
  onSave: (info: API.TextbookChecklist) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  textbookList,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<API.TextbookChecklist>
      title={info ? '编辑名录' : '注册名录'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      width={400}
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormSelect
        name="textbook_id"
        label="教材版本"
        options={textbookList.map((item) => ({
          label: item.textbook_version,
          value: item.id,
        }))}
        initialValue={info?.textbook_id}
        rules={[{ required: true, message: '请选择教材版本！' }]}
      />
      <ProFormSegmented
        name="volume"
        label="册次"
        valueEnum={{
          上册: '上册',
          下册: '下册',
          全一册: '全一册',
        }}
        initialValue={info?.volume || '上册'}
        rules={[{ required: true, message: '请选择册次！' }]}
      />
      <ProFormText
        name="editor"
        label="主编"
        initialValue={info?.editor || ''}
      />
    </ModalForm>
  );
};

export default EditModal;
