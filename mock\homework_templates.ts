const users: any = [];
for (let i = 0; i < 5; i++) {
  users.push({
    id: '9d522c8c-4b43-48d1-aaff-fbbea3880415' + 1,
    name: '【典中点】六上语文-人教部编版',
    xbzy_zy_id: 'a29e5e01-b9c6-49c0-9433-fc6f82ca5ce6',
    source: '典中点范本',
    enterpriseId: null,
    originalFileURL: null,
    fileURL: null,
    createdAt: '2024-05-29T09:45:55.000Z',
    updatedAt: '2024-06-14T03:55:00.000Z',
    xbzy_zy: {
      id: 'a29e5e01-b9c6-49c0-9433-fc6f82ca5ce6',
      mc: '六年级语文人教部编版上册',
      xd: 'e7bbb2de-0590-11ed-9c79-92fc3b3249d5',
      nj: 'e7bbecae-0590-11ed-9c79-92fc3b3249d5',
      xk: '6a749654-0772-11ed-ac74-092ab92074e6',
      jcbb: 'xx-yw-rjbbb',
      jc: 'ff8080814371757b014390f883db0453',
      jfmc: '六年级语文人教部编版上册',
      createdAt: '2024-01-24T08:17:20.000Z',
      updatedAt: '2024-01-24T08:17:20.000Z',
    },
  });
}

export default {
  'GET /mock/homework_templates': (req: any, res: any) => {
    setTimeout(() => {
      res.json({
        errorCode: 0,
        data: { list: users, total: users.length },
        msg: 'ok',
      });
    }, 500);
  },
  'GET /mock/compliance_templates': (req: any, res: any) => {
    setTimeout(() => {
      res.json({
        errorCode: 0,
        data: { list: users, total: users.length },
        msg: 'ok',
      });
    }, 500);
  },
};
