import { ModalForm, ProFormText } from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<any>
      title={info ? '编辑' : '新增'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText
        name="name"
        label="名称"
        rules={[{ required: true, message: '请输入名称！' }]}
        colProps={{ span: 12 }}
      />
    </ModalForm>
  );
};

export default EditModal;
