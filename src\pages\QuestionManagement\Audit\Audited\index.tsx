import ConditionalRender from '@/components/ConditionalRender';
import TopicContent from '@/components/QuestionBank/TopicContent';
import {
  getPersonalQuestion,
  getQuestionCheckRecord,
} from '@/services/personal';
import {
  Button,
  Col,
  Empty,
  message,
  Modal,
  Pagination,
  PaginationProps,
  Row,
  Space,
  Spin,
  Tag,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import styles from './index.less';

export enum QuestionCheckStatus {
  '待审核' = '待审核',
  '已通过' = '已通过',
  '已拒绝' = '已拒绝',
}
interface AuditedProps {
  subject?: number;
}

export const tagColor = (status: QuestionCheckStatus) => {
  switch (status) {
    case QuestionCheckStatus['待审核']:
      return 'blue';
    case QuestionCheckStatus['已通过']:
      return 'green';
    case QuestionCheckStatus['已拒绝']:
      return 'red';
    default:
      return 'blue';
  }
};

export interface AuditedRef {
  /** 展开或收起所有试题 */
  expandAll: (expand: boolean) => void;
  /** 是否所有试题都已展开 */
  isAllExpanded: boolean;
}

const Audited = React.forwardRef<AuditedRef, AuditedProps>(
  ({ subject }, ref) => {
    const [dataSource, setDataSource] = React.useState<any[]>([]);
    const [isAllExpanded, setIsAllExpanded] = React.useState(false);
    const [pagination, setPagination] = React.useState<PaginationProps>({
      current: 1,
      pageSize: 10,
    });
    const [total, setTotal] = React.useState(0);
    const [loading, setLoading] = React.useState(false);
    const [checkRecordModal, setCheckRecordModal] = React.useState<{
      visible: boolean;
      data: any;
    }>({
      visible: false,
      data: undefined,
    });
    /** 审核记录 */
    const [checkRecordData, setCheckRecordData] = React.useState<any[]>([]);

    React.useImperativeHandle(ref, () => ({
      expandAll: (expand: boolean) => {
        setDataSource((prev) =>
          prev.map((item) => ({ ...item, expanded: expand })),
        );
        setIsAllExpanded(expand);
      },
      isAllExpanded,
    }));

    /** 获取待审试题列表 */
    const getUnAuditedQuestionList = async () => {
      setLoading(true);
      try {
        const { errCode, data, msg } = await getPersonalQuestion({
          offset: (pagination?.current || 1) - 1,
          limit: pagination?.pageSize || 10,
          checkStatus: `${QuestionCheckStatus['已通过']},${QuestionCheckStatus['已拒绝']}`,
          subject,
        });
        if (errCode) {
          message.warning(`获取待审试题列表失败 ${msg}`);
          return;
        }
        setTotal(data?.total || 0);
        setDataSource(
          data.list?.map((item, index) => ({
            ...item,
            key: item.key || item.id || `item-${index}`,
            expanded: false,
          })) || [],
        );
      } finally {
        setLoading(false);
      }
    };

    /** 获取审核记录 */
    const getAuditRecord = async (id: number) => {
      const { errCode, data, msg } = await getQuestionCheckRecord({
        questionBankId: id,
      });
      if (errCode) {
        message.warning(`获取审核记录失败 ${msg}`);
        return;
      }
      setCheckRecordData(data?.list || []);
    };

    useEffect(() => {
      if (checkRecordModal.visible) {
        getAuditRecord(checkRecordModal?.data?._id);
      }
    }, [checkRecordModal]);

    useEffect(() => {
      if (subject && pagination) {
        getUnAuditedQuestionList();
      }
    }, [subject, pagination]);

    return (
      <>
        {/* <StickyBox offsetTop={148} offsetBottom={20} style={{ zIndex: 1 }}> */}
        <header className={styles.options}>
          <div>已审核题目数量：{total}</div>
        </header>
        {/* </StickyBox> */}
        <Spin spinning={loading} tip="数据加载中...">
          <div className={styles.wapper}>
            <ConditionalRender
              hasAccess={dataSource.length !== 0}
              accessComponent={
                <>
                  {dataSource.map((item) => {
                    return (
                      <div
                        className={styles.topicContent}
                        style={{
                          maxHeight: item.expanded ? 'none' : '280px',
                        }}
                        key={item.key}
                      >
                        <div className={styles.header}>
                          <Space size="large">
                            <Tag color={tagColor(item?.checkStatus)}>
                              {item?.checkStatus}
                            </Tag>
                            <span>类型：{item?.type?.name ?? '-'}</span>
                            <span>难度：{item?.difficulty?.name ?? '-'}</span>
                            <span>年级：{item?.grade?.name ?? '-'}</span>
                            <span>
                              版本：{item?.textbookVersion?.name ?? '-'}
                            </span>
                            <span>课时：{item?.catalog?.name}</span>
                          </Space>
                          <Space>
                            <Button
                              type="link"
                              onClick={() => {
                                setCheckRecordModal({
                                  visible: true,
                                  data: item,
                                });
                              }}
                            >
                              审核记录
                            </Button>
                          </Space>
                        </div>
                        <TopicContent info={item} allShowAnalysis />
                        {item.expanded ? (
                          <div style={{ textAlign: 'center', marginTop: 8 }}>
                            <Button
                              type="link"
                              onClick={(e) => {
                                e.stopPropagation();
                                setDataSource((prev) =>
                                  prev.map((i) => ({
                                    ...i,
                                    expanded:
                                      i.key === item.key ? false : i.expanded,
                                  })),
                                );
                              }}
                            >
                              收起当前试题
                            </Button>
                          </div>
                        ) : (
                          <div className={styles.footer}>
                            <Button
                              type="link"
                              onClick={(e) => {
                                e.stopPropagation();
                                setDataSource((prev) =>
                                  prev.map((i) => ({
                                    ...i,
                                    expanded:
                                      i.key === item.key ? true : i.expanded,
                                  })),
                                );
                              }}
                            >
                              展开当前试题
                            </Button>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </>
              }
              noAccessComponent={
                <Empty
                  style={{
                    margin: '30px',
                  }}
                  description="暂无内容"
                />
              }
            />

            <ConditionalRender
              hasAccess={dataSource.length !== 0}
              accessComponent={
                <Pagination
                  align="center"
                  current={pagination?.current}
                  pageSize={pagination?.pageSize}
                  total={total}
                  onChange={(page, pageSize) => {
                    setPagination({
                      current: page,
                      pageSize,
                    });
                  }}
                  showSizeChanger
                  showQuickJumper
                />
              }
            />
          </div>
        </Spin>
        <Modal
          title="审核记录"
          open={checkRecordModal.visible}
          onCancel={() =>
            setCheckRecordModal({ visible: false, data: undefined })
          }
          footer={null}
        >
          <div className={styles.checkRecord}>
            {checkRecordData.map((item, index) => (
              <div key={index} className={styles.checkRecordCard}>
                <Row gutter={[16, 10]}>
                  <Col span={12}>审核人：{item?.checker}</Col>
                  <Col span={12}>
                    审核状态：
                    <Tag color={tagColor(item?.checkStatus)}>
                      {item?.checkStatus}
                    </Tag>
                  </Col>
                  <Col span={12}>
                    审核时间：
                    {dayjs(item?.createdAt).format('YYYY-MM-DD HH:mm')}
                  </Col>
                  <Col span={12}>审核意见：{item?.suggestion}</Col>
                </Row>
              </div>
            ))}
          </div>
        </Modal>
      </>
    );
  },
);

export default Audited;
