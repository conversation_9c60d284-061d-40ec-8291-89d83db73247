import { dictionaries } from '@/services';
import {
  OnChangePayload,
  onProTableChange,
  onProTableSearch,
} from '@/utils/calc';
import { message } from 'antd';

export type DictionarieState = {
  total: number;
  list: API.Dictionarie[];
  currentList: API.Dictionarie[];
  inited: boolean;
};

export default {
  state: {
    total: 0,
    list: [],
    currentList: [],
    inited: false,
  } as DictionarieState,

  effects: {
    /** 查询列表 **/
    *queryList(
      { payload }: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(dictionaries.index, payload);
      if (errCode) {
        message.error(msg || '字典列表查询失败');
      } else {
        yield put({ type: 'querySuccess', payload: data });
      }
    },
    *add(
      { payload }: { payload: API.Dictionarie },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(dictionaries.create, payload);
      if (errCode) {
        message.error(msg || '字典创建失败');
      } else {
        message.success('字典创建成功');
        yield put({ type: 'addSuccess', payload: data });
      }
    },
    *update(
      {
        payload,
      }: { payload: { code: string; info: Partial<API.Dictionarie> } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(
        dictionaries.update,
        payload.code,
        payload.info,
      );
      if (errCode) {
        message.error(msg || '字典更新失败');
      } else {
        message.success('字典更新成功');
        yield put({ type: 'updateSuccess', payload });
      }
    },
    *remove(
      { payload }: { payload: { id: number } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(dictionaries.remove, payload.id);
      if (errCode) {
        message.error(msg || '字典删除失败');
      } else {
        message.success('字典删除成功');
        yield put({ type: 'removeSuccess', payload });
      }
    },
  },

  reducers: {
    /** 查询列表成功后更新数据，触发渲染 */
    querySuccess(
      _state: DictionarieState,
      { payload }: { payload: DictionarieState },
    ) {
      return { ...payload, currentList: payload.list, inited: true };
    },
    /** proTable组件的过滤和排序操作，分页不用处理，组件自己会完成 */
    onChange(
      state: DictionarieState,
      { payload }: { payload: OnChangePayload<API.Dictionarie> },
    ) {
      const filteredList = onProTableChange(state.list, payload);
      return {
        ...state,
        total: filteredList.length,
        currentList: filteredList,
      };
    },
    /** proTable组件的查询操作 */
    onSearch(
      state: DictionarieState,
      { payload }: { payload: Partial<API.Dictionarie> },
    ) {
      const newList = onProTableSearch(state.list, payload);
      return {
        ...state,
        total: newList.length,
        currentList: newList,
      };
    },
    addSuccess(
      state: DictionarieState,
      { payload }: { payload: API.Dictionarie },
    ) {
      const newList = [...state.list, payload]
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .sort((a, b) => a.type.localeCompare(b.type));
      const currentList = [...state.currentList, payload]
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .sort((a, b) => a.type.localeCompare(b.type));
      return {
        ...state,
        total: newList.length,
        list: newList,
        currentList,
      };
    },
    updateSuccess(
      state: DictionarieState,
      {
        payload,
      }: { payload: { code: string; info: Partial<API.Dictionarie> } },
    ) {
      const newList = state.list
        .map((item) => {
          if (item.code === payload.code) {
            return { ...item, ...payload.info };
          }
          return item;
        })
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .sort((a, b) => a.type.localeCompare(b.type));
      const currentList = state.currentList.map((item) => {
        if (item.code === payload.code) {
          return { ...item, ...payload.info };
        }
        return item;
      });
      return {
        ...state,
        list: newList,
        currentList,
      };
    },
    removeSuccess(
      state: DictionarieState,
      { payload }: { payload: { code: string } },
    ) {
      const newList = state.list.filter((item) => item.code !== payload.code);
      const currentList = state.currentList.filter(
        (item) => item.code !== payload.code,
      );
      return {
        ...state,
        list: newList,
        currentList,
      };
    },
  },
};
