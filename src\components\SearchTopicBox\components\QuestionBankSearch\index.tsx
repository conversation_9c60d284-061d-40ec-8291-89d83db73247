import ConditionalRender from '@/components/ConditionalRender';
import { Input } from 'antd';
import classNames from 'classnames';
import React from 'react';
import styles from './index.less';

const { Search } = Input;

interface QuestionBankSearchProps {
  onChange?: (value: string) => void;
  label?: string | false;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
}

const QuestionBankSearch: React.FC<QuestionBankSearchProps> = ({
  onChange,
  className,
  label = '搜索',
  placeholder = '请输入关键字搜索试题',
}) => {
  const [value, setValue] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (onChange && typeof value === 'string') {
      onChange(value.trim());
    }
  }, [value]);

  return (
    <div className={classNames(styles.wapper, className)} style={{ ...styles }}>
      <ConditionalRender
        hasAccess={!!label}
        accessComponent={<label>{label}：</label>}
      />
      <Search
        placeholder={placeholder}
        allowClear
        onSearch={(newValue) => {
          setValue(newValue.trim());
        }}
      />
    </div>
  );
};
export default QuestionBankSearch;
