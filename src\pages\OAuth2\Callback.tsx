/*
 * @Description: OAuth2统一认证回调页面
 * @Date: 2025-01-07 17:44:07
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-05-08 09:56:36
 */
import { getToken } from '@/services/auth';
import { saveJWT } from '@/utils/auth';
import { useSearchParams } from '@umijs/max';
import { Flex, message, Progress } from 'antd';
import { useEffect, useState } from 'react';

const Callback = () => {
  const [search] = useSearchParams();
  const code = search.get('code');
  const [percent, setPercent] = useState<number>(0);

  useEffect(() => {
    if (!code) {
      console.log('无效的code');
      return;
    }
    // 每1秒更新一次percent
    const timer = setInterval(() => {
      setPercent((prev) => {
        if (prev >= 98) {
          clearInterval(timer);
          return 98;
        }
        return prev + Math.round(Math.random() * 20);
      });
    }, 200);
    getToken({
      code,
      redirect_uri: encodeURI(
        `${window.location.origin}${BASE_URL}/oauth2/callback`,
      ),
    }).then(({ errCode, msg, data }) => {
      clearInterval(timer);
      console.log(errCode, msg, data);
      if (errCode) {
        message.error(msg);
      } else {
        setPercent(100);
        saveJWT(data.token, `${new Date().getTime() + data.expiresIn * 1000}`);
      }
    });

    return () => {
      clearInterval(timer);
    };
  }, [code]);

  useEffect(() => {
    if (percent >= 100) {
      sessionStorage.removeItem('homework-ischild');
      setTimeout(() => {
        window.location.href = BASE_URL + '/';
      }, 250);
    }
  }, [percent]);

  return (
    <Flex
      vertical
      align="center"
      justify="center"
      gap={20}
      style={{ height: 'calc(100vh - 200px)' }}
    >
      <Progress
        percent={percent}
        type="line"
        status="active"
        style={{ width: '50vw' }}
      />
      {percent >= 100 ? '登录成功，正在跳转...' : '正在登录...'}
    </Flex>
  );
};

export default Callback;
