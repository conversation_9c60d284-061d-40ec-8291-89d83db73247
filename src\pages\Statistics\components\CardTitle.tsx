import ConditionalRender from '@/components/ConditionalRender';
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  MinusOutlined,
} from '@ant-design/icons';
import React from 'react';
import styles from '../index.less';

interface CardTitleProps {
  title: string;
  type?: 'up' | 'down' | 'none';
  value: string | number;
}

const CardTitle: React.FC<CardTitleProps> = ({ title, type, value }) => {
  return (
    <ConditionalRender
      hasAccess={!!type}
      accessComponent={
        <div className={styles.cardTitle}>
          <span>{title}</span>
          <span
            className={styles.cardTitleValue}
            style={{
              color:
                type === 'up'
                  ? '#3f8600'
                  : type === 'none'
                  ? '#ed8f03'
                  : '#cf1322',
            }}
          >
            {type === 'up' ? (
              <ArrowUpOutlined />
            ) : type === 'none' ? (
              <MinusOutlined />
            ) : (
              <ArrowDownOutlined />
            )}
            &nbsp;
            {type !== 'none' && (
              <span className={styles.percentageChangeValue}>{value}</span>
            )}
          </span>
          较上月
        </div>
      }
    />
  );
};
export default CardTitle;
