import {
  dbjcImport,
  dbjcsjImport,
  kszyImport,
  kszysjImport,
  QuestionImport,
} from '@/services/upload';
import { InboxOutlined } from '@ant-design/icons';
import { Button, message, Modal, Upload, UploadProps } from 'antd';
import { UploadChangeParam, UploadFile } from 'antd/lib/upload/interface';
import React, { useState } from 'react';
import { MONGO_MODEL_KEY } from './index';
import styles from './index.less';

const { Dragger } = Upload;

interface ModalUploadProps {
  open: boolean;
  tableName?: MONGO_MODEL_KEY;
  type?: string;
  info?: any;
  /** 关闭回调 */
  onClose: () => void;
  /** 上传成功后回调 */
  onSuccess?: () => void;
}

const ModalUpload: React.FC<ModalUploadProps> = ({
  open,
  info,
  type,
  tableName,
  onClose,
  onSuccess,
}) => {
  const [fileList, setFileList] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择文件');
      return;
    }

    setUploading(true);

    try {
      const {
        fbname,
        outterId,
        innerId,
        newId,
        catalog,
        volume,
        textbook,
        subject,
        grade,
        section,
      } = info || {};

      const params = {
        gradeSection: {
          name: section?.name,
          code: section?.code,
        },
        grade: {
          name: grade?.name,
          code: grade?.code,
        },
        subject: {
          name: subject?.subject,
          id: subject?.id,
        },
        textbookVersion: {
          name: textbook?.textbook_version,
          id: textbook?.id,
        },
        volume: {
          name: volume?.volume,
          id: volume?.id,
        },
        catalog: catalog
          ? {
              name: catalog?.title,
              id: catalog?.id,
            }
          : undefined,
        tableName,
      };
      let response;
      switch (type) {
        case '课时作业范本':
          response = await kszyImport({
            files: fileList[0],
            fields: JSON.stringify({
              ...params,
              name: fbname,
              outterId,
            }),
          });
          break;
        case '达标检测范本':
          response = await dbjcImport({
            files: fileList[0],
            fields: JSON.stringify({ ...params, name: fbname, innerId }),
          });
          break;
        case '课时作业设计':
          response = await kszysjImport({
            files: fileList[0],
            fields: JSON.stringify({
              ...params,
              name: fbname,
              outterId,
              newId,
            }),
          });
          break;
        case '达标检测设计':
          response = await dbjcsjImport({
            files: fileList[0],
            fields: JSON.stringify({ ...params, name: fbname, innerId }),
          });
          break;
        default:
          response = await QuestionImport({
            files: fileList[0],
            fields: JSON.stringify(params),
          });
          break;
      }

      if (response?.errCode) {
        return message.warning(`文件上传失败，${response?.msg}`);
      } else if (response) {
        message.success('文件上传成功');
        onSuccess?.();
      }
    } catch (error) {
      message.error('上传失败');
    } finally {
      setUploading(false);
    }
  };

  const props: UploadProps = {
    name: 'file',
    maxCount: 1,
    listType: 'picture',
    beforeUpload: (file: File) => {
      const isValidType =
        file.type ===
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        file.name.endsWith('.docx');

      const isValidSize = file.size / 1024 / 1024 <= 10;

      if (!isValidType) {
        message.error('只能上传 .docx 格式的文件！');
        return Upload.LIST_IGNORE;
      }

      if (!isValidSize) {
        message.error('文件大小不能超过 10MB！');
        return Upload.LIST_IGNORE;
      }

      setFileList([file]);
      return false;
    },
    onRemove: () => {
      setFileList([]);
    },
    onChange: (info: UploadChangeParam<UploadFile<any>>) => {
      if (info.file.status === 'removed') {
        setFileList([]);
      }
    },
  };

  const steps = [
    {
      id: 1,
      content: (
        <a
          href="https://ysp-uploader-**********.cos.ap-nanjing.myqcloud.com/question-bank-template/%E9%A2%98%E5%BA%93%E6%A8%A1%E6%9D%BF.docx"
          download
        >
          下载{type || '试题'}导入模板
        </a>
      ),
    },
    {
      id: 2,
      content: '根据模板格式填写试题内容，并保存为 .docx 格式',
    },
    {
      id: 3,
      content: '上传已填写好的模版文件',
    },
    {
      id: 4,
      content: type
        ? '系统将自动解析模板并导入作业数据'
        : '系统将自动解析模版并导入试题数据',
    },
    type?.includes('达标检测') && {
      id: 5,
      content: (
        <span
          style={{
            color: 'red',
          }}
        >
          注意：导入数据将完全覆盖当前达标检测下的原有数据，请谨慎操作
        </span>
      ),
    },
  ];

  return (
    <Modal
      open={open}
      onCancel={() => {
        setFileList([]);
        onClose();
      }}
      title={`${type || '试题'}导入`}
      footer={null}
      width={600}
      centered
      destroyOnClose
      closable={!uploading}
      maskClosable={false}
    >
      <div style={{ padding: '24px' }}>
        <Dragger {...props} disabled={uploading}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">单击或拖动文件到此区域上传</p>
          <p className="ant-upload-hint">
            仅支持单个文件上传，文件格式为.docx，大小不超过10MB
          </p>
        </Dragger>

        <div className={styles.submitBtn}>
          <Button
            type="primary"
            onClick={handleUpload}
            loading={uploading}
            disabled={fileList.length === 0}
          >
            {uploading ? '上传中...' : '确认上传'}
          </Button>
        </div>

        <div className={styles.uploadWarning}>
          <h4>操作顺序：</h4>
          <ol>
            {steps.map(
              (item) => item && <li key={item?.id}>{item?.content}</li>,
            )}
          </ol>
        </div>
      </div>
    </Modal>
  );
};

export default ModalUpload;
