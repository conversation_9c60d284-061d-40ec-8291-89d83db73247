import { useCurrentAccess } from '@/common/useCurrentAccess';
import ConditionalRender from '@/components/ConditionalRender';
import { create, index, remove, update } from '@/services/plan_course_setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Popconfirm, Space, message } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const DesignCourseSetting: React.FC = () => {
  const { isAdmin } = useCurrentAccess();
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<any | undefined>(undefined);

  const handleSave = async (values: any) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: any) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<any, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '课程类别',
      dataIndex: 'type',
      align: 'center',
      key: 'type',
    },
    {
      title: '课程名称',
      dataIndex: 'course_name',
      align: 'center',
      key: 'name',
    },
    {
      title: '年级',
      dataIndex: 'grade_name',
      align: 'center',
      key: 'grade_name',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <ConditionalRender
          hasAccess={isAdmin}
          key={record.id}
          accessComponent={
            <Space>
              <Button
                type="link"
                onClick={() => {
                  setCurrent(record);
                  setModalVisible(true);
                }}
              >
                编辑
              </Button>
              <Popconfirm
                title="确认删除？"
                onConfirm={() => {
                  handleDel(record);
                }}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </Space>
          }
        />
      ),
    },
  ];

  return (
    <>
      <ProTable<any>
        search={false}
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        options={false}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data.list,
            total: data.total,
          };
        }}
        toolBarRender={() => [
          <ConditionalRender
            key="add"
            hasAccess={isAdmin}
            accessComponent={
              <Button
                type="primary"
                onClick={() => {
                  setCurrent(undefined);
                  setModalVisible(true);
                }}
                icon={<PlusOutlined />}
              >
                新增
              </Button>
            }
          />,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default DesignCourseSetting;
