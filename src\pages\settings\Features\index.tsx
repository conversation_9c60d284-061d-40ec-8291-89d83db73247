import { FeatureState } from '@/models/feature';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { AnyAction, connect } from '@umijs/max';
import { Button, Popconfirm, Space, Spin } from 'antd';
import React, { Dispatch, useEffect, useState } from 'react';
import EditModal from './EditModal';

const FeaturesPage: React.FC<{
  feature: FeatureState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ feature, dispatch, loading }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Feature | undefined>(undefined);

  const handleSave = async (values: API.Feature) => {
    if (current) {
      const { code, ...info } = values;
      dispatch({
        type: 'feature/update',
        payload: {
          code,
          info,
        },
      });
    } else {
      dispatch({
        type: 'feature/add',
        payload: values,
      });
    }
  };

  const handleDel = async (record: API.Feature) => {
    const { code } = record;
    dispatch({
      type: 'feature/remove',
      payload: {
        code,
      },
    });
  };

  const columns: ProColumns<API.Feature, 'text'>[] = [
    {
      title: '功能编号',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '功能名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '父级功能编号',
      dataIndex: 'parentCode',
      key: 'parentCode',
    },
    {
      title: '功能图标',
      dataIndex: 'icon',
      key: 'icon',
    },
    {
      title: '功能路径',
      dataIndex: 'path',
      key: 'path',
    },
    {
      title: '排序',
      dataIndex: 'orderIndex',
      key: 'orderIndex',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (!feature.list.length) {
      dispatch({
        type: 'feature/queryList',
        payload: {},
      });
    }
  }, [feature.list.length]);

  useEffect(() => {
    setModalVisible(false);
  }, [feature]);

  return (
    <Spin spinning={loading.models.feature}>
      <ProTable<API.Feature>
        rowKey="code"
        headerTitle="系统功能"
        columns={columns}
        dataSource={feature.treeData}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增功能
          </Button>,
        ]}
        search={false}
        pagination={false}
        options={false}
        tableStyle={{
          height: 'calc(100vh - 11.5rem)',
          overflowY: 'auto',
        }}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </Spin>
  );
};

export default connect(({ feature, loading }) => ({ feature, loading }))(
  FeaturesPage,
);
