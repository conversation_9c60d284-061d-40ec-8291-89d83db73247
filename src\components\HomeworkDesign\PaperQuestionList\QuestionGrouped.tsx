import { convertNumber, getQueryParams, swapArray } from '@/utils/calc';
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';
import { Dispatch } from 'react';
import styles from './index.less';
import QuestionPart from './QuestionPart';

const QuestionGrouped = ({
  item,
  index,
  showDuration = true,
  settingScore = false,
  setOpen,
  sortQuestion,
  actionType = false,
}: {
  item: any;
  index: number;
  setOpen: React.Dispatch<
    React.SetStateAction<{
      show: boolean;
      type: string;
      data: any;
    }>
  >;
  sortQuestion: Dispatch<any>;
  showDuration?: boolean;
  settingScore?: boolean;
  reolad?: () => void;
  actionType?: boolean;
}) => {
  const { isSysAdmin } = getQueryParams();
  const questionOption = [
    // {
    //   id: 'analysis',
    //   title: '查看解析',
    //   icon: <UnorderedListOutlined />,
    //   click: (data: any) => {
    //     setOpen({
    //       show: true,
    //       type: 'Analysis',
    //       data,
    //     });
    //   },
    // },
    {
      id: 'add',
      title: '新增试题',
      icon: <PlusOutlined />,
      click: (data: any) => {
        setOpen({
          show: true,
          type: 'add',
          data,
        });
      },
    },
    {
      id: 'edit',
      title: '改编',
      icon: <EditOutlined />,
      click: (data: any) => {
        setOpen({
          show: true,
          type: 'edit',
          data,
        });
      },
    },
    {
      id: 'similar',
      title: '更换试题',
      icon: <ReloadOutlined />,
      click: (data: any) => {
        setOpen({
          show: true,
          type: 'Similar',
          data,
        });
      },
    },
    {
      id: 'up',
      title: '上移',
      icon: <ArrowUpOutlined />,
      click: async (data: any) => {
        const { pId, index } = data;
        sortQuestion((prevData: any) => {
          const current = prevData?.bigQuestions?.[pId];
          const newQuestionIds: any = Array.from(current.questionIds);
          const newBigQuestion = {
            ...current,
            questionIds: swapArray(newQuestionIds, index, 'up'),
          };
          return {
            ...prevData,
            bigQuestions: {
              ...prevData.bigQuestions,
              [newBigQuestion.id]: newBigQuestion,
            },
          };
        });
      },
    },
    {
      id: 'down',
      title: '下移',
      icon: <ArrowDownOutlined />,
      click: async (data: any) => {
        const { pId, index } = data;
        sortQuestion((prevData: any) => {
          const current = prevData?.bigQuestions?.[pId];
          const newQuestionIds: any = Array.from(current.questionIds);
          const newBigQuestion = {
            ...current,
            questionIds: swapArray(newQuestionIds, index, 'down'),
          };
          return {
            ...prevData,
            bigQuestions: {
              ...prevData.bigQuestions,
              [newBigQuestion.id]: newBigQuestion,
            },
          };
        });
      },
    },
    {
      id: 'delete',
      title: '删除',
      icon: <DeleteOutlined />,
      click: async (data: any) => {
        const { pId, index } = data;
        sortQuestion((prevData: any) => {
          const newData = { ...prevData };
          const current = newData.bigQuestions[pId];
          const updatedQuestionIds = swapArray(
            current.questionIds,
            index,
            'remove',
          );
          if (
            updatedQuestionIds?.length === 0 &&
            pId.indexOf('hidden') === -1
          ) {
            newData.bigQuestionOrder = newData.bigQuestionOrder.filter(
              (id: string) => id !== pId,
            );
            delete newData.bigQuestions[pId];
          } else {
            current.questionIds = updatedQuestionIds;
          }
          return newData;
        });
      },
    },
  ];
  const questionPartOption = [
    {
      id: 'delete',
      title: '删除',
      icon: <DeleteOutlined />,
      click: async (data: any) => {
        sortQuestion((prevData: any) => {
          const newBigQuestionOrder = prevData.bigQuestionOrder.filter(
            (id: string) => id !== data.id,
          );
          const newBigQuestions = { ...prevData.bigQuestions };
          delete newBigQuestions[data.id];
          return {
            ...prevData,
            bigQuestionOrder: newBigQuestionOrder,
            bigQuestions: newBigQuestions,
          };
        });
      },
    },
    {
      id: 'up',
      title: '上移',
      icon: <ArrowUpOutlined />,
      click: async (data: any) => {
        const { index } = data;
        sortQuestion((prevData: any) => {
          const { bigQuestionOrder } = prevData;
          return {
            ...prevData,
            bigQuestionOrder: swapArray(bigQuestionOrder, index, 'up'),
          };
        });
      },
    },
    {
      id: 'down',
      title: '下移',
      icon: <ArrowDownOutlined />,
      click: async (data: any) => {
        const { index } = data;
        sortQuestion((prevData: any) => {
          const { bigQuestionOrder } = prevData;
          return {
            ...prevData,
            bigQuestionOrder: swapArray(bigQuestionOrder, index, 'down'),
          };
        });
      },
    },
  ];
  return (
    <div
      key={item?.id}
      className={classNames(
        styles.questionPart,
        actionType ? styles.border : '',
        index === -1 ? styles.hideUp : '',
      )}
    >
      {index !== -1 && (
        <div className={styles.questionPartTitle}>
          {convertNumber(index + 1)}、 {item.name}（共{item.children?.length}
          小题） {settingScore ? `（ ${item?.score || 0} 分）` : ''}
        </div>
      )}
      <div>
        {item?.children?.map((val: any, ind: number) => {
          return (
            <div
              key={val?._id || ind}
              className={styles.questionItem}
              onMouseOver={(e) => {
                if (actionType) {
                  if (e.currentTarget?.parentElement?.parentElement) {
                    e.currentTarget.parentElement.parentElement.classList.remove(
                      `${styles.border}`,
                    );
                  }
                }
              }}
              onMouseOut={(e) => {
                if (actionType) {
                  if (e.currentTarget?.parentElement?.parentElement) {
                    e.currentTarget.parentElement.parentElement.classList.add(
                      `${styles.border}`,
                    );
                  }
                }
              }}
            >
              <QuestionPart
                showDuration={showDuration}
                settingScore={settingScore}
                data={{
                  question: val,
                  ind,
                }}
              />
              {actionType && (
                <ul className={styles.questionOperation}>
                  {questionOption?.map((tar) => {
                    const disabled =
                      (ind === 0 && tar.id === 'up') ||
                      (ind === item?.children?.length - 1 &&
                        tar.id === 'down') ||
                      (isSysAdmin !== 'true' &&
                        val?.tableName === 'system' &&
                        tar.id === 'edit');

                    return (
                      <li
                        key={tar.id}
                        style={
                          !showDuration &&
                          (tar.id === 'edit' || tar.id === 'add')
                            ? {
                                display: 'none',
                              }
                            : disabled
                            ? {
                                color: 'hsla(0, 0%, 100%, .5)',
                                pointerEvents: 'none',
                              }
                            : {}
                        }
                        onClick={(e) => {
                          if (!disabled) {
                            e.stopPropagation();
                            if (tar.id === 'add') {
                              const {
                                grade,
                                gradeSection,
                                subject,
                                textbookVersion,
                                volume,
                                catalog,
                              } = val;

                              (tar as any).click({
                                questionOrderId: item?.id,
                                grade,
                                gradeSection,
                                subject,
                                textbookVersion,
                                volume,
                                catalog,
                              });
                            } else if (tar.id === 'edit') {
                              (tar as any).click({
                                questionOrderId: item?.id,
                                ...val,
                              });
                            } else {
                              (tar as any).click({
                                question: val,
                                ids: val.ids,
                                pId: item?.id,
                                index: ind,
                                planId: item?.planId,
                              });
                            }
                          }
                        }}
                      >
                        {tar.icon}
                        {''}
                        {tar.title}
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>
          );
        })}
      </div>
      {index !== -1 && actionType && (
        <ul className={styles.questionOperation}>
          {questionPartOption?.map((tar) => {
            return (
              <li key={tar.id} onClick={() => (tar as any).click(item)}>
                {tar.icon}
                {''}
                {tar.title}
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
};

export default QuestionGrouped;
