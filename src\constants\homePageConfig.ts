import {
  AppstoreOutlined,
  BookOutlined,
  BuildOutlined,
  CalendarOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  OrderedListOutlined,
  ReadOutlined,
  SafetyOutlined,
  ScheduleOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';

/**
 * 首页功能卡片配置
 */
export interface FeatureCardConfig {
  code: string; // 功能编号
  name: string; // 显示名称
  icon: any; // 图标组件
  color: string; // 图标颜色
  title?: string; // 悬浮提示
  span?: number; // 栅格占位
}

/**
 * 首页功能区域配置
 */
export interface FeatureSectionConfig {
  title: string; // 区域标题
  parentCode?: string; // 父级功能编号（用于权限判断）
  cards: FeatureCardConfig[]; // 功能卡片列表
  cardStyle?: 'normal' | 'large'; // 卡片样式
  gutter?: [number, number]; // 间距
}

/**
 * 常用功能配置
 */
export const COMMON_FEATURES: FeatureCardConfig[] = [
  {
    code: '040100',
    name: '学科管理',
    icon: BookOutlined,
    color: '#1677ff',
    title: '义务教育学科管理',
    span: 4,
  },
  {
    code: '040300',
    name: '教材版本',
    icon: ReadOutlined,
    color: '#fa8c16',
    title: '教材版本管理',
    span: 4,
  },
  {
    code: '040500',
    name: '章节目录',
    icon: OrderedListOutlined,
    color: '#f5222d',
    title: '教材章节目录管理',
    span: 4,
  },
  {
    code: '070000',
    name: '作业范本',
    icon: FileTextOutlined,
    color: '#722ed1',
    title: '课时作业范本管理',
    span: 4,
  },
  {
    code: '080000',
    name: '检测范本',
    icon: ScheduleOutlined,
    color: '#13c2c2',
    title: '达标检测范本管理',
    span: 4,
  },
  {
    code: '090000',
    name: '作业设计',
    icon: FileTextOutlined,
    color: '#52c41a',
    title: '课时作业设计管理',
    span: 4,
  },
  {
    code: '100000',
    name: '检测设计',
    icon: ScheduleOutlined,
    color: '#eb2f96',
    title: '达标检测设计管理',
    span: 4,
  },
  {
    code: '110000',
    name: '作业组卷',
    icon: AppstoreOutlined,
    color: '#faad14',
    title: '作业组卷管理',
    span: 4,
  },
];

/**
 * 功能区域配置
 */
export const FEATURE_SECTIONS: FeatureSectionConfig[] = [
  {
    title: '基础数据管理',
    parentCode: '980000',
    cardStyle: 'large',
    gutter: [16, 16],
    cards: [
      {
        code: '980100',
        name: '学年学期',
        icon: CalendarOutlined,
        color: '#1890ff',
        title: '学年学期管理',
        span: 6,
      },
      {
        code: '980200',
        name: '行政区划',
        icon: AppstoreOutlined,
        color: '#52c41a',
        title: '行政区划管理',
        span: 6,
      },
      {
        code: '980300',
        name: '数据字典',
        icon: DatabaseOutlined,
        color: '#fa8c16',
        title: '数据字典管理',
        span: 6,
      },
      {
        code: '980400',
        name: '题型维护',
        icon: DatabaseOutlined,
        color: '#722ed1',
        title: '题型维护管理',
        span: 6,
      },
    ],
  },
  {
    title: '系统设置',
    parentCode: '990000',
    cardStyle: 'large',
    gutter: [16, 16],
    cards: [
      {
        code: '990100',
        name: '单位管理',
        icon: BuildOutlined,
        color: '#1890ff',
        title: '单位管理',
        span: 6,
      },
      {
        code: '990200',
        name: '用户管理',
        icon: UserOutlined,
        color: '#52c41a',
        title: '用户管理',
        span: 6,
      },
      {
        code: '990300',
        name: '角色管理',
        icon: TeamOutlined,
        color: '#fa8c16',
        title: '角色管理',
        span: 6,
      },
      {
        code: '990400',
        name: '权限维护',
        icon: SafetyOutlined,
        color: '#722ed1',
        title: '权限点维护',
        span: 6,
      },
      {
        code: '990500',
        name: '系统功能',
        icon: AppstoreOutlined,
        color: '#eb2f96',
        title: '系统功能维护',
        span: 6,
      },
    ],
  },
];
