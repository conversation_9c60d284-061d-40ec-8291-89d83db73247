import DefaultImg from '@/components/DefaultImg';
import { EnterpriseState } from '@/models/enterprise';
import { RoleState } from '@/models/role';
import {
  create as createUser,
  index as getUserList,
  remove as removeUser,
  update as updateUser,
} from '@/services/users';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { AnyAction, connect, Dispatch, useModel } from '@umijs/max';
import {
  Button,
  Divider,
  Image,
  message,
  Popconfirm,
  Select,
  Space,
  Switch,
  Tag,
  theme,
  Tooltip,
} from 'antd';
import { useRef, useState } from 'react';
import BindRoles from './BindRoles';
import EditModal from './EditModal';

const Users: React.FC<{
  role: RoleState;
  enterprise: EnterpriseState;
  dispatch: Dispatch<AnyAction>;
}> = ({ role, enterprise, dispatch }) => {
  const { initialState } = useModel('@@initialState');
  const { token } = theme.useToken();
  const actionRef = useRef<ActionType>();
  const [current, setCurrent] = useState<API.User>();
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [bindRoleModalVisible, setBindRoleModalVisible] =
    useState<boolean>(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  const columns: ProColumns<API.User>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
      hidden: true,
    },
    {
      dataIndex: 'avatar',
      hideInSearch: true,
      align: 'center',
      render: (_, record) => {
        return (
          <Image
            src={record.avatar}
            alt="avatar"
            style={{ width: 40, height: 40, borderRadius: '50%' }}
            fallback={DefaultImg}
          />
        );
      },
    },
    {
      title: '用户名',
      dataIndex: 'username',
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      hideInSearch: true,
    },
    {
      title: '角色',
      dataIndex: 'role',
      filters: role.list.map((item) => ({
        text: item.name,
        value: item.id,
      })),
      render: (_, record) => {
        const roles = record.roles || [];
        const sliceNum = 5;
        return (
          <Tooltip
            title={
              roles.length > sliceNum
                ? roles.map((r) => <div key={r.id}>{r.name}</div>)
                : null
            }
          >
            <Space>
              {roles.slice(0, sliceNum).map((r) => (
                <Tag key={r.id}>{r.name}</Tag>
              ))}
              {roles.length > sliceNum && <Tag key="more">...</Tag>}
            </Space>
          </Tooltip>
        );
      },
      search: false,
    },
    {
      title: '所属单位',
      dataIndex: 'enterprise_id',
      render: (_, record) => record.enterprise?.name || '-',
      renderFormItem: () => (
        <Select
          showSearch
          allowClear
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options={enterprise.list.map((item) => ({
            label: item.name,
            value: item.id,
          }))}
        />
      ),
    },
    {
      title: '所属学段',
      dataIndex: 'grade_section_name',
    },
    {
      title: '所属年级',
      dataIndex: 'grade',
      render: (_, record: any) => {
        return (
          <Space wrap>
            {record?.grade?.map((v: string) => {
              return <Tag key={v}>{v}</Tag>;
            })}
          </Space>
        );
      },
    },
    {
      title: '任教学科',
      dataIndex: 'subject_name',
    },
    {
      title: '是否激活',
      dataIndex: 'isActive',
      align: 'center',
      hideInSearch: true,
      render: (_, record) => {
        return (
          <Switch
            checked={record.isActive}
            size="small"
            onChange={async (checked) => {
              const { errCode, msg } = await updateUser(record.id, {
                isActive: checked,
              });
              if (errCode) {
                message.error(msg);
              } else {
                actionRef.current?.reload();
                message.success(`已${checked ? '激活' : '禁用'}`);
              }
            }}
            disabled={
              record.username === 'admin' ||
              record.username === initialState?.username
            }
          />
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      align: 'center',
      render: (_text, record) => {
        return (
          <Space split={<Divider type="vertical" />}>
            <EditOutlined
              key="editable"
              title="编辑"
              onClick={() => {
                setCurrent(record);
                setEditModalVisible(true);
              }}
              style={{ color: token.colorPrimary }}
            />
            <TeamOutlined
              key="role"
              title="设置角色"
              onClick={() => {
                setCurrent(record);
                setBindRoleModalVisible(true);
              }}
              style={{ color: token.colorPrimary }}
            />
            {record.username !== 'admin' &&
              record.username !== initialState?.username && (
                <Popconfirm
                  key="delete"
                  title="确认删除？"
                  onConfirm={async () => {
                    const { errCode, msg } = await removeUser(record.id);
                    if (errCode) {
                      message.error(msg);
                      return;
                    }
                    message.success('删除成功');
                    actionRef.current?.reload();
                  }}
                >
                  <DeleteOutlined
                    title="删除"
                    style={{ color: token.colorError }}
                  />
                </Popconfirm>
              )}
          </Space>
        );
      },
    },
  ];

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的用户');
      return;
    }
    const promises = selectedRowKeys.map((id) => removeUser(id));
    const results = await Promise.all(promises);
    const hasError = results.some((res) => res.errCode);
    if (hasError) {
      message.error('部分用户删除失败');
    } else {
      message.success('批量删除成功');
    }
    actionRef.current?.reload();
    setSelectedRowKeys([]);
  };

  return (
    <div>
      <ProTable<API.User>
        rowKey="id"
        headerTitle="用户列表"
        columns={columns}
        actionRef={actionRef}
        options={false}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => {
            setSelectedRowKeys(keys as number[]);
          },
          getCheckboxProps: (record) => ({
            disabled:
              record.username === 'admin' ||
              record.username === initialState?.username,
          }),
        }}
        request={async (params = {}, sort, filter) => {
          const { errCode, msg, data } = await getUserList({
            ...params,
            sort,
            filter,
          });
          if (errCode) {
            message.error(msg);
            return {
              success: false,
              total: 0,
              data: [],
            };
          }
          // 如果current有值，这里就更新一下，界面中已经打开的子组件就能实时更新
          if (current) {
            setCurrent(
              (data?.list || []).find((item) => item.id === current.id),
            );
          }
          return {
            success: true,
            total: data?.total || 0,
            data: data?.list || [],
          };
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setCurrent(undefined);
              setEditModalVisible(true);
            }}
            type="primary"
          >
            新建
          </Button>,
          <Button
            key="batchDelete"
            icon={<DeleteOutlined />}
            onClick={handleBatchDelete}
            disabled={selectedRowKeys.length === 0}
            danger
          >
            批量删除
          </Button>,
        ]}
      />
      <EditModal
        info={current}
        open={editModalVisible}
        onSave={async (info: API.User) => {
          let res;
          if (info.id) {
            res = await updateUser(info.id, info);
          } else {
            res = await createUser(info);
          }
          if (res.errCode) {
            message.error(res.msg);
            return;
          }
          message.success('保存成功');
          actionRef?.current?.reload();
          setEditModalVisible(false);
        }}
        onClose={() => setEditModalVisible(false)}
      />
      <BindRoles
        open={bindRoleModalVisible}
        current={current}
        saveHandl={() => {
          dispatch({ type: 'role/queryList', payload: {} });
          actionRef.current?.reload();
        }}
        cancleHandl={() => {
          setBindRoleModalVisible(false);
        }}
      />
    </div>
  );
};

export default connect(({ role, enterprise }) => ({
  role,
  enterprise,
}))(Users);
