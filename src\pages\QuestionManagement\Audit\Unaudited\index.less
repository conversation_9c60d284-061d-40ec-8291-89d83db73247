.wapper {
  min-height: 400px;

  .topicContent {
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 10%);
    overflow: hidden;
    transition: max-height 0.3s ease;
    position: relative;
    margin-bottom: 16px;

    img {
      max-width: 100%;
      cursor: pointer;
    }

    .header {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
    }

    .footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 50px;
      background: linear-gradient(
        to top,
        #fff 0%,
        rgba(255, 255, 255, 0%) 100%
      );
      display: flex;
      align-items: flex-end;
      justify-content: center;
      padding-bottom: 10px;
    }
  }
}

.options {
  line-height: 40px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
