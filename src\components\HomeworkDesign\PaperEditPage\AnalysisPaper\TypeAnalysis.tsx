import TitleBar from '@/components/TitleBar';
import { MenuUnfoldOutlined } from '@ant-design/icons';
import { Table } from 'antd';

const columns = [
  {
    title: '题型',
    dataIndex: 'name',
    key: 'name',
    width: '160px',
    ellipsis: true,
  },
  {
    title: '题目数量',
    dataIndex: 'count',
    key: 'count',
    render: (_: any, record: any) => {
      return (
        <span>
          {record.count}（{record.rate}%）
        </span>
      );
    },
  },
];
const TypeAnalysis = ({ data }: { data: any }) => {
  return (
    <div>
      <TitleBar
        prefix={false}
        title={
          <>
            <MenuUnfoldOutlined />
            题型分析
          </>
        }
        actionDom={
          <div>
            <span>{data?.typeCoverage ? data?.typeCoverage + '%' : ''}</span>
          </div>
        }
      />
      <Table
        bordered
        size="small"
        dataSource={data?.type || []}
        columns={columns}
        pagination={false}
      />
    </div>
  );
};

export default TypeAnalysis;
