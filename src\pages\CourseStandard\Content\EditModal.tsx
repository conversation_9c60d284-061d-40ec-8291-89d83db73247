import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<any>
      width={560}
      title={info ? '编辑课程内容' : '新增课程内容'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText
        name="name"
        label="名称"
        fieldProps={{ maxLength: 100 }}
        rules={[{ required: true, message: '请输入名称！' }]}
      />
      <ProFormTextArea
        name="describe"
        label="描述"
        rules={[{ required: true, message: '请输入描述！' }]}
      />
      <ProFormTextArea name="analy" label="解析" />
    </ModalForm>
  );
};

export default EditModal;
