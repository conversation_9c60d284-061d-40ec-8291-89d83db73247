import DesignCourseTime from '@/pages/CoursePlan/DesignCourseTime';
import { index as cityConceptAPI } from '@/services/city_homework_concept';
import { index as cityFormAPI } from '@/services/city_homework_form';
import { index as cityPrincipleAPI } from '@/services/city_homework_principle';
import { index as requirementAPI } from '@/services/city_homework_requirement';
import { index as citySuggestionAPI } from '@/services/city_homework_suggestion';
import { index as cityTypeAPI } from '@/services/city_homework_type';
import { index } from '@/services/course_plan_admin_target';
import {
  index as implementAPI,
  index as principleAPI,
} from '@/services/course_plan_course_carry';
import { index as standardAPI } from '@/services/course_plan_course_system';
import { index as subjectAPI } from '@/services/course_plan_subject';
import { index as appendixAPI } from '@/services/course_standard_appendix';
import { index as characterAPI } from '@/services/course_standard_character';
import { index as contentAPI } from '@/services/course_standard_content';
import { index as standardIdeaAPI } from '@/services/course_standard_idea';
import { index as GoalAPI } from '@/services/course_standard_objective';
import { index as qualityAPI } from '@/services/course_standard_quality';
import { index as settingAPI } from '@/services/plan_course_setting';
import { index as mechanismAPI } from '@/services/plan_implementation_mechanism';
import { index as conceptAPI } from '@/services/province_homework_concept';
import { index as provinceGoalAPI } from '@/services/province_homework_goal';
import { index as implementPointAPI } from '@/services/province_homework_implement';
import { index as basicWorkAPI } from '@/services/province_homework_type';

import Case from '@/pages/MunicipalDesign/Case';
import { FileSearchOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Card, Col, FloatButton, message, Modal, Row, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import CustomMenu from './CustomMenu';
import styles from './index.less';

const CommonViewData = (
  data: any[],
  fields: string[] = ['name', 'describe', ''],
) => {
  if (!data || data.length === 0) {
    return <div>暂无数据</div>;
  }
  return (
    <div style={{ paddingRight: '10px' }}>
      {data?.map((item: any) => (
        <div key={item.id} style={{ marginBottom: '20px' }}>
          <h4>
            {item[fields[0]]} {item[fields[2]]}
          </h4>
          <div style={{ color: '#666' }}>{item[fields[1]]}</div>
        </div>
      ))}
    </div>
  );
};
const GuidanceModalButton: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedKey, setSelectedKey] = useState('1-1');
  const [content, setContent] = useState<React.ReactNode>(null);
  const [openKeys, setOpenKeys] = useState(['1']);

  const items: MenuProps['items'] = [
    {
      label: '课程方案',
      key: '1',
      children: [
        { label: '培养目标', key: '1-1' },
        { label: '方案原则', key: '1-2' },
        { label: '课程学制', key: '1-3' },
        { label: '课程科目', key: '1-4' },
        { label: '课程设置', key: '1-5' },
        { label: '教学时间', key: '1-6' },
        { label: '实施机制', key: '1-7' },
      ],
    },
    {
      label: '课程标准',
      key: '2',
      children: [
        { label: '课程性质', key: '2-1' },
        { label: '课程理念', key: '2-2' },
        { label: '课程目标', key: '2-3' },
        { label: '课程内容', key: '2-4' },
        { label: '学业质量', key: '2-5' },
        { label: '课程实施', key: '2-6' },
        { label: '课标附录', key: '2-7' },
      ],
    },
    {
      label: '省级作业设计实施',
      key: '3',
      children: [
        {
          label: '作业目标',
          key: '3-1',
        },
        {
          label: '作业理念',
          key: '3-2',
        },
        {
          label: '基础型作业',
          key: '3-3',
        },
        {
          label: '发展型作业',
          key: '3-4',
        },
        {
          label: '扩展型作业',
          key: '3-5',
        },
        {
          label: '课程实施要点',
          key: '3-6',
        },
      ],
    },
    {
      label: '市级作业设计实施',
      key: '4',
      children: [
        {
          label: '学科设计理念',
          key: '4-1',
        },
        {
          label: '学科设计要求',
          key: '4-2',
        },
        {
          label: '学科设计原则',
          key: '4-3',
        },
        {
          label: '设计类信息管理',
          key: '4-4',
        },
        {
          label: '设计作业形式',
          key: '4-5',
        },
        {
          label: '作业设计建议',
          key: '4-6',
        },
        {
          label: '典型案例集',
          key: '4-7',
        },
      ],
    },
  ];

  const getTarget = async () => {
    const { errCode, data, msg } = await index({});
    if (errCode) {
      return message.error(msg || '培养目标列表查询失败');
    }
    setContent(CommonViewData(data.list ?? []));
  };

  const getPrinciple = async () => {
    const { errCode, data, msg } = await principleAPI({});
    if (errCode) {
      return message.error(msg || '方案原则列表查询失败');
    }
    setContent(CommonViewData(data.list ?? []));
  };

  /** 学科学制 */
  const getCourseSystem = async () => {
    const { errCode, data, msg } = await standardAPI({});
    if (errCode) {
      return message.error(msg || '学科学制列表查询失败');
    }
    const groupedData = data.list?.reduce(
      (acc: Record<string, any[]>, item) => {
        if (!acc[item.type]) {
          acc[item.type] = [];
        }
        acc[item.type].push(item);
        return acc;
      },
      {},
    );

    setContent(
      <div style={{ padding: '16px', width: '100%' }}>
        {Object.entries(groupedData ?? []).map(([type, items]) => (
          <div key={type} style={{ marginBottom: '24px' }}>
            <h3>{type}</h3>
            <Space>
              {items.map((item) => (
                <div key={item.id}>
                  <div>{item.system}</div>
                </div>
              ))}
            </Space>
          </div>
        ))}
      </div>,
    );
  };

  /** 课程科目 */
  const getCourseSubject = async () => {
    const { errCode, data, msg } = await subjectAPI({});
    if (errCode) {
      return message.error(msg || '课程科目列表查询失败');
    }
    setContent(
      <div style={{ padding: '16px', width: '100%' }}>
        <Row gutter={[16, 16]}>
          {data.list?.map((item: any) => (
            <Col span={8} key={item.id}>
              <Card
                size="small"
                title={item.subject_name}
                style={{ height: '100%' }}
              >
                <div style={{ color: '#666' }}>
                  <p>
                    状态: &nbsp;
                    {item.is_used ? (
                      <Tag color="green">已启用</Tag>
                    ) : (
                      <Tag color="green">已禁用</Tag>
                    )}
                  </p>
                  <p>{dayjs(item.createdAt).format('YYYY-MM-DD HH:mm')}</p>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>,
    );
  };

  /** 课程设置 */
  const getCourseSetting = async () => {
    const { errCode, data, msg } = await settingAPI({});
    if (errCode) {
      return message.error(msg || '课程设置列表查询失败');
    }

    setContent(
      <div style={{ padding: '16px', width: '100%' }}>
        <Row gutter={[16, 16]}>
          {data.list?.map((item: any) => (
            <Col span={12} key={item.id}>
              <Card
                size="small"
                title={`${item.course_name} (${item.type})`}
                style={{ height: '100%' }}
              >
                <div style={{ color: '#666' }}>
                  <p>适用年级: {item.grade_name}</p>
                  <p>创建时间: {dayjs(item.createdAt).format('YYYY-MM-DD')}</p>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>,
    );
  };

  /** 实施时机 */
  const getMechanism = async () => {
    const { errCode, data, msg } = await mechanismAPI({});
    if (errCode) {
      return message.error(`实施时机列表查询失败 ${msg}`);
    }
    setContent(CommonViewData(data.list ?? [], ['name', 'mechanism_content']));
  };

  /** 课程性质 */
  const getCourseNature = async () => {
    const { errCode, data, msg } = await characterAPI({});
    if (errCode) {
      return message.error(`课程性质列表查询失败 ${msg}`);
    }
    setContent(CommonViewData(data.list ?? []));
  };

  /** 课程理念 */
  const getCourseConcept = async () => {
    const { errCode, data, msg } = await standardIdeaAPI({});
    if (errCode) {
      return message.error(`课程理念列表查询失败 ${msg}`);
    }
    setContent(CommonViewData(data.list ?? []));
  };
  /** 课程目标 */
  const getCourseGoal = async () => {
    const { errCode, data, msg } = await GoalAPI({});
    if (errCode) {
      return message.error(`课程目标列表查询失败 ${msg}`);
    }
    setContent(CommonViewData(data.list ?? []));
  };
  /** 课程内容 */
  const getCourseContent = async () => {
    const { errCode, data, msg } = await contentAPI({});
    if (errCode) {
      return message.error(`课程内容列表查询失败 ${msg}`);
    }
    setContent(CommonViewData(data.list ?? []));
  };
  /** 学业质量 */
  const getQuality = async () => {
    const { errCode, data, msg } = await qualityAPI({});
    if (errCode) {
      return message.error(`学业质量列表查询失败 ${msg}`);
    }
    setContent(CommonViewData(data.list ?? []));
  };
  /** 课程实施 */
  const getCourseImplement = async () => {
    const { errCode, data, msg } = await implementAPI({});
    if (errCode) {
      return message.error(`课程实施列表查询失败 ${msg}`);
    }
    setContent(CommonViewData(data.list ?? []));
  };
  /** 课标附录 */
  const getAppendix = async () => {
    const { errCode, data, msg } = await appendixAPI({});
    if (errCode) {
      return message.error(`课标附录列表查询失败 ${msg}`);
    }
    setContent(CommonViewData(data.list ?? []));
  };
  /** 省级作业目标 */
  const getProvinceGoal = async () => {
    const { errCode, data, msg } = await provinceGoalAPI({});
    if (errCode) {
      return message.error(`省级作业目标列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'stage_name',
        'goal_description',
        'subject_name',
      ]),
    );
  };
  /** 省级作业理念 */
  const getProvinceConcept = async () => {
    const { errCode, data, msg } = await conceptAPI({});
    if (errCode) {
      return message.error(`省级作业理念列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'stage_name',
        'design_concept',
        'subject_name',
      ]),
    );
  };
  /** 基础型作业 */
  const getWork = async (type: string) => {
    const { errCode, data, msg } = await basicWorkAPI({
      type,
    });
    if (errCode) {
      return message.error(`${type}作业列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'stage_name',
        'description',
        'subject_name',
      ]),
    );
  };

  /** 实施要点 */
  const getImplementPoint = async () => {
    const { errCode, data, msg } = await implementPointAPI({});
    if (errCode) {
      return message.error(`实施要点列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'stage_name',
        'implementation_points',
        'subject_name',
      ]),
    );
  };
  /** 市级作业设计理念 */
  const getCityDesignIdea = async () => {
    const { errCode, data, msg } = await cityConceptAPI({});
    if (errCode) {
      return message.error(`市级作业设计理念列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'grade_section_name',
        'design_detail',
        'subject_name',
      ]),
    );
  };
  /** 市级学科设计要求 */
  const getCityDesignRequirement = async () => {
    const { errCode, data, msg } = await requirementAPI({});
    if (errCode) {
      return message.error(`市级学科设计要求列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'grade_section_name',
        'require_detail',
        'subject_name',
      ]),
    );
  };
  /** 市级学科设计原则 */
  const getCityDesignPrinciple = async () => {
    const { errCode, data, msg } = await cityPrincipleAPI({});
    if (errCode) {
      return message.error(`市级学科设计原则列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'grade_section_name',
        'principle_detail',
        'subject_name',
      ]),
    );
  };
  /** 市级设计类信息管理 */
  const getCityDesignManage = async () => {
    const { errCode, data, msg } = await cityTypeAPI({});
    if (errCode) {
      return message.error(`市级设计类信息管理列表查询失败 ${msg}`);
    }
    const groupedData = data.list?.reduce(
      (acc: Record<string, any[]>, item) => {
        const key = `${item.grade_section_name}_${item.subject_name}`;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      },
      {},
    );

    setContent(
      <div style={{ padding: '16px', width: '100%' }}>
        <Row gutter={[16, 16]}>
          {groupedData &&
            Object.entries(groupedData).map(([key, items]) => (
              <Col span={8} key={key}>
                <Card
                  size="small"
                  title={`${items[0].grade_section_name} ${items[0].subject_name}`}
                  style={{ height: '100%' }}
                >
                  <div style={{ color: '#666' }}>
                    {items.map((item) => (
                      <div key={item.id}>{item.name}</div>
                    ))}
                  </div>
                </Card>
              </Col>
            ))}
        </Row>
      </div>,
    );
  };
  /** 市级设计作业形式 */
  const getCityDesignForm = async () => {
    const { errCode, data, msg } = await cityFormAPI({});
    if (errCode) {
      return message.error(`市级设计作业形式列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'grade_section_name',
        'form_detail',
        'subject_name',
      ]),
    );
  };
  /** 市级作业设计建议 */
  const getCityDesignSuggest = async () => {
    const { errCode, data, msg } = await citySuggestionAPI({});
    if (errCode) {
      return message.error(`市级作业设计建议列表查询失败 ${msg}`);
    }
    setContent(
      CommonViewData(data.list ?? [], [
        'grade_section_name',
        'suggest_detail',
        'subject_name',
      ]),
    );
  };

  const onClick = (e: { key: string }) => {
    setSelectedKey(e.key);
    switch (e.key) {
      case '1-1':
        getTarget();
        break;
      case '1-2':
        getPrinciple();
        break;
      case '1-3':
        getCourseSystem();
        break;
      case '1-4':
        getCourseSubject();
        break;
      case '1-5':
        getCourseSetting();
        break;
      case '1-6':
        setContent(<DesignCourseTime />);
        break;
      case '1-7':
        getMechanism();
        break;
      case '2-1':
        getCourseNature();
        break;
      case '2-2':
        getCourseConcept();
        break;
      case '2-3':
        getCourseGoal();
        break;
      case '2-4':
        getCourseContent();
        break;
      case '2-5':
        getQuality();
        break;
      case '2-6':
        getCourseImplement();
        break;
      case '2-7':
        getAppendix();
        break;
      case '3-1':
        getProvinceGoal();
        break;
      case '3-2':
        getProvinceConcept();
        break;
      case '3-3':
        getWork('基础型');
        break;
      case '3-4':
        getWork('发展型');
        break;
      case '3-5':
        getWork('拓展型');
        break;
      case '3-6':
        getImplementPoint();
        break;
      case '4-1':
        getCityDesignIdea();
        break;
      case '4-2':
        getCityDesignRequirement();
        break;
      case '4-3':
        getCityDesignPrinciple();
        break;
      case '4-4':
        getCityDesignManage();
        break;
      case '4-5':
        getCityDesignForm();
        break;
      case '4-6':
        getCityDesignSuggest();
        break;
      case '4-7':
        setContent(<Case />);
        break;
      default:
        setContent(null);
        break;
    }
  };

  const onOpenChange: MenuProps['onOpenChange'] = (keys) => {
    setOpenKeys(keys);
  };

  useEffect(() => {
    if (selectedKey && modalOpen) {
      onClick({ key: selectedKey });
    }
  }, [modalOpen]);

  return (
    <>
      <FloatButton
        type="primary"
        icon={<FileSearchOutlined />}
        onClick={() => {
          setModalOpen(true);
          setSelectedKey('1-1');
          setOpenKeys(['1']);
          setContent(null);
        }}
      />

      <Modal
        title="帮助文档"
        open={modalOpen}
        onCancel={() => {
          setModalOpen(false);
        }}
        footer={null}
        width={1000}
        height={600}
        styles={{
          body: {
            height: '100%',
            minHeight: '520px',
          },
        }}
      >
        <Row gutter={16}>
          <Col span={8}>
            <div className={styles.content}>
              <CustomMenu
                key={selectedKey}
                onClick={onClick}
                activeKey={selectedKey}
                openKeys={openKeys}
                onOpenChange={onOpenChange}
                items={items}
              />
            </div>
          </Col>
          <Col span={16}>
            <div className={styles.content}>{content}</div>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
export default GuidanceModalButton;
