/* eslint-disable */
import { convertNumber, convertSpecialNumber } from '@/utils/calc';
import { pageData } from './defaultSetting';
/**
 * 加载图像,返回一个Promise数组，当所有图像都加载完成后解析
 */
const loadImg = (e: any[]) => {
  if (0 === e.length) return !0;
  const t: any[] = [];
  return (
    e.forEach((e) => {
      const n = document.createElement('img');
      (n.src = e),
        t.push(
          new Promise((e) => {
            (n.onload = () => e(!0)), (n.onerror = (t) => e(t));
          }),
        );
    }),
    Promise.all(t)
  );
};

/**
 * 试题格式转化
 * @param curQuz 当前题目对象
 * @param num
 */
const convertQuz = (curQuz: any, num: number) => {
  let type, options;
  const { baseType, isCompose, _id } = curQuz || {};
  const { code } = baseType || {};
  if (isCompose) {
    type = 'parent';
    const { children } = curQuz;
    for (let index = 0; index < children.length; index++) {
      const curQuz = children[index];
      convertQuz(curQuz, index);
    }
  }
  switch (code) {
    case 'SINGLE_CHOICE':
    case 'MULTIPLE_CHOICE':
      type = 'options';
      options = curQuz.options.map((v: any, index: number) => {
        const name = String.fromCharCode(64 + index + 1);
        return {
          content: Object.values(v)[0],
          name: name,
          key: _id + name,
        };
      });
      (curQuz.lineNumber = 1), (curQuz.options = options);
      break;
    // case 'FILL_IN_THE_BLANK':
    //   type = 'fill';
    //   break;
    case 'SUBJECTIVE':
      type = 'subjective';
      break;
    case 'XIE_ZUO_TI':
      type = 'chineseWriting';
      break;
    case '连线题':
      type = 'matching';
      break;
    default:
      type = 'content';
      break;
  }
  (curQuz.key = curQuz.id || curQuz._id), (curQuz.desctype = type);
  (curQuz.num = num + 1), (curQuz.content = curQuz.name);
};
/**
 * 根据结构数据和题目列表，转换题目数据为适合答题卡展示的格式数据
 * @param structure 结构数据
 * @param list 试题列表
 * @returns 适合答题卡展示的格式数据
 */
export const convertData = (structure: any, list: any) => {
  if (!structure || !list) return;
  const layout = [];
  const isHidden = structure?.find(
    (v: { id: string }) => v?.id?.indexOf('hidden') !== -1,
  );
  for (let index = 0; index < structure.length; index++) {
    const { id, name, questionIds } = structure[index];
    let scoreTotal = 0;
    const groupedData = questionIds?.reduce(
      (acc: any, item: string, index: number) => {
        const curQz = list.find((v: { _id?: string }) => v?._id === item);
        convertQuz(curQz, index);
        scoreTotal += curQz?.score || 0;
        if (curQz?.isCompose) {
          const { name, children, _id } = curQz;
          acc.push(
            {
              desctype: 'content',
              content: name || '',
              name: name || '',
              key: _id + '.ftg',
              num: index + 1,
              parentId: _id,
            },
            ...children,
          );
        } else {
          acc.push({ ...curQz });
        }
        return acc;
      },
      [],
    );
    layout.push(
      {
        id,
        desctype: 'questionName',
        questionName: name,
        hidden: isHidden?.id === id,
        num: isHidden ? convertNumber(index) : convertNumber(index + 1),
      },
      ...groupedData,
    );
  }

  return layout;
};
/**
 * dom计算高度，传入内容字符串和预加载容器元素
 * @param content 内容字符串
 * @param preloadRef 预加载容器元素
 * @returns dom计算高度值
 */
export const calculateHeight = async (
  content: any,
  preloadRef: HTMLDivElement | null,
) => {
  // 创建一个要计算高度的元素
  const container = document.createElement('div');
  container.innerHTML = content;
  // 设置样式以计算高度
  container.style.width = `${pageData.preloadWidth}px`;
  container.style.lineHeight = `${pageData.lineHeight}`;
  container.style.fontSize = `${pageData.templateFontSize}px`;

  // 先添加到DOM再获取图片
  preloadRef?.appendChild(container);

  // 获取所有图片并确保加载完成
  const images = Array.from(container.querySelectorAll('img'));
  const imageSrcs = images
    .map((img) => {
      // 处理可能存在的相对路径
      if (!img.src.startsWith('http') && !img.src.startsWith('data:')) {
        img.src = img.getAttribute('src') || '';
      }
      return img.src;
    })
    .filter(Boolean);

  if (imageSrcs.length > 0) {
    try {
      await loadImg(imageSrcs);
      // 强制重排确保高度计算准确
      container.offsetHeight;
    } catch (e) {
      console.error('图片加载失败:', e);
    }
  }

  const height = container.offsetHeight;
  container.remove();
  preloadRef && (preloadRef.innerHTML = '');
  return height;
};
/**
 * 根据分栏以及当前页码，判断是否需要减去头部标题高度，并返回内容高度值
 * @param e 当前页码
 * @returns 内容高度值
 */
const hasHead = (e: number) => {
  const { column: t } = pageData.currentLayout;
  return (e - 1) % (2 * t) === 0;
};
/**
 * 计算内容高度值，根据当前页码确认是否减去头部标题高度
 * @param e 当前页码
 * @param t
 * @returns
 */
const getContentHeightByPage = (e: number, t = !1) => {
  const n = hasHead(e),
    i =
      pageData.pageSize.h -
      2 * pageData.pagePadding[0] -
      pageData['pageNumberHeight'],
    r = 0;
  return n ? i - pageData.headHeight - r : i - r;
};
/**
 * 计算每行字数
 * @returns 每行字数
 */
const wordsData = () => {
  const e = pageData.preloadWidth - 2,
    t = Math.floor(e / 37.5);
  return t;
};
/**
 * 根据作文总字数计算高度值、行数以及每行的字数
 * @param e
 * @returns 返回对象，包含高度值、行数以及每行的字数
 */
const wordsToHeight = (e: number) => {
  const t = wordsData(),
    n = Math.ceil(e / t),
    i = 44.5 * n;
  return {
    height: i,
    rows: n,
    rowWords: t,
  };
};
export const heightToWords = (e: number) => {
  const t = e < 0 ? 0 : e,
    n = wordsData(),
    i = Math.floor(t / 44.5),
    r = i * n;
  return {
    words: r,
    rows: i,
    rowWords: n,
  };
};

export const composeContent = (
  paperData: any,
  ke: string,
  preloadRef: HTMLDivElement | null,
) => {
  var n;
  const i = paperData
    .flat()
    .filter((t: { key: string }) => t.key.split('-')[0] === ke);
  if (i.length > 1) {
    const el = document.createElement('div');
    (el.innerHTML = `<div class="split-content mce-content-body" style="width: ${pageData.preloadWidth}px; line-height: ${pageData.lineHeight};"></div>`),
      preloadRef && preloadRef.appendChild(el);
    const o: any = el.querySelector('.split-content');

    for (let t = 0, l = i.length; t < l; t++) {
      const e: any = o?.lastChild,
        r = i[t].content;
      if (e)
        if (
          null == (n = null == e ? void 0 : e?.classList)
            ? void 0
            : n.contains('rest-node')
        ) {
          e.removeAttribute('class');
          const t = document.createElement('div');
          t.innerHTML = r;
          const n = t.querySelector('.split-node');
          n &&
            ((e.innerHTML = e.innerHTML.replace('<wbr>', ' ') + n.innerHTML),
            n.parentElement?.removeChild(n),
            (o.innerHTML = o.innerHTML + t.innerHTML));
        } else o.innerHTML = o.innerHTML + r;
      else o.innerHTML = r;
    }
    const s = i.reduce((e, t) => e + t.height, 0),
      a = o.offsetHeight > s ? o.offsetHeight : s,
      c = o.innerHTML;
    return {
      content: c,
      height: a,
    };
  }
  const { content, height } = i[0];
  return {
    content,
    height,
  };
};
/**
 * web端富文本拆分逻辑
 * @param e
 * @param t
 * @param n
 * @param preloadRef
 * @returns
 */
const domTextSplit = async (
  e: any,
  t: number,
  n: number,
  preloadRef: HTMLDivElement | null,
) => {
  // 处理嵌套span的情况
  const handleNestedSpan: any = (node: Node) => {
    if (node.nodeType === 1 && (node as HTMLElement).tagName === 'SPAN') {
      const span = node as HTMLElement;
      if (span.querySelector('span')) {
        // 深度优先处理嵌套span
        const childNodes = Array.from(span.childNodes);
        return childNodes.flatMap((child) => handleNestedSpan(child));
      }
    }
    return [node];
  };
  const i = [...e.childNodes],
    r = i.reduce((e, t) => {
      let n = [];
      if (3 === t.nodeType) {
        if (new RegExp(/[\u4e00-\u9fa5]/).test(t.textContent))
          n = t.textContent.split('');
        else {
          const e = t.textContent.replace(/([^a-zA-Z])/g, '|@$1');
          n = e.split('|@');
        }
      } else {
        // n = [t]
        // 元素节点处理 - 添加嵌套span处理
        n = handleNestedSpan(t);
      }
      return [...e, ...n];
    }, []);

  let o = !1;
  const s = { content: '', h: t },
    a = { content: '', h: n - t };
  if ('TABLE' === e.tagName) {
    return (
      (a.content = e.outerHTML),
      (a.h = e.offsetHeight),
      { isSplit: o, restContent: s, splitContent: a }
    );
  }
  // 在创建临时容器时添加span换行样式
  const l = document.createElement('div');
  l.classList.add('copy-question');
  (l.innerHTML = `<div class="split-content mce-content-body" style="width: ${
    pageData.preloadWidth - 20
  }px; line-height: ${
    pageData.lineHeight
  }; white-space: pre-wrap;"><p style="word-break: break-word;"></p></div>`),
    preloadRef && preloadRef.appendChild(l);
  // 获取所有图像的src
  const es = Array.from(l.querySelectorAll('img')).map((e) => e.src);
  // 加载图像
  await loadImg(es);
  const u = e.getAttribute('style'),
    A: any = l.querySelector('p');
  return (
    u && A?.setAttribute('style', u),
    r.forEach((e: any) => {
      const n = 'string' === typeof e ? document.createTextNode(e) : e;
      A.appendChild(n),
        A.clientHeight <= t
          ? ((o = !0),
            (s.content +=
              'string' === typeof e
                ? e
                : e.outerHTML
                ? e.outerHTML.replace(/undefined/g, '')
                : ''))
          : (a.content +=
              'string' === typeof e
                ? e
                : e.outerHTML
                ? e.outerHTML.replace(/undefined/g, '')
                : '');
    }),
    // (a.content = a.content.replace(/^ /, '<wbr>')),
    { isSplit: o, restContent: s, splitContent: a }
  );
};

const splitDom = async (
  e: any,
  t: number,
  n: number,
  preloadRef: HTMLDivElement | null,
) => {
  const i = e.content,
    r = [],
    o = Object.assign({}, e, {
      key: e.key + '-1',
      height: t,
      totalHeight: e.height,
      isSplit: !0,
      splitPage: 0,
    }),
    s = document.createElement('div');
  (s.innerHTML = `<div style="width: ${pageData.preloadWidth}px; border: 1px solid;">${i}</div>`),
    preloadRef && preloadRef.appendChild(s);
  // 获取所有图像的src
  const es = Array.from(s.querySelectorAll('img')).map((e) => e.src);
  // 加载图像
  await loadImg(es);
  const l = 26,
    c = s.offsetHeight,
    u = c > (t - e.hasScoreBox ? l : 0),
    A = (e: number) => {
      let t = '',
        n = 6,
        i = !1;
      return (
        s.firstChild?.childNodes?.forEach((r: any) => {
          if (1 === r.nodeType) {
            const o = r.classList.contains('remove');
            n + r.offsetHeight < e && !o && !i
              ? ((t += r.outerHTML),
                r.classList.add('remove'),
                (n += r.offsetHeight))
              : o || (i = !0);
          }
        }),
        { content: t, totalEleHeight: n }
      );
    };
  if (u) {
    const n = t - (e.hasScoreBox ? l : 0),
      { content: i } = A(n);
    o.content = i;
  }
  r.push(o);
  const d = 2 * pageData.currentLayout.column,
    h = d - (n % pageData.currentLayout.column) - 1;
  let p = e.height - t;
  for (let t = 1; t <= h; t++) {
    const i = getContentHeightByPage(n + t, !0),
      o = Object.assign({}, e, {
        key: e.key + '-' + (t + 1),
        totalHeight: e.height,
        isSplit: !0,
        splitPage: t,
        maxSplitPage: t === h,
      });
    if (i > p) {
      if (u) {
        const { content: e, totalEleHeight: t } = A(i);
        t > p && (p = t), (o.content = e);
      } else o.content = '';
      return r.push(Object.assign({}, o, { height: p < 57 ? 57 : p })), r;
    }
    if (u) {
      const { content: e } = A(i);
      o.content = e;
    } else o.content = '';
    r.push(Object.assign({}, o, { height: i })), (p -= i);
  }
  return preloadRef && (preloadRef.innerHTML = ''), r;
};
const splitOptions = async (
  e: any,
  t: number,
  preloadRef: HTMLDivElement | null,
) => {
  if (e.options?.length) {
    let i = document.createElement('div');
    for (let u of e.options) {
      i.innerHTML += `<div class="option-row">${u}</div>`;
    }
    preloadRef && preloadRef.appendChild(i);
    // 获取所有图像的src
    const es = Array.from(i.querySelectorAll('img')).map((e) => e.src);
    // 加载图像
    await loadImg(es);
    let r = !1,
      o = 0,
      s = 0;
    return (
      Array.from(i?.childNodes)
        ?.filter((e: any) => e.classList && e.classList.contains('option-row'))
        ?.forEach((e: any, n) => {
          const i = e.offsetHeight + e.offsetTop;
          i <= t && !r ? ((o = n + 1), (s = i)) : (r = !0);
        }),
      [
        Object.assign({}, e, {
          isSplit: !0,
          splitPage: 0,
          splitRow: o,
          height: 0 === o ? 0 : s,
        }),
        Object.assign({}, e, {
          isSplit: !0,
          splitPage: 1,
          splitRow: o,
          height: e.height - s,
        }),
      ]
    );
  } else {
    return [];
  }
};
/**
 * 计算调整后的高度（考虑分数框）
 */
const calculateAdjustedHeight = (question: any, availableHeight: number) => {
  const scoreBoxHeight = question.hasScoreBox ? 26 : 0;
  return availableHeight - scoreBoxHeight;
};

/**
 * 更新第一个拆分片段的内容
 */
const updateFirstSplit = (firstSplit: any, splitContent: string) => {
  firstSplit.content = splitContent;
  if (!splitContent) {
    firstSplit.totalHeight -= 2 * firstSplit.height;
    firstSplit.height = 0;
  }
};

/**
 * 处理内容拆分的核心逻辑
 */
const processSplitContent = async (
  container: HTMLElement,
  maxHeight: number,
  preloadRef: HTMLDivElement | null,
) => {
  let currentPageContent = '';
  let accumulatedHeight = 6;
  let isSplitComplete = false;
  let maxFloatImageHeight = 0;

  const nodes: any[] = Array.from(container.firstChild?.childNodes || []);

  for (const node of nodes) {
    if (node.nodeType === 1) {
      let nodeHeight = node.offsetHeight;

      const floatImages = node.querySelectorAll('.float-img');
      floatImages.forEach((img: { offsetHeight: number }) => {
        if (img.offsetHeight > nodeHeight) {
          maxFloatImageHeight = img.offsetHeight;
        }
      });

      const isRemoved = node.classList.contains('remove');

      if (!isRemoved && !isSplitComplete) {
        if (accumulatedHeight + nodeHeight < maxHeight) {
          currentPageContent += node.outerHTML;
          node.classList.add('remove');
          accumulatedHeight += nodeHeight;
        } else {
          const remainingHeight = maxHeight - accumulatedHeight;
          const nodeRectHeight = node.getBoundingClientRect().height;

          if (remainingHeight > 25) {
            const splitResult = await domTextSplit(
              node,
              remainingHeight,
              nodeRectHeight,
              preloadRef,
            );
            if (splitResult.isSplit) {
              const clonedNode = node.cloneNode() as any;
              clonedNode.innerHTML = splitResult.restContent.content;
              clonedNode.classList.add('rest-node', 'remove');
              node.parentElement.insertBefore(clonedNode, node);
              currentPageContent += clonedNode.outerHTML;
              accumulatedHeight += clonedNode.offsetHeight;
              node.classList.add('split-node');
              node.innerHTML = splitResult.splitContent.content;
            } else {
              node.innerHTML = splitResult.splitContent.content;
            }
          }
          isSplitComplete = true;
        }
      }
    }
  }

  if (maxFloatImageHeight > maxHeight) {
    container.firstChild?.childNodes?.forEach((node: any) => {
      if (node.nodeType === 1) {
        node.classList.remove('remove');
      }
    });
    return { content: '' };
  }

  return {
    content: currentPageContent,
    totalEleHeight: accumulatedHeight,
  };
};

/**
 * 处理剩余内容分页
 */
const processRemainingContent = async (
  question: any,
  availableHeight: number,
  pageNum: number,
  isColumnMode: boolean,
  needsSplit: boolean,
  splitResults: any[],
  tempContainer: HTMLElement,
  preloadRef: HTMLDivElement | null,
) => {
  const maxPages =
    2 *
    pageData.currentLayout.column *
    (pageData.currentLayout.isContentColumn ? 2 : 1);
  const remainingPages =
    maxPages - (pageNum % pageData.currentLayout.column) - 1;
  let remainingHeight = question.height - availableHeight;

  for (let currentPage = 1; currentPage <= remainingPages; currentPage++) {
    let targetPage = pageNum + currentPage;

    if (pageData.currentLayout.isContentColumn) {
      targetPage = isColumnMode
        ? pageNum + 1 * (currentPage / 2)
        : pageNum + Math.ceil(currentPage / 2);
    }

    const pageHeight = getContentHeightByPage(targetPage, true);
    const splitPart = {
      ...question,
      key: `${question.key}-${currentPage + 1}`,
      totalHeight: question.height,
      isSplit: true,
      splitPage: currentPage,
      maxSplitPage: currentPage === remainingPages,
    };

    if (pageHeight > remainingHeight) {
      if (needsSplit) {
        const { content: splitContent, totalEleHeight: contentHeight = 0 } =
          await processSplitContent(tempContainer, pageHeight, preloadRef);

        if (contentHeight > remainingHeight) remainingHeight = contentHeight;

        if (
          2 === pageData.subjectiveMode &&
          splitPart.maxSplitPage &&
          remainingHeight - contentHeight < 50
        ) {
          remainingHeight += 54;
        }
        splitPart.content = splitContent;
      } else {
        splitPart.content = '';
      }

      splitResults.push({
        ...splitPart,
        height: remainingHeight < 30 ? 30 : remainingHeight,
        maxSplitPage: true,
      });
      return;
    }

    if (needsSplit) {
      const { content: splitContent } = await processSplitContent(
        tempContainer,
        pageHeight,
        preloadRef,
      );
      splitPart.content = splitContent;
    } else {
      splitPart.content = '';
    }

    splitResults.push({ ...splitPart, height: pageHeight });
    remainingHeight -= pageHeight;
  }
};

/**
 * 清理DOM
 */
const cleanup = (container: HTMLElement, preloadRef: HTMLDivElement | null) => {
  container.remove();
  preloadRef && (preloadRef.innerHTML = '');
};

/**
 * 优化后的拆分内容方法
 */
const splitContent = async (
  question: any,
  availableHeight: number,
  pageNum: number,
  isColumnMode = false,
  preloadRef: HTMLDivElement | null,
) => {
  if (!preloadRef) return [];

  const splitResults = [];
  const firstSplit = {
    ...question,
    key: `${question.key}-1`,
    height: availableHeight,
    totalHeight: question.height,
    isSplit: true,
    splitPage: 0,
  };

  // 准备内容容器
  const tempContainer = document.createElement('div');
  tempContainer.innerHTML = `<div class="split-content mce-content-body " 
         style="width: ${pageData.preloadWidth}px; 
                line-height: ${pageData.lineHeight};">
      ${question.content}
    </div>`;

  preloadRef.appendChild(tempContainer);

  // 加载图片资源
  const imageSources = Array.from(tempContainer.querySelectorAll('img')).map(
    (img) => img.src,
  );
  await loadImg(imageSources);

  const totalHeight = tempContainer.offsetHeight;
  const needsSplit =
    totalHeight > calculateAdjustedHeight(question, availableHeight);

  // 处理第一个片段
  if (needsSplit) {
    const adjustedHeight = calculateAdjustedHeight(question, availableHeight);
    const { content: splitCon } = await processSplitContent(
      tempContainer,
      adjustedHeight,
      preloadRef,
    );
    updateFirstSplit(firstSplit, splitCon);
  }
  splitResults.push(firstSplit);

  // 处理剩余内容
  await processRemainingContent(
    question,
    availableHeight,
    pageNum,
    isColumnMode,
    needsSplit,
    splitResults,
    tempContainer,
    preloadRef,
  );

  // 清理
  cleanup(tempContainer, preloadRef);
  // 在返回前验证内容
  if (splitResults.some((item) => !item.content)) {
    console.error('内容拆分异常，部分内容丢失');
    return [question]; // 返回原始问题作为fallback
  }
  return splitResults;
};
const splitWriting = (e: any, t: number, n: number) => {
  const i = [];
  let r = 0;
  const o = Object.assign({}, e, {
      key: e.key + '-1',
      height: t,
      totalHeight: e.height,
      isSplit: !0,
      splitPage: 0,
    }),
    {
      words: s,
      rows: a,
      rowWords: l,
    } = heightToWords(t - 7 - (e.hasScoreBox ? 64 : 38));
  (o.currentWords = s),
    (o.rows = a),
    (o.rowWords = l),
    (o.startWords = r),
    (r = s),
    i.push(o);
  const c = 2 * pageData.currentLayout.column,
    u = c - (n % pageData.currentLayout.column);
  let A = e.height - t;
  for (let t = 1; t <= u; t++) {
    const o = getContentHeightByPage(n + t, !0),
      s = Object.assign({}, e, {
        key: e.key + '-' + (t + 1),
        totalHeight: e.height,
        isSplit: !0,
        splitPage: t,
      });
    if (o > A) {
      const { words: e, rows: t, rowWords: n } = heightToWords(A - 7);
      return (
        (s.currentWords = e),
        (s.rows = t),
        (s.rowWords = n),
        (s.startWords = r),
        (r += e),
        i.push(Object.assign({}, s, { height: A < 57 ? 57 : A })),
        i
      );
    }
    {
      const { words: e, rows: t, rowWords: n } = heightToWords(o - 7);
      (s.currentWords = e),
        (s.rows = t),
        (s.rowWords = n),
        (s.startWords = r),
        (r += e),
        i.push(Object.assign({}, s, { height: o })),
        (A -= o);
    }
  }
  return i;
};
const hasBottomBorder = (
  e: { desctype: string; isColumn: any },
  t: { isColumn: any },
) => {
  return !e || 'subjective' !== e.desctype || !!e.isColumn !== !!t.isColumn;
};
/**
 * 内容格式化处理
 * @param e
 * @param t
 * @param c
 * @returns
 */
const convertContent = (e: any, c: string) => {
  let i = e;
  // 定义需要清除的样式属性
  const removeStyles = [
    'font-family',
    'font-size',
    'line-height',
    'text-indent',
    'padding-left',
  ];
  // 基础样式设置
  const baseStyle = `font-family:  ${pageData.templateFontFamily}, sans-serif; font-size: ${pageData.templateFontSize}px; line-height: ${pageData.lineHeight};`;

  // 处理style和data-mce-style属性
  ['style', 'data-mce-style'].forEach((attr) => {
    i = i.replace(
      new RegExp(`${attr}="([^"]*)"`, 'g'),
      (match: string, styles: string) => {
        const filtered = styles
          .split(';')
          .filter((style) => {
            const [prop] = style.split(':');
            return !removeStyles.includes(prop.trim());
          })
          .join(';');
        return `${attr}="${baseStyle}${filtered}"`;
      },
    );
  });

  // 为没有style属性的元素添加基础样式
  i = i.replace(
    /<([a-z][a-z0-9]*)(?![^>]*style=)/gi,
    `<$1 style="${baseStyle}"`,
  );

  return (
    (i = i
      .replace(/(<p.*?>)&ensp;&ensp;&ensp;&ensp;(<img)/g, '$1$2')
      .replace(/(<p.*?>)&ensp;&ensp;&ensp;&ensp;([A-G][．|.])/g, '$1$2')),
    i
  );
};
/**
 * 渲染选项
 * @param o
 * @returns
 */
export const renderOptions = (o: any) => {
  const { lineNumber, options } = o;
  let q = '';
  for (let i = 0; i < options.length; i++) {
    q += `<div class="question-option ant-col-${
      24 / lineNumber
    }"><span class="option">${
      options[i].name
    }</span> <div class="option-content padding-8">${convertContent(
      options[i].content,
      'option',
    )}</div></div>`;
  }
  const d = `<div class="option-row  ant-row">${q}</div>`;
  return d;
};
export const reloadOptions = async (
  r: any,
  preloadRef: HTMLDivElement | null,
) => {
  const c = r.name,
    h = renderOptions(r);
  let u = 0,
    i = 0;
  u = await calculateHeight(h, preloadRef);
  let n = convertContent(c, r.desctype);
  n = n.replace(/\n/g, '');
  r.width = pageData.preloadWidth;
  i = await calculateHeight(
    `<div class="padding-8"><div style="display: flex;"><span class="questionNum" style="padding-right: 8px;">${r.num}.</span><div>${n}</div></div></div>`,
    preloadRef,
  );
  (r.height = Math.ceil(u) + Math.ceil(i)), (r.opHeight = Math.ceil(u));
};
// pageDataModule.ts
export const updatePageData = (newData: any) => {
  Object.assign(pageData, newData);
};
/**
 * 将试题数据拆分为答题卡展示格式数据
 * @param questionLayout
 * @param preloadRef
 * @returns
 */
export const buildLayout = async (
  questionLayout: any,
  preloadRef: HTMLDivElement | null,
) => {
  const n: any = [];
  let oldTemplateLayout: any;
  let templateLayout: { key: any; type: any }[];
  for (let i = 0, r = questionLayout.length; i < r; i++) {
    const r: any = questionLayout[i];
    r.width = pageData.preloadWidth;
    const t = r.pid ? 'numberToCircled' : 'normal';
    // const o = t && this.updateQuestions.includes(r.id)
    //   , s = this.templateLayout.find(e => e.key === r.key);
    let a: any = {
      key: r.id || r._id || r.parentId || r.key,
      desctype: r.desctype,
      num: r.num,
    };
    if ('paperTitle' === r.desctype) {
      let i = 0;
      const { paperName, editor, describe } = r;
      i = await calculateHeight(
        `<div className="paperTitle"><h1>${paperName}</h1></div>`,
        preloadRef,
      );
      a = Object.assign({}, a, {
        paperName,
        editor,
        describe,
        height: Math.ceil(i),
        canSplit: !1,
      });
    }
    if ('questionName' === r.desctype) {
      const e = r.questionName || r.name;
      a = Object.assign({}, a, {
        content: e,
        height: r.hidden ? 0 : pageData.questionNameHeight,
        canSplit: !1,
      });
    }
    if ('content' === r.desctype) {
      const c = r.name;
      let n = convertContent(c, r.desctype);
      n = n.replace(/\n/g, '');
      let i = 0;
      r.width = pageData.preloadWidth;
      i = await calculateHeight(
        `<div class="padding-8"><div style="display: flex;"><span class="questionNum" style="padding-right: 8px;">${convertSpecialNumber(
          r.num,
          t,
        )}.</span><div>${n}</div></div></div>`,
        preloadRef,
      );
      a = Object.assign({}, r, {
        content: n,
        height: Math.ceil(i),
        canSplit: !0,
        hasScoreBox: !1,
      });
    }
    if ('groupContent' === r.desctype) {
      const c = r.name;
      let n = convertContent(c, r.desctype);
      let i = 0;
      const o = await calculateHeight(
        `<div class="padding-8">${n}</div>`,
        preloadRef,
      );

      o && (i = o);
      a = Object.assign({}, r, {
        content: n,
        height: Math.ceil(i),
        canSplit: !0,
        hasScoreBox: !1,
      });
    }
    if ('options' === r.desctype) {
      const o = r.options?.map((v: any) => {
          return {
            ...v,
            content: convertContent(v.content, r.desctype),
          };
        }),
        c = r.name,
        h = renderOptions(r);
      let u = 0,
        i = 0;
      u = await calculateHeight(h, preloadRef);
      let n = convertContent(c, r.desctype);
      n = n.replace(/\n/g, '');
      r.width = pageData.preloadWidth;
      i = await calculateHeight(
        `<div class="padding-8"><div style="display: flex;"><span class="questionNum" style="padding-right: 8px;">${r.num}.</span><div>${n}</div></div></div>`,
        preloadRef,
      );
      a = Object.assign({}, r, {
        options: o,
        content: n,
        height: Math.ceil(u) + Math.ceil(i),
        nameHeight: Math.ceil(i),
        opHeight: Math.ceil(u),
      });
    }
    if ('fill' === r.desctype) {
      let n,
        o = r.name.replace(/\n/g, '');
      const c = `<div style="display: flex;"><span class="questionNum" style="padding-right: 8px;">${convertSpecialNumber(
        r.num,
        t,
      )}.</span><div>${r.name}</div></div>`;
      n = c.replace(/\n/g, '');
      let i = 0;
      r.width = pageData.preloadWidth;
      i = await calculateHeight(
        `<div class="padding-8">${n}</div>`,
        preloadRef,
      );
      a = Object.assign({}, r, {
        content: o,
        height: Math.ceil(i),
        canSplit: !1,
      });
    }
    if ('subjective' === r.desctype) {
      const c = r.name;
      let n = convertContent(c, r.desctype);
      let i = 0;
      let e = 30 * (r.answerAreaRows || 1);
      const o = await calculateHeight(
        `<div class="padding-8"><div style="display: flex;"><span class="questionNum" style="padding-right: 8px;">${convertSpecialNumber(
          r.num,
          t,
        )}.</span><div>${n}</div></div></div>`,
        preloadRef,
      );
      o && (i = o), (i += e);
      (a = Object.assign({}, r, {
        content: n,
        height: Math.ceil(i),
        canSplit: !0,
        hasScoreBox: !1,
      })),
        o && (a.isChangeContent = !0);
    }
    if ('matching' === r.desctype) {
      let n = r.name;
      n = n.replace(/\n/g, '');
      let i = 0;
      let e = 30 * (r.answerAreaRows || 1);
      const o = await calculateHeight(
        `<div class="padding-8"><div style="display: flex;"><span class="questionNum" style="padding-right: 8px;">${convertSpecialNumber(
          r.num,
          t,
        )}.</span><div>${n}</div></div></div>`,
        preloadRef,
      );
      o && (i = o), (i += e);
      (a = Object.assign({}, r, {
        content: n,
        height: Math.ceil(i),
        canSplit: !1,
        hasScoreBox: !0,
      })),
        o && (a.isChangeContent = !0);
    }
    if ('chineseWriting' === r.desctype) {
      const t = 1080,
        { height: n, rows: i, rowWords: o } = wordsToHeight(t);
      a = Object.assign({}, r, {
        height: n,
        rows: i,
        rowWords: o,
        startWords: 0,
        canSplit: !0,
      });
    }
    a.height && n.push(a);
  }
  templateLayout = n;
  oldTemplateLayout = JSON.parse(JSON.stringify(n));
  return {
    oldTemplateLayout,
    templateLayout,
  };
};
/**
 * 将数据按排版拆分成多页数据
 * @param templateLayout 模板布局数据
 * @param preloadRef
 * @returns
 */
export const viewTemplate2 = async (
  templateLayout: any,
  preloadRef: HTMLDivElement | null,
) => {
  const t = [];
  let n: any[] = [];
  const i = templateLayout.filter(
      (e: { desctype: string }) => 'chineseWriting' === e.desctype,
    ),
    r = templateLayout.filter(
      (e: { desctype: string }) => 'chineseWriting' !== e.desctype,
    );
  let o = 0,
    s = 0;
  a();
  for (let l = 0, c = r.length; l < c; l++) {
    const e = r[l],
      i = (e.desctype, 0);
    let c = s - e.height - i;
    if (c < 0) {
      let r = o !== 2 * pageData.currentLayout.column;
      'options' === e.desctype && (r = o % pageData.currentLayout.column !== 0),
        'content' === e.desctype && (r = !0),
        'matching' === e.desctype && (r = !0),
        'groupContent' === e.desctype && (r = !0),
        'chineseWriting' === e.desctype && (r = !0),
        'subjective' === e.desctype && (r = !0),
        'paperPart' === e.desctype && (r = !0);
      const l = e.hasScoreBox ? 26 : 0,
        u = ('subjective' === e.desctype ? 34 : 30) + l;
      let A = !1,
        d = 0;
      pageData.currentLayout.isContentColumn &&
        ((d = getContentHeightByPage(o, !0)), s > d && (A = !0));
      const h = A ? s - d : e.height + c + i;
      if (r && e.canSplit && h > u) {
        let r = [];
        switch (e.desctype) {
          case 'content':
          case 'groupContent':
          case 'subjective':
          case 'matching':
          case 'paperPart':
            r = await splitContent(e, h, o, A, preloadRef);
            break;
          case 'options':
            r = await splitOptions(e, h, preloadRef);
            break;
          default:
            r = await splitDom(e, h, o, preloadRef);
        }
        r.forEach((e, l) => {
          if (pageData.currentLayout.isContentColumn) {
            const c = getContentHeightByPage(o, !0),
              u = s > c ? 1 : 2;
            u % 2 === 1
              ? (n.push(
                  Object.assign({}, e, { isColumn: !1, contentHeight: c }),
                ),
                (s -= e.height + i))
              : (n.push(
                  Object.assign({}, e, { isColumn: !0, contentHeight: c }),
                ),
                (s -= e.height + i),
                l < r.length - 1 && (n?.length && t.push(n), a(), (n = [])));
          } else
            n.push({
              ...e,
            }),
              l < r.length - 1
                ? (n?.length && t.push(n), a(), (n = []))
                : (s = e.height ? s - e.height - i : s);
        });
      } else {
        t.push(n), a();
        if (s - e.height - i < 10 && e.canSplit && !r) {
          n = [];
          let r = [];
          const l = getContentHeightByPage(o, !0),
            c = pageData.currentLayout.isContentColumn ? s - l : s;
          switch (e.desctype) {
            case 'content':
            case 'groupContent':
            case 'subjective':
            case 'paperPart':
              r = await splitContent(e, c, o, !0, preloadRef);
              break;
            case 'options':
              r = await splitOptions(e, c, preloadRef);
              break;
            default:
              r = await splitDom(e, c, o, preloadRef);
          }
          r.forEach((e, l) => {
            if (pageData.currentLayout.isContentColumn) {
              const c = getContentHeightByPage(o, !0),
                u = s > c ? 1 : 2;
              u % 2 === 1
                ? (n.push(Object.assign({}, e, { isColumn: !1 })),
                  (s -= e.height + i))
                : (n.push(Object.assign({}, e, { isColumn: !0 })),
                  (s -= e.height + i),
                  l < r.length - 1 && (t.push(n), a(), (n = [])));
            } else
              n.push(e),
                l < r.length - 1
                  ? (t.push(n), a(), (n = []))
                  : (s = e.height ? s - e.height - i : s);
          });
        } else (s -= e.height + i), (n = [e]);
      }
    } else if (
      pageData.currentLayout.isContentColumn &&
      'chineseWriting' !== e.desctype
    ) {
      const r = pageData.getContentHeightByPage(o, !0),
        l = e.hasScoreBox ? 26 : 0,
        u = ('subjective' === e.desctype ? 34 : 30) + l,
        A = s - r;
      if (e.canSplit && c < r && s > r && A >= u) {
        let r = [];
        switch (e.desctype) {
          case 'content':
          case 'groupContent':
          case 'subjective':
          case 'paperPart':
            r = await splitContent(e, A, o, !0, preloadRef);
            break;
          case 'options':
            r = await splitOptions(e, A, preloadRef);
            break;
          default:
            r = await splitDom(e, A, o, preloadRef);
        }
        r.forEach((e, o) => {
          (o + 1) % 2 === 1
            ? (n.push(Object.assign({}, e, { isColumn: !1 })), (s -= A))
            : (n.push(Object.assign({}, e, { isColumn: !0 })),
              (s -= e.height + i),
              o < r.length - 1 && (n?.length && t.push(n), a(), (n = [])));
        });
      } else
        c < r && s > r
          ? ((s = c), (s -= A))
          : ((s = c), s > r && (s -= s - r + e.height)),
          c > r
            ? n.push(Object.assign({}, e, { isColumn: !1 }))
            : n.push(Object.assign({}, e, { isColumn: !0 }));
    } else {
      (s = c), n.push(e);
    }
  }
  if (i.length) {
    if (pageData.currentLayout.isContentColumn) {
      const e = s <= getContentHeightByPage(o, !0);
      if (e)
        n?.length && t.push(n),
          o++,
          (s = getContentHeightByPage(o, !0)),
          (n = []);
      else {
        const e = 2 * getContentHeightByPage(o, !0),
          t = e - s;
        s = getContentHeightByPage(o, !0) - t;
      }
    }
    const e = 2 * pageData.currentLayout.column,
      r = (o - 1) % e,
      a = 1 === o ? o + 1 : o,
      l = getContentHeightByPage(a, !0),
      c = 0 === r && t.length ? s : (e - r) * l + s;
    if (c < i[0].height)
      for (let i = 0; i < e - r; i++) {
        n?.length && t.push(n),
          o++,
          (s = getContentHeightByPage(o, !0)),
          (n = []);
      }
    for (let u = 0, A = i.length; u < A; u++) {
      const e = i[u];
      let r = s - e.height;
      if (r < 0) {
        const i = o !== 2 * pageData.currentLayout.column,
          a = e.hasScoreBox ? 64 : 38,
          l = 30 + a,
          c = e.height + r;
        if (i && e.canSplit && c > l) {
          let i = splitWriting(e, c, o);
          i.forEach((e, r) => {
            n.push(e),
              r < i.length - 1
                ? (t.push(n),
                  o++,
                  (s = getContentHeightByPage(o, !0)),
                  (n = []))
                : (s = e.height ? s - e.height : s);
          });
        } else if (
          (t.push(n),
          o++,
          (s = getContentHeightByPage(o, !0)),
          s - e.height < 10 && e.canSplit)
        ) {
          n = [];
          let i = splitWriting(e, s, o);
          i.forEach((e, r) => {
            n.push(e),
              r < i.length - 1
                ? (t.push(n),
                  o++,
                  (s = getContentHeightByPage(o, !0)),
                  (n = []))
                : (s = e.height ? s - e.height : s);
          });
        } else (s -= e.height), (n = [e]);
      } else (s = r), n.push(e);
    }
    n?.length && t.push(n);
  } else {
    n?.length && t.push(n);
  }
  function a() {
    o++,
      (s =
        getContentHeightByPage(o, !0) *
        (pageData.currentLayout.isContentColumn ? 2 : 1));
  }
  t.forEach((e) => {
    e.forEach((t, n) => {
      t.contentHeight = getContentHeightByPage(o, !0);
      if ('subjective' === t.type) {
        const i = e[n + 1],
          r = hasBottomBorder(i, t);
        t.hasBottomBorder = r;
      }
    });
  }),
    (pageData.totalPages = t.length),
    (pageData.pageContent.length = pageData.totalPages || 0),
    preloadRef && (preloadRef.innerHTML = ''),
    pageData.isAutoReview && (pageData.isAutoReview = !1);
  return {
    templateData: t,
    oldTemplateLayout: JSON.parse(JSON.stringify(templateLayout)),
    pageData,
  };
};
