import { QuestionContext } from '@/context/QuestionContext';
import { DictionarieState } from '@/models/dictionarie';
import { QuestionTypesState } from '@/models/questionTypes';
import { index as getQuestionTierAPI } from '@/services//question_tier';
import { index } from '@/services/question_classification';
import { index as getDictionarieAPI } from '@/services/subject_dictionary';

import {
  FormInstance,
  ProForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import { useContext, useEffect, useState } from 'react';
import ConditionalRender from '../ConditionalRender';
import FormItemWrapper from './FormItemWrapper';
import { convertDictSelect } from './util';

const formItemLayout = {
  labelCol: { flex: '7em' },
};

const transitionDictSelect = (data: any[]) => {
  if (!data || !Array.isArray(data) || data.length !== 0) {
    return data.map((item) => ({
      label: item.name,
      title: item.name,
      value: item?.code ?? item.id,
    }));
  } else {
    return [];
  }
};

const QuestionForm = ({
  form,
  initValues,
  setLoading,
  handleQuestionSubmit,
  dictionarie,
  questionTypes,
  mode,
}: {
  form: FormInstance<any>;
  formType?: string;
  initValues?: any;
  onClose?: () => void;
  subject?: any;
  setLoading?: (loading: boolean) => void;
  handleQuestionSubmit?: (values: any) => void;
  dictionarie: DictionarieState;
  questionTypes: QuestionTypesState;
  mode?: 'answerAnalysis';
}) => {
  // 设置试题类型
  const [type, setType] = useState<any[]>();
  // 设置试题难度
  const [degree, setDegree] = useState<any[]>();
  // 题目分层
  const [topicSlice, setTopicSlice] = useState<any[]>([]);
  // 认知层次
  const [cognitive, setCognitive] = useState<any[]>([]);
  //核心素养
  const [quality, setQuality] = useState<any[]>([]);
  // 考察能力
  const [assessment, setAssessment] = useState<any[]>([]);
  //  版本
  const [version, setVersion] = useState<any[]>([]);
  /** 题目分类 */
  const [classify, setClassify] = useState<any[]>([]);

  /** 当前学科Id */
  const { subjectId: currentSubjectId, catalogId } =
    useContext(QuestionContext);

  /** 获取题目分类列表 */
  const getClassify = async () => {
    const { errCode, msg, data } = await index({});
    if (errCode) {
      message.error(`获取题目分类失败 ${msg}`);
      return;
    }

    setClassify(transitionDictSelect(data?.list || []));
  };

  /** 获取核心素养，认知层次，考察能力列表 */
  const getDictionarie = async (type: string) => {
    if (!currentSubjectId) return;
    const { errCode, msg, data } = await getDictionarieAPI({
      type,
      subjectId: currentSubjectId,
    });
    if (errCode) {
      message.error(`获取字典数据失败 ${msg}`);
      return;
    }
    return data.list;
  };

  const getLoadCognitiveAndQualityData = async () => {
    const qualityData = await getDictionarie('核心素养');
    const cognitiveData = await getDictionarie('认知层次');
    const assessmentData = await getDictionarie('考察能力');
    const { errCode, data, msg } = await getQuestionTierAPI({});
    if (errCode) {
      message.error(`获取题目分层数据失败 ${msg}`);
      return;
    }
    setTopicSlice(transitionDictSelect(data?.list || []));
    setCognitive(transitionDictSelect(cognitiveData || []));
    setQuality(transitionDictSelect(qualityData || []));
    setAssessment(transitionDictSelect(assessmentData || []));
  };

  // 获取试题所需字典数据
  const getBasicInfo = async () => {
    if (!currentSubjectId) return;
    const topicTypeList = questionTypes.list
      .filter((item) => {
        return item?.subjectId?.includes(Number(currentSubjectId));
      })
      .map((item) => {
        return {
          ...item,
          label: item.name,
          value: item.code,
          title: item.name,
        };
      });
    const difficultyList = dictionarie?.list.filter(
      (item: { type: string; name: string }) => item.type === 'difficulty_type',
    );

    const versionList = dictionarie?.list.filter(
      (item: { type: string; name: string }) => item.type === 'label',
    );

    setVersion(convertDictSelect(versionList));
    setType(topicTypeList);
    setDegree(convertDictSelect(difficultyList));
  };
  useEffect(() => {
    (async () => {
      await getBasicInfo();
      await getLoadCognitiveAndQualityData();
      await getClassify();
    })();
  }, []);

  useEffect(() => {
    if (initValues) {
      form.setFieldsValue({
        ...initValues,
        points: initValues?.points?.map((item: any) => ({
          label: item?.name,
          value: item?.id,
        })),
        questions: initValues?.children?.map((child: any) => ({
          ...child,
          points: child?.points?.map((item: any) => ({
            label: item?.name,
            value: item?.id,
          })),
          type_code: child?.type?.code,
        })),
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        difficulty: 'EASY',
        isCompose: false,
        year: dayjs(),
      });
    }
  }, [initValues]);

  return (
    <ProForm
      form={form}
      layout="horizontal"
      submitter={false}
      onFinish={async (values) => {
        setLoading?.(true);
        const { isCompose, type_code, questions, ...rest } = values;
        const type_info = questionTypes.list.find((v) => v.code === type_code);
        let params;
        if (isCompose) {
          if (rest.points) {
            if (rest.points && Array.isArray(rest.points)) {
              rest.points = rest.points
                .filter(
                  (item: { label: any; value: any }) =>
                    item && item.label && item.value,
                )
                .map((item: any) => ({
                  name: item.label,
                  id: item.value,
                }));
            }
          }
          params = {
            ...rest,
            isCompose,
            difficulty: {
              name: values?.difficulty.name,
              code: values?.difficulty.code,
            },
            baseType: {
              name: type_info?.sourceName,
              code: type_info?.sourceCode,
            },
            type: {
              name: type_info?.name,
              code: type_code,
            },
            children: questions.map((item: any) => {
              const { type_code, answer, ...rest } = item;
              const type_children_info = questionTypes.list.find(
                (v) => v.code === type_code,
              );
              if (rest.points) {
                if (rest.points && Array.isArray(rest.points)) {
                  rest.points = rest.points
                    .filter(
                      (item: { label: any; value: any }) =>
                        item && item.label && item.value,
                    )
                    .map((item: any) => ({
                      name: item.label,
                      id: item.value,
                    }));
                }
              }
              return {
                ...rest,
                baseType: {
                  name: type_children_info?.sourceName,
                  code: type_children_info?.sourceCode,
                },
                type: {
                  name: type_children_info?.name,
                  code: type_code,
                },
                difficulty: {
                  name: values?.difficulty.name,
                  code: values?.difficulty.code,
                },
                answer:
                  typeof answer !== 'string' ? JSON.stringify(answer) : answer,
              };
            }),
          };
        } else {
          if (rest.points) {
            if (rest.points && Array.isArray(rest.points)) {
              rest.points = rest.points
                .filter(
                  (item: { label: any; value: any }) =>
                    item && item.label && item.value,
                )
                .map((item: any) => ({
                  name: item.label,
                  id: item.value,
                }));
            }
          }
          params = {
            ...rest,
            isCompose,
            baseType: {
              name: type_info?.sourceName,
              code: type_info?.sourceCode,
            },
            type: {
              name: type_info?.name,
              code: type_code,
            },
            difficulty: {
              name: values?.difficulty.name,
              code: values?.difficulty.code,
            },
            answer:
              typeof rest?.answer !== 'string'
                ? JSON.stringify(rest?.answer)
                : rest?.answer,
          };
        }
        handleQuestionSubmit?.(params);
      }}
      {...formItemLayout}
      grid
    >
      <ProFormText name="_id" hidden />
      <ProFormText name="grade" hidden />
      <ProFormText name="gradeSection" hidden />
      <ProFormText name="subject" hidden />
      <ProFormText name="volume" hidden />
      <ProFormText name="textbookVersion" hidden />
      <ProFormText name="unit" hidden />
      <ProFormText name="period" hidden />
      <ProFormText name="catalog" hidden />
      <ProFormText name="author" hidden />
      <ProFormText name="baseType" hidden />
      <ProFormText name="createType" hidden />
      <ProFormText name="stem" hidden />
      <ProFormText name="tableName" hidden />
      {/* TODO 临时使用，后期删除 */}
      <ConditionalRender
        hasAccess={true}
        accessComponent={
          <>
            <ProFormSelect
              colProps={{ md: 12, xl: 12 }}
              name="label"
              label="版本"
              options={version}
            />
            <ProFormText
              colProps={{ md: 12, xl: 12 }}
              name="sourceId"
              hidden
              label="原试题Id"
            />
          </>
        }
      />

      <FormItemWrapper
        mode={mode}
        initValues={initValues}
        form={form}
        type={type}
        topicSlice={topicSlice}
        cognitive={cognitive}
        quality={quality}
        assessment={assessment}
        catalogId={catalogId ?? initValues?.catalog?.id}
        degree={degree}
        classify={classify}
      />
    </ProForm>
  );
};

export default connect(({ dictionarie, questionTypes }: any) => ({
  dictionarie,
  questionTypes,
}))(QuestionForm);
