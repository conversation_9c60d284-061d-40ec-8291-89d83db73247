import { SECTION_GRADE } from '@/constants';
import { DictionarieState } from '@/models/dictionarie';
import { connect } from '@umijs/max';
import { Col, ColProps, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { RadioGroupStyled } from './styled';

const Grade: React.FC<{
  initalValue?: string;
  onChange?: (value?: API.Dictionarie) => void;
  /** 学段编号 */
  sectionCode?: string;
  /** 标题列属性 */
  labelCol?: ColProps;
  /** 自定义行样式 */
  rowStyles?: React.CSSProperties;
  dictionarie: DictionarieState;
}> = ({
  initalValue,
  onChange,
  sectionCode,
  labelCol,
  rowStyles,
  dictionarie,
}) => {
  const [value, setValue] = useState<string | undefined>(initalValue);
  const [gradeList, setGradeList] = useState<string[]>([]);

  // 根据学段获取年级列表，并查询学科列表
  useEffect(() => {
    setGradeList(
      SECTION_GRADE[(sectionCode as keyof typeof SECTION_GRADE) || ''] || [],
    );
  }, [sectionCode]);

  // 年级列表切换后，检查currentGrade是否在列表中
  useEffect(() => {
    if (value) {
      if (!gradeList.includes(value)) {
        setValue(undefined);
      }
    } else {
      setValue(gradeList?.[0]);
      onChange?.(
        dictionarie.list.find(
          (d) => d.type === 'grade' && d.code === gradeList?.[0],
        ),
      );
    }
  }, [gradeList, value]);

  return (
    <Row className={styles.row} style={rowStyles}>
      <Col className={styles.label} {...labelCol}>
        <label>年级</label>
      </Col>
      <Col>
        <RadioGroupStyled
          size="small"
          options={dictionarie.list
            .filter((d) => d.type === 'grade' && gradeList.includes(d.code))
            .map((item) => ({
              label: item.name,
              value: item.code,
              title: item.description || item.name,
            }))}
          onChange={(e) => {
            setValue(e.target.value);
            const item = dictionarie.list.find(
              (d) => d.type === 'grade' && d.code === e.target.value,
            );
            onChange?.(item);
          }}
          value={value}
          optionType="button"
          buttonStyle="solid"
        />
      </Col>
    </Row>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(Grade);
