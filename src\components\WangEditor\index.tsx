/*
 * @Description: wangEditor 编辑器，支持配合 ProForm 组件使用
 * 使用 onInsertedImage 来收集所有上传或者插入的图片，记录为 imageList1
 * 最后保存编辑器内容之前，使用 editor.getElemsByType('image') 获取当前编辑器的所有图片，记录为 imageList2
 * 对比 imageList1 和 imageList2 ，两者的差异，就是删除过的图片
 * @Date: 2025-03-03 18:02:58
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-06 12:33:31
 */
import '@wangeditor-next/editor/dist/css/style.css'; // 引入 css

import { deleteFile, upload } from '@/utils/cos';
import {
  IDomEditor,
  IEditorConfig,
  IToolbarConfig,
} from '@wangeditor-next/editor';
import { Editor, Toolbar } from '@wangeditor-next/editor-for-react';
import { message } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

// 定义组件暴露的ref类型
export interface WangEditorRef {
  /** 获取编辑器内容 */
  getValue: () => string;
  /** 从服务器清除已删除的图片和视频 */
  clearDeletedMedia: () => void;
}

const MyEditor = forwardRef<
  WangEditorRef,
  { value?: string; onChange?: (value: string) => void }
>((props, ref) => {
  const { value, onChange } = props;

  // editor 实例
  const [editor, setEditor] = useState<IDomEditor | null>(null);

  // 编辑器内容
  const [html, setHtml] = useState('');

  // 记录图片和视频列表
  const [imageList, setImageList] = useState<string[]>([]);
  const [videoList, setVideoList] = useState<string[]>([]);

  useEffect(() => {
    setHtml(value || '');
  }, [value]);

  // 工具栏配置
  const toolbarConfig: Partial<IToolbarConfig> = {};

  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    placeholder: '请输入内容...',
    MENU_CONF: {
      insertImage: {
        onInsertedImage(imageNode) {
          if (imageNode === null) return;
          const src = imageNode.src;
          if (src.includes('myqcloud.com/')) {
            setImageList((prevList) => [...prevList, src]);
          }
        },
      },
      uploadImage: {
        ...({} as any), // 防止ts检查错误
        customUpload: async (file, insertFn) => {
          try {
            const { result, data, err } = await upload({
              file,
              dir: 'docImages',
              name: `${Date.now()}_${file.name}`,
              onProgress: (progressData) => {
                console.log(progressData.percent);
              },
            });
            if (!result) {
              message.error(`上传失败 ${err}`);
            } else {
              message.success('上传成功');
              const imageUrl = '//' + data!.Location;
              insertFn(imageUrl);
            }
          } catch (error) {
            message.error(`源上传失败：${error}`);
          }
        },
        base64LimitSize: 5 * 1024, // 5kb
      },
      uploadVideo: {
        ...({} as any), // 防止ts检查错误
        customUpload: async (file, insertFn) => {
          try {
            const { result, data, err } = await upload({
              file,
              dir: 'docVideos',
              name: `${Date.now()}_${file.name}`,
              onProgress: (progressData) => {
                console.log(progressData.percent);
              },
            });
            if (!result) {
              message.error(`上传失败 ${err}`);
            } else {
              message.success('上传成功');
              const videoUrl = '//' + data!.Location;
              insertFn(videoUrl);
              if (videoUrl.includes('myqcloud.com/')) {
                setVideoList((prevList) => [...prevList, videoUrl]);
              }
            }
          } catch (error) {
            message.error(`视频上传失败：${error}`);
          }
        },
      },
    },
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getValue: () => html,
    clearDeletedMedia: () => {
      if (!editor) return;
      const currentImages = editor
        .getElemsByType('image')
        .map((img) => (img as unknown as { src: string }).src);
      const currentVideos = editor
        .getElemsByType('video')
        .map((video) => (video as unknown as { src: string }).src);

      const deletedImages = imageList.filter(
        (img) => !currentImages.includes(img) && img.includes('myqcloud.com/'),
      );
      const deletedVideos = videoList.filter(
        (vid) => !currentVideos.includes(vid) && vid.includes('myqcloud.com/'),
      );

      deletedImages.forEach((src) => {
        const key = src.split('myqcloud.com/')[1];
        deleteFile({ name: key });
      });

      deletedVideos.forEach((src) => {
        const key = src.split('myqcloud.com/')[1];
        deleteFile({ name: key });
      });
    },
  }));

  // 及时销毁 editor ，重要！
  useEffect(() => {
    if (editor) {
      const initialImages = editor.getElemsByType('image').map((img) => {
        return (img as unknown as { src: string }).src;
      });
      setImageList(initialImages);

      const initialVideos = editor.getElemsByType('video').map((video) => {
        return (video as unknown as { src: string }).src;
      });
      setVideoList(initialVideos);
    }
    return () => {
      if (editor === null) return;
      editor.destroy();
      setEditor(null);
    };
  }, [editor]);

  return (
    <div style={{ border: '1px solid #ccc', zIndex: 2000 }}>
      <Toolbar
        editor={editor}
        defaultConfig={toolbarConfig}
        mode="default"
        style={{ borderBottom: '1px solid #ccc' }}
      />
      <Editor
        defaultConfig={editorConfig}
        value={html}
        onCreated={setEditor}
        onChange={(editor) => {
          const content = editor.getHtml();
          // 使用正则表达式判断内容是否为空
          const isEmpty = /^<p[^>]*><br><\/p>$/.test(content);
          setHtml(isEmpty ? '' : content);
          onChange?.(isEmpty ? '' : content);
        }}
        mode="default"
        style={{ minHeight: '150px', overflowY: 'hidden' }}
      />
    </div>
  );
});

export default MyEditor;
