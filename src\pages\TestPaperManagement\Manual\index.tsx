/*
 * @Description: 手工组卷
 * @Date: 2025-04-21 17:04:44
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-21 17:05:31
 */
import CommonCard from '@/components/CommonCard';
import {
  createComposerPaper,
  getComposerPaper,
  removeComposerPaper,
  updateComposerPaper,
} from '@/services/test_paper';
import { index as findChicklist } from '@/services/textbook_checklist';
import { PlusOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Button, message } from 'antd';
import { useEffect, useState } from 'react';
import { PartialWithRequired } from 'typings';
import PaperList from '../components/List';
import TemplateModal from '../components/TestPaperModal';

type TemplateModalType = {
  width?: number;
  open: boolean;
  title: string | undefined;
  data: any;
  type: 'add' | 'edit' | 'look';
};

const TestPaper = () => {
  const { initialState } = useModel('@@initialState');
  const [baseModal, setBaseModal] = useState<TemplateModalType>();
  const [data, setData] = useState<API.ComposerPaperType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState<{
    pageSize: number;
    current: number;
  }>({
    pageSize: 10,
    current: 1,
  });

  /** 获取列表 */
  const getListData = async () => {
    setLoading(true);
    const { errCode, msg, data } = await getComposerPaper({
      offset:
        Number((pagination?.current || 1) - 1) *
        Number(pagination?.pageSize || 10),
      limit: Number(pagination?.pageSize || 10),
      ruleType: '手工',
      enterpriseCode: initialState?.enterprise?.code,
      creatorId: initialState?.id,
    });
    if (errCode) {
      setLoading(false);
      message.error(msg || '组卷方案列表查询失败');
    } else {
      setLoading(false);
      setData(data?.list ?? []);
    }
  };

  /**删除方案 */
  const onDelete = async (id: number) => {
    const { errCode, msg } = await removeComposerPaper(id);
    if (errCode) {
      return message.warning(`删除组卷方案失败 ${msg}`);
    }
    message.success('删除组卷方案成功');
    getListData();
  };

  /** 编辑 */
  const onEdit = async (
    data: PartialWithRequired<API.ComposerPaperType, 'id'>,
  ) => {
    const { id, ...rest } = data;
    const { errCode, msg } = await updateComposerPaper(id, rest);
    if (errCode) {
      return message.warning(`修改组卷方案失败 ${msg}`);
    }
    message.success('修改组卷方案成功');
    getListData();
    setBaseModal({
      open: false,
      title: undefined,
      data: undefined,
      type: 'add',
    });
  };

  const getTextbookChecklist = async (
    textbook_id: number,
    grade: string,
    volume: string,
  ) => {
    const { errCode, msg, data } = await findChicklist({
      textbook_id,
      grade,
      volume,
    });
    if (errCode) {
      message.error(msg || '获取教材目录失败');
      return null;
    }
    return data.list?.[0];
  };
  /** 新增方案 */
  const onSubmit = async (values: any) => {
    if (baseModal?.type === 'edit') {
      return onEdit({
        id: values.id,
        description: values.description,
        name: values.name,
      });
    }
    const res = await getTextbookChecklist(
      values.versionId,
      values.gradeCode,
      values.volume,
    );
    if (!res?.id) {
      return message.warning('获取教材目录Id失败');
    } else {
      values.textbookChecklistId = res?.id;
      const body = {
        ...values,
        ruleType: '手工',
        creatorId: initialState?.id,
        creatorName: initialState?.nickname,
        enterpriseCode: initialState?.enterprise?.code,
      };
      const { errCode, msg } = await createComposerPaper(body);
      if (errCode) {
        return message.warning(`创建组卷方案失败 ${msg}`);
      }
      message.success('创建组卷方案成功');
      getListData();
      setBaseModal({
        open: false,
        title: undefined,
        data: undefined,
        type: 'add',
      });
    }
  };

  useEffect(() => {
    if (pagination.current && pagination.pageSize) {
      getListData();
    }
  }, [pagination]);

  const headEvent = (type: string, data: any) => {
    switch (type) {
      case 'delete':
        onDelete(data.id);
        break;
      case 'paper':
        history.push(`/testPaperManagement/manual/detail/${data.id}`, {
          ...data,
        });
        break;
      case 'record':
        history.push(`/testPaperManagement/manual/record/${data.id}`, {
          ...data,
        });
        break;
      case 'edit':
        setBaseModal({
          open: true,
          title: '编辑组卷方案',
          data,
          type: 'edit',
        });
        break;
      case 'detail':
        setBaseModal({
          open: true,
          title: '查看组卷方案',
          data,
          type: 'look',
        });
        break;
    }
  };

  return (
    <div>
      <CommonCard title={`手工作业组卷管理 (${data.length ?? 0})`}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setBaseModal({
              open: true,
              title: '新增组卷方案',
              data: undefined,
              type: 'add',
            });
          }}
        >
          新增组卷方案
        </Button>
      </CommonCard>
      <PaperList
        loading={loading}
        headEvent={headEvent}
        isAdmin={true}
        data={data}
        pagination={{
          pageSize: pagination.pageSize,
          current: pagination.current,
          showSizeChanger: true,
          total: data?.length ?? 0,
          align: 'end',
          showTotal: (total) => `共 ${total} 条`,
          onChange: (page, pageSize) => {
            setPagination({
              pageSize,
              current: page,
            });
          },
        }}
      />
      <>
        <TemplateModal
          width={baseModal?.width ?? 500}
          open={baseModal?.open}
          title={baseModal?.title}
          data={baseModal?.data}
          type={baseModal?.type}
          onSubmit={onSubmit}
          onCancel={() => {
            setBaseModal({
              open: false,
              title: undefined,
              data: undefined,
              type: 'add',
            });
          }}
        />
      </>
    </div>
  );
};

export default TestPaper;
