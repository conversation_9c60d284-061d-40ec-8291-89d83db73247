/*
 * @Description: 课时作业范本管理
 * @Date: 2025-02-11 10:00:28
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-11 10:02:47
 */

import { workBook } from '@/assets';
import { useCurrentAccess } from '@/common/useCurrentAccess';
import CommonCard from '@/components/CommonCard';
import ConditionalRender from '@/components/ConditionalRender';
import { DictionarieState } from '@/models/dictionarie';
import {
  templateWorkCreate,
  templateWorkIndex,
  templateWorkRemove,
  templateWorkUpdate,
} from '@/services/class_work';
import { EditTwoTone, FileWordOutlined, PlusOutlined } from '@ant-design/icons';
import { ActionType, ProList } from '@ant-design/pro-components';
import { connect, history, useLocation, useModel } from '@umijs/max';
import { Button, message, Modal, Popconfirm, Space, Tabs, Tag } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import AddTemplate from '../AddTemplate';
import styles from './index.less';

type TemplateModalType = {
  width?: number;
  open: boolean;
  title: string | undefined;
  data: any;
};

type ParamsType = {
  pageSize?: number;
  current?: number;
};

const HomeworkTemplate = ({
  type,
  dictionarie,
}: {
  type: string;
  dictionarie: DictionarieState;
}) => {
  const { initialState: currentUser } = useModel('@@initialState');
  const { isSysAdmin, isSchoolAdmin } = useCurrentAccess();

  const actionRef = useRef<ActionType>();
  const { pathname } = useLocation();
  const [baseModal, setBaseModal] = useState<TemplateModalType>();
  const [curSection, setCurSection] = useState<any>();
  const [activeKey, setActiveKey] = useState<string>('system');
  /* 初始化数据 */
  const initData = async (params?: ParamsType) => {
    const { pageSize, current } = params || {};
    const { errCode, data, msg } = await templateWorkIndex({
      enterprise_id:
        activeKey === 'system' ? undefined : currentUser?.enterprise_id,
      grade_section_code: curSection?.code,
      type: activeKey === 'system' ? '内置' : '自定义',
      status:
        isSchoolAdmin && activeKey === 'system'
          ? '发布'
          : !isSchoolAdmin && !isSysAdmin
          ? '发布'
          : undefined,
      offset: ((current || 1) - 1) * (pageSize || 10),
      limit: pageSize || 10,
    });
    if (errCode) {
      message.warning('数据获取失败,请稍后重试' + msg);
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
    return {
      data: data?.list ?? [],
      success: true,
      total: data?.total,
    };
  };

  const deleteTemplate = async (id?: string) => {
    const { errCode, msg } = await templateWorkRemove(id || '');
    if (errCode) {
      message.warning('删除失败,请稍后重试！' + msg);
      return;
    }
    message.success('删除成功');
    actionRef?.current?.reload();
  };
  const publishTemplate = async (id?: string, flag?: boolean) => {
    const { errCode, msg } = await templateWorkUpdate(id || '', {
      status: '发布',
      flag: flag || false,
    });
    if (errCode) {
      if (errCode === 400) {
        Modal.confirm({
          title: '提示',
          icon: <FileWordOutlined />,
          content: msg,
          onOk() {
            publishTemplate(id, true);
          },
          onCancel() {},
          okText: '确定',
          cancelText: '取消',
        });
        return;
      } else {
        message.warning('发布失败,请稍后重试！' + msg);
        return;
      }
    }
    message.success('发布成功');
    actionRef?.current?.reload();
  };
  const updateTemplate = async (type: string, id?: string, status?: any) => {
    let params;
    switch (type) {
      case 'share':
        params = {
          is_share: !status,
        };
        break;
      default:
        params = {
          status: status || '草稿',
        };
        break;
    }
    const { errCode, msg } = await templateWorkUpdate(id || '', params);
    if (errCode) {
      message.warning('操作失败,请稍后重试！' + msg);
      return;
    }
    message.success(
      type === 'withdraw' ? '当前范本已经恢复为草稿状态' : '操作成功',
    );
    actionRef?.current?.reload();
  };

  const addTemplate = () => {
    setBaseModal({
      width: 600,
      open: true,
      title: '新增范本',
      data: undefined,
    });
  };

  const onSubmit = async (values: any) => {
    if (values?.id) {
      const { id, ...rest } = values;
      const { errCode, msg } = await templateWorkUpdate(id, {
        ...rest,
      });
      if (errCode) {
        message.warning('修改失败,请稍后重试！' + msg);
        return;
      }
      message.success('修改成功');
    } else {
      const { errCode, msg } = await templateWorkCreate(values);
      if (errCode) {
        message.warning('创建失败,请稍后重试！' + msg);
        return;
      }
      message.success('创建成功');
    }
    setBaseModal({
      open: false,
      title: undefined,
      data: undefined,
    });
    actionRef?.current?.reload();
  };
  useEffect(() => {
    if (dictionarie && dictionarie?.currentList?.length) {
      const curSec = dictionarie.currentList.find(
        (item: { name: string }) => item.name === type,
      );
      if (curSec) {
        setCurSection(curSec);
      }
    }
  }, [type, dictionarie]);

  return (
    <>
      <ConditionalRender
        hasAccess={!isSysAdmin}
        accessComponent={
          <div className={styles.topBar}>
            <Tabs onChange={setActiveKey} activeKey={activeKey}>
              <Tabs.TabPane tab="系统范本" key="system"></Tabs.TabPane>
              <Tabs.TabPane tab="学校范本" key="school"></Tabs.TabPane>
            </Tabs>
          </div>
        }
      />
      <CommonCard title={`${type}课时作业范本管理`}>
        <ConditionalRender
          hasAccess={
            (activeKey === 'system' && !isSysAdmin) ||
            (activeKey === 'school' && !isSchoolAdmin)
          }
          accessComponent={<></>}
          noAccessComponent={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={addTemplate}
            >
              新增范本
            </Button>
          }
        />
      </CommonCard>
      <div className={classNames('commonWapper', styles.listWapper)}>
        <ProList<API.TemplateWork>
          actionRef={actionRef}
          toolBarRender={false}
          search={false}
          params={{
            curSection,
            currentUser,
            activeKey,
          }}
          rowKey="id"
          request={initData}
          pagination={{
            pageSize: 10,
            align: 'center',
            showTitle: false,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => {
              return `总共 ${total} 条`;
            },
          }}
          showActions="hover"
          metas={{
            title: {
              dataIndex: 'name',
              render: (_, row) => {
                return (
                  <>
                    {row?.name}
                    <Tag
                      style={{
                        marginLeft: 16,
                      }}
                      color={row?.status === '草稿' ? 'blue' : 'green'}
                    >
                      {row?.status}
                    </Tag>
                    {row?.is_share && <Tag color="#2db7f5">已共享</Tag>}
                    {row?.status === '草稿' && (
                      <EditTwoTone
                        title="修改范本名称"
                        className={styles.editTwoToneIcon}
                        onClick={() => {
                          setBaseModal({
                            width: 600,
                            open: true,
                            title: '编辑范本',
                            data: row,
                          });
                        }}
                      />
                    )}
                  </>
                );
              },
            },
            avatar: {
              dataIndex: 'avatar',
              render: () => {
                return <img alt="" src={workBook} style={{ width: 50 }} />;
              },
            },
            description: {
              dataIndex: 'updatedAt',
              render: (_, row: any) => {
                return (
                  <Space wrap>
                    <span>
                      学段：
                      {row?.grade_section_name}
                    </span>
                    <span>
                      年级：
                      {row?.grade_name}
                    </span>
                    <span>
                      科目：
                      {row?.subject_name}
                    </span>
                    <span>
                      版材：
                      {row?.textbook_version}
                    </span>
                    <span>
                      册次：
                      {row?.volume}
                    </span>
                    <ConditionalRender
                      hasAccess={
                        row?.enterprise_id !== currentUser?.enterprise_id &&
                        row?.is_share
                      }
                      accessComponent={
                        <span>
                          来源：
                          <a>{row?.enterprise?.name}</a>
                        </span>
                      }
                    />
                    <ConditionalRender
                      hasAccess={INDEPENDENT_QUESTION_BANK}
                      accessComponent={
                        <span>
                          来源：
                          {row?.source}
                        </span>
                      }
                    />
                    <span>
                      更新：
                      {dayjs(row?.updatedAt).format('YYYY-MM-DD HH:mm')}
                    </span>
                  </Space>
                );
              },
            },
            actions: {
              render: (_dom, row) => {
                return (
                  <ConditionalRender
                    hasAccess={row?.status === '草稿'}
                    accessComponent={
                      <>
                        <Button
                          key="detail"
                          type="link"
                          onClick={() => {
                            history.push(`${pathname}/${row?.id}`, row);
                          }}
                        >
                          进入范本
                        </Button>
                        <Popconfirm
                          key="publish"
                          title="发布后当前版本不可再做修改，确认发布吗?"
                          onConfirm={() => publishTemplate(row?.id)}
                          okText="确定"
                          cancelText="取消"
                          placement="topRight"
                        >
                          <Button color="cyan" variant="link">
                            发布
                          </Button>
                        </Popconfirm>
                        <Popconfirm
                          key="deletes"
                          title="删除后数据将无法恢复，确认删除吗?"
                          onConfirm={() => deleteTemplate(row?.id)}
                          okText="确定"
                          cancelText="取消"
                          placement="topRight"
                        >
                          <Button type="link" danger>
                            刪除
                          </Button>
                        </Popconfirm>
                      </>
                    }
                    noAccessComponent={
                      <>
                        <ConditionalRender
                          hasAccess={
                            row?.enterprise_id !== currentUser?.enterprise_id &&
                            row?.is_share
                          }
                          accessComponent={<></>}
                          noAccessComponent={
                            activeKey === 'school' &&
                            isSchoolAdmin && (
                              <Popconfirm
                                key="share"
                                title={`${
                                  row?.is_share ? '取消' : ''
                                }共享后其他学校将${
                                  row?.is_share ? '不' : ''
                                }可使用此范本，确认${
                                  row?.is_share ? '取消' : ''
                                }共享吗？`}
                                onConfirm={() =>
                                  updateTemplate(
                                    'share',
                                    row?.id,
                                    row?.is_share,
                                  )
                                }
                                okText="确定"
                                cancelText="取消"
                                placement="topRight"
                              >
                                <Button color="purple" variant="link">
                                  {row?.is_share ? '取消' : ''}共享
                                </Button>
                              </Popconfirm>
                            )
                          }
                        />
                        <Button
                          key="detail"
                          type="link"
                          onClick={() => {
                            history.push(`${pathname}/${row?.id}`, row);
                          }}
                        >
                          进入范本
                        </Button>
                        <ConditionalRender
                          hasAccess={
                            (activeKey === 'system' && !isSysAdmin) ||
                            (activeKey === 'school' && !isSchoolAdmin) ||
                            row?.is_share
                          }
                          accessComponent={<></>}
                          noAccessComponent={
                            <Popconfirm
                              key="withdraw"
                              title="确认撤销当前作业范本的发布状态吗?"
                              onConfirm={() =>
                                updateTemplate('withdraw', row?.id, '草稿')
                              }
                              okText="确定"
                              cancelText="取消"
                              placement="topRight"
                            >
                              <Button type="link" danger>
                                撤销
                              </Button>
                            </Popconfirm>
                          }
                        />
                      </>
                    }
                  />
                );
              },
            },
          }}
        />
      </div>
      <>
        <AddTemplate
          width={baseModal?.width ?? 450}
          open={baseModal?.open}
          title={baseModal?.title}
          initialValues={baseModal?.data}
          curSection={{
            ...curSection,
            activeKey,
          }}
          onFinish={onSubmit}
          layout="horizontal"
          modalProps={{
            destroyOnClose: true,
            onCancel: () => {
              setBaseModal({
                open: false,
                title: undefined,
                data: undefined,
              });
            },
            styles: {
              body: {
                marginTop: 20,
              },
            },
          }}
        />
      </>
    </>
  );
};

export default connect(({ dictionarie }: any) => ({ dictionarie }))(
  HomeworkTemplate,
);
