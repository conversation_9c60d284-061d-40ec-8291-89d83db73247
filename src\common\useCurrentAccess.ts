import { useAccess, useSelectedRoutes } from '@umijs/max';

interface RouteWithAccess {
  route?: {
    access?: string;
  };
}

type CurrentAccessType = {
  /**
   * 是否可以查看当前页面
   */
  isView: boolean;
  /**
   * 是否可以管理当前页面
   */
  isAdmin: boolean;
  /**
   * 是否系统管理当前页面
   */
  isSysAdmin: boolean;
  /**
   * 是否学校管理当前页面
   */
  isSchoolAdmin: boolean;
  /**
   * 当前路由的权限列表
   */
  permissions: Array<{ name: string; code: string }>;
};

/**
 * 返回当前路由对应的权限配置
 * @returns 当前路由的权限配置对象，如果未配置则返回默认值
 */
export const useCurrentAccess = (): CurrentAccessType => {
  const { featuresList = {} } = useAccess();
  const routes = useSelectedRoutes();

  const currentRoute = routes[routes.length - 1] as RouteWithAccess | undefined;
  const accessKey = currentRoute?.route?.access;

  // 默认值
  const defaultAccess: CurrentAccessType = {
    isView: false,
    isAdmin: false,
    isSysAdmin: false,
    isSchoolAdmin: false,
    permissions: [],
  };

  if (!accessKey) return defaultAccess;

  const currentPermission = featuresList?.[accessKey];
  if (Array.isArray(currentPermission)) {
    return {
      isView: currentPermission.some((perm) => perm.name === '查看'),
      isAdmin: currentPermission.some((perm) => perm.name === '管理'),
      isSysAdmin: currentPermission.some((perm) => perm.name === '系统管理'),
      isSchoolAdmin: currentPermission.some((perm) => perm.name === '学校管理'),
      permissions: currentPermission,
    };
  }

  return defaultAccess;
};
