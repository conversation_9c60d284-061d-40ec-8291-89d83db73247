/*
 * @Description: 数据字典
 * @Date: 2025-02-12 12:25:32
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-05-19 10:46:19
 */
import { DictionarieState } from '@/models/dictionarie';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { AnyAction, connect, Dispatch } from '@umijs/max';
import { Button, Divider, Popconfirm, Space } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import EditModal from './EditModal';

const Dictionarie: React.FC<{
  dictionarie: DictionarieState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ dictionarie, dispatch }) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Dictionarie | undefined>(
    undefined,
  );

  const handleSave = useCallback(
    async (values: API.Dictionarie) => {
      const { code, ...info } = values;
      if (current) {
        dispatch({
          type: 'dictionarie/update',
          payload: {
            code,
            info,
          },
        });
      } else {
        dispatch({
          type: 'dictionarie/add',
          payload: values,
        });
      }
    },
    [current],
  );

  const handleDel = async (record: API.Dictionarie) => {
    const { code } = record;
    dispatch({
      type: 'dictionarie/remove',
      payload: {
        code,
      },
    });
  };

  const columns: ProColumns<API.Dictionarie, 'text'>[] = [
    {
      title: '序号',
      valueType: 'index',
      key: 'index',
      width: 55,
      align: 'center',
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      copyable: true,
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '别名',
      dataIndex: 'alias',
      key: 'alias',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      filters: [...new Set(dictionarie.list.map((item) => item.type))]
        .sort((a, b) => a.localeCompare(b))
        .map((item) => ({ text: item, value: item })),
      search: false,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        0: {
          text: '禁用',
          status: 'Error',
        },
        1: {
          text: '启用',
          status: 'Success',
        },
      },
      filters: true,
      search: false,
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space split={<Divider type="vertical" />}>
          <a
            onClick={() => {
              handleSave({ ...record, status: record.status === 1 ? 0 : 1 });
            }}
          >
            {record.status === 1 ? '禁用' : '启用'}
          </a>
          <a
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </a>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <a style={{ color: '#ff4d4f' }}>删除</a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setModalVisible(false);
  }, [dictionarie]);

  return (
    <>
      <ProTable<API.Dictionarie>
        actionRef={actionRef}
        rowKey="id"
        headerTitle="数据字典"
        columns={columns}
        dataSource={dictionarie.currentList}
        onChange={(_pagination, filters, sorter) => {
          if (filters?.status?.length) {
            filters.status = filters.status.map((item) => Number(item));
          }
          dispatch({
            type: 'dictionarie/onChange',
            payload: {
              filters,
              sorter,
            },
          });
        }}
        beforeSearchSubmit={(params) => {
          dispatch({
            type: 'dictionarie/onSearch',
            payload: params,
          });
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
        options={false}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => {
          setCurrent(undefined);
          setModalVisible(false);
        }}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ dictionarie, loading }) => ({
  dictionarie,
  loading,
}))(Dictionarie);
