import ProFormImg from '@/components/ProFormItem/ProFormImg';
import { AreaState } from '@/models/area';
import { DictionarieState } from '@/models/dictionarie';
import {
  ModalForm,
  ProForm,
  ProFormDependency,
  ProFormSegmented,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Divider, Form } from 'antd';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Enterprise;
  onSave: (info: API.Enterprise) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
  area: AreaState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  dictionarie,
  area,
}) => {
  const [form] = Form.useForm<any>();
  return (
    <ModalForm<API.Enterprise>
      title={info ? '编辑单位' : '注册单位'}
      autoFocusFirstInput
      form={form}
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      onOpenChange={(visible) => {
        if (visible) {
          if (info) {
            form.setFieldsValue(info);
          } else {
            form.setFieldsValue({
              type: 'SCHOOL',
              province: area?.treeData?.[0]?.code,
              province_name: area?.treeData?.[0]?.name,
              city: area?.treeData?.[0]?.children?.[0]?.code,
              city_name: area?.treeData?.[0]?.children?.[0]?.name,
            });
          }
        } else {
          form.resetFields();
        }
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={async (info) => {
        // if (info.id) {
        //   // 更新时，可以清除某些字段的值
        //   const { province, city, area: areaCode } = info;
        //   if (province) {
        //     info.province_name = area.list.find(
        //       (item) => item.code === province,
        //     )?.name;
        //   } else {
        //     info.province = '';
        //     info.province_name = '';
        //   }
        //   if (city) {
        //     info.city_name = area.list.find((item) => item.code === city)?.name;
        //   } else {
        //     info.city = '';
        //     info.city_name = '';
        //   }
        //   if (areaCode) {
        //     info.area_name = area.list.find(
        //       (item) => item.code === areaCode,
        //     )?.name;
        //   } else {
        //     info.area = '';
        //     info.area_name = '';
        //   }
        // }
        onSave(info);
      }}
    >
      <Divider orientation="left">基础信息</Divider>
      <ProFormText name="id" label="ID" hidden />
      <ProFormText name="province_name" label="province_name" hidden />
      <ProFormText name="city_name" label="city_name" hidden />
      <ProFormText name="area_name" label="area_name" hidden />
      <ProForm.Group colProps={{ span: 12 }}>
        <ProFormText
          name="name"
          label="名称"
          rules={[{ required: true, message: '请输入名称！' }]}
        />
        <ProFormText
          name="code"
          label="编号"
          rules={[{ required: true, message: '请输入单位编号！' }]}
          readonly={!!info}
        />
        <ProFormText name="en_name" label="英文名称" />
      </ProForm.Group>
      <ProForm.Group colProps={{ span: 12 }}>
        <ProFormImg name="logo" label="LOGO" initialValue={info?.logo} />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSegmented
          name="type"
          label="类型"
          request={async () =>
            dictionarie.list
              .filter((item) => item.type === 'enterprise_type')
              .map((item) => ({
                label: item.name,
                value: item.code,
              }))
          }
          rules={[{ required: true, message: '请选择单位类型！' }]}
          colProps={{ span: 12 }}
        />
        <ProFormDependency name={['type']}>
          {({ type }) => {
            if (type?.toUpperCase() === 'SCHOOL') {
              return (
                <>
                  <ProFormSelect
                    name="school_system"
                    label="学制"
                    options={dictionarie.list
                      .filter((item) => item.type === 'school_system')
                      .map((item) => ({
                        label: item.name,
                        value: item.code,
                      }))}
                    rules={[{ required: true, message: '请输入学制！' }]}
                    onChange={(_, option: any) => {
                      form.setFieldValue('school_system_name', option?.label);
                    }}
                    colProps={{ span: 12 }}
                  />
                  <ProFormText
                    name="school_system_name"
                    label="学制名称"
                    hidden
                  />
                </>
              );
            }
          }}
        </ProFormDependency>
      </ProForm.Group>
      <Divider orientation="left">扩展信息</Divider>
      <ProForm.Group>
        <ProFormTreeSelect
          name="province"
          label="所属省"
          request={async () =>
            area.treeData.map((d) => ({ ...d, children: undefined }))
          }
          readonly
          rules={[{ required: true, message: '请输入所属省份！' }]}
          colProps={{ span: 12 }}
        />
        <ProFormDependency name={['province']}>
          {({ province }, form) => {
            if (!province) {
              form.setFieldValue('city', undefined);
            }
            return null;
          }}
        </ProFormDependency>
        <ProFormTreeSelect
          name="city"
          label="所属市"
          tooltip="跨级直属请选择上一级区域"
          dependencies={['province']}
          rules={[{ required: true, message: '请输入所属市！' }]}
          request={async ({ province }) => {
            if (province) {
              return area.treeData
                .filter((d) => (d.code = province))
                ?.map((d) => ({
                  ...d,
                  children: d.children?.map((cd) => ({
                    ...cd,
                    children: undefined,
                  })),
                }));
            }
            return [];
          }}
          fieldProps={{
            treeDefaultExpandAll: true,
            onSelect: (_, node) => {
              form.setFieldValue('city_name', node.name);
            },
          }}
          colProps={{ span: 12 }}
        />
        <ProFormDependency name={['city']}>
          {({ city }, form) => {
            if (!city) {
              form.setFieldValue('area', undefined);
            }
            return null;
          }}
        </ProFormDependency>
        <ProFormTreeSelect
          name="area"
          label="所属区/县"
          tooltip="跨级直属请选择上一级区域"
          dependencies={['province', 'city']}
          rules={[{ required: true, message: '请输入所属区/县！' }]}
          request={async ({ province, city }) => {
            if (province && city) {
              // 省直属只能选省
              if (province === city) {
                return area.treeData
                  .filter((d) => (d.code = province))
                  ?.map((d) => ({
                    ...d,
                    children: undefined,
                  }));
              }
              return area.treeData
                .filter((d) => (d.code = province))
                ?.map((d) => ({
                  ...d,
                  children: d.children?.filter((cd) => cd.code === city),
                }));
            }
            return [];
          }}
          fieldProps={{
            treeDefaultExpandAll: true,
            onSelect: (_, node) => {
              form.setFieldValue('area_name', node.name);
            },
          }}
          colProps={{ span: 12 }}
        />
        <ProFormText name="address" label="单位地址" colProps={{ span: 12 }} />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText name="liaison" label="联系人" colProps={{ span: 12 }} />
        <ProFormText name="mobile" label="联系电话" colProps={{ span: 12 }} />
      </ProForm.Group>
      <ProFormTextArea
        name="describe"
        label="单位简介"
        colProps={{ span: 24 }}
      />
    </ModalForm>
  );
};

export default connect(({ dictionarie, area }) => ({ dictionarie, area }))(
  EditModal,
);
