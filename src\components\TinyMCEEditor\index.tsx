import { Editor } from '@tinymce/tinymce-react';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';

interface TinyMCEEditorProps {
  content?: string;
  value?: string;
  id?: string;
  height?: number;
  isForm?: boolean;
  readonly?: boolean;
  onEditorInit?: (editor: any) => void; // 用于通知父组件该编辑器已初始化
  editorChange?: (content: string, text: string) => void; // 用于通知父组件编辑器内容变化
  editorHeightChange?: (height: number) => void; // 用于通知父组件编辑器高度变化
}

const TinyMCEEditor: React.FC<TinyMCEEditorProps> = ({
  content,
  value,
  id,
  readonly = false,
  onEditorInit,
  editorChange,
  editorHeightChange,
  isForm = false,
}) => {
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [isEditorVisible, setEditorVisible] = useState(false);
  useEffect(() => {
    // 检查挂载toolbar的dom是否已经渲染
    if (toolbarRef?.current) {
      setEditorVisible(true);
    }
  }, []);
  if (isForm) {
    return (
      <div className={classNames(styles.formWrappper, styles.editorWrappper)}>
        <div ref={toolbarRef} className={styles.editorToolBarWrappper} />
        {isEditorVisible && (
          <Editor
            id={id}
            // 这里设置本地脚本文件的路径
            tinymceScriptSrc="https://www.xingjiaoyun.cn/tinymce/tinymce.min.js"
            initialValue={value || content}
            init={{
              menubar: false,
              inline: true,
              fixed_toolbar_container_target: toolbarRef?.current
                ? toolbarRef.current
                : undefined,
              content_style:
                '.mce-content-body { font-size:14px ;line-height: 30px; margin: 4px;} .mce-content-body p{ margin: 0;}',
              language: 'zh_CN',
              plugins: [
                'advlist',
                'autolink',
                'lists',
                'link',
                'image',
                'charmap',
                'preview',
                'anchor',
                'searchreplace',
                'visualblocks',
                'formatpainter',
                'code',
                'fullscreen',
                'insertdatetime',
                'media',
                'table',
                'code',
                'help',
                'wordcount',
                'kityformula-editor',
                'text-emphasis',
                'text-wave',
                'double-underline',
                'importword',
                'lineheight',
                'importword',
                'imagetools',
                'letterspacing',
                'layout',
                'clearhtml',
                'paste',
              ],
              toolbar:
                'fontselect fontsizeselect | bold italic | alignleft aligncenter ' +
                'alignjustify ' +
                'bullist numlist outdent indent lineheight letterspacing | underline strikethrough | text-emphasis clearhtml text-wave double-underline kityformula-editor table charmap | ' +
                'formatpainter removeformat importword | help',
              placeholder: '请输入内容',
              font_formats:
                '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;' +
                '宋体=SimSun,serif;' +
                '楷体=KaiTi,STKaiti,serif;' +
                '仿宋=FangSong,STFangsong,serif;' +
                '等线=DengXian,Microsoft YaHei,sans-serif;' +
                '黑体=SimHei,Microsoft YaHei,sans-serif;' +
                'Arial=Arial,Helvetica,sans-serif;' +
                'Times New Roman=Times New Roman,Times,serif;' +
                'Courier New=Courier New,Courier,monospace;' +
                'Verdana=Verdana,Geneva,sans-serif;' +
                'Tahoma=Tahoma,Geneva,sans-serif;' +
                'Trebuchet MS=Trebuchet MS, sans-serif;' +
                'Impact=Impact, Charcoal, sans-serif;' +
                'Comic Sans MS=Comic Sans MS, cursive;' +
                'Arial Black=Arial Black, Gadget, sans-serif;' +
                'Lucida Sans Unicode=Lucida Sans Unicode, Lucida Grande, sans-serif;' +
                'Palatino Linotype=Palatino Linotype, Book Antiqua, Palatino, serif;',
              fontsize_formats:
                '10px 12px 14px 16px 18px 20px 22px 24px 26px 28px 30px 32px 34px 36px 38px 40px',
              lineheight_formats: '1 1.2 1.5 1.7 2 2.5 3 3.5 4 4.5 5',
              init_instance_callback: (editor) => {
                if (readonly) {
                  editor.mode.set('readonly');
                }
              },
              paste_data_images: true,
              paste_preprocess: function (plugin, args) {
                // 删除空段落
                args.content = args.content.replace(
                  /<p>(<[^>]+>)*(\s|&nbsp;)*(<\/[^>]+>)*<\/p>/g,
                  '',
                );
              },
            }}
            onBlur={(evt, editor) => {
              // 失去焦点时触发的事件
              const c = editor.getContent(),
                e = editor.getElement(),
                w = editor.getContent({ format: 'text' });
              editorChange?.(c, w);
              if (editorHeightChange) {
                e.style.minHeight = '0px';
                const t = e.getBoundingClientRect().height;
                editorHeightChange?.(Math.ceil(t));
                e.style.minHeight = Math.ceil(t) + 'px';
              }
            }}
            onInit={(evt, editor) => {
              // 编辑器
              onEditorInit?.(editor);
            }}
          />
        )}
      </div>
    );
  } else {
    return (
      <div className={styles.editorWrappper}>
        <div ref={toolbarRef} className={styles.editorToolBarWrappper} />
        {isEditorVisible && (
          <Editor
            id={id}
            // 这里设置本地脚本文件的路径
            tinymceScriptSrc="https://www.xingjiaoyun.cn/tinymce/tinymce.min.js"
            initialValue={value || content}
            init={{
              menubar: false,
              inline: true,
              fixed_toolbar_container_target: toolbarRef?.current
                ? toolbarRef.current
                : undefined,
              content_style:
                '.mce-content-body { font-size:14px ;line-height: 30px; margin: 4px;} .mce-content-body p{ margin: 0;}',
              language: 'zh_CN',
              plugins: [
                'advlist',
                'autolink',
                'lists',
                'link',
                'image',
                'charmap',
                'preview',
                'anchor',
                'searchreplace',
                'visualblocks',
                'formatpainter',
                'code',
                'fullscreen',
                'insertdatetime',
                'media',
                'table',
                'code',
                'help',
                'wordcount',
                'kityformula-editor',
                'text-emphasis',
                'text-wave',
                'double-underline',
                'importword',
                'lineheight',
                'importword',
                'imagetools',
                'letterspacing',
                'layout',
                'clearhtml',
                'paste',
              ],
              toolbar:
                'fontselect fontsizeselect | bold italic | alignleft aligncenter ' +
                'alignjustify ' +
                'bullist numlist outdent indent lineheight letterspacing | underline strikethrough | text-emphasis clearhtml text-wave double-underline kityformula-editor table charmap | ' +
                'formatpainter removeformat importword | help',
              font_formats:
                '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;' +
                '宋体=SimSun,serif;' +
                '楷体=KaiTi,STKaiti,serif;' +
                '仿宋=FangSong,STFangsong,serif;' +
                '等线=DengXian,Microsoft YaHei,sans-serif;' +
                '黑体=SimHei,Microsoft YaHei,sans-serif;' +
                'Arial=Arial,Helvetica,sans-serif;' +
                'Times New Roman=Times New Roman,Times,serif;' +
                'Courier New=Courier New,Courier,monospace;' +
                'Verdana=Verdana,Geneva,sans-serif;' +
                'Tahoma=Tahoma,Geneva,sans-serif;' +
                'Trebuchet MS=Trebuchet MS, sans-serif;' +
                'Impact=Impact, Charcoal, sans-serif;' +
                'Comic Sans MS=Comic Sans MS, cursive;' +
                'Arial Black=Arial Black, Gadget, sans-serif;' +
                'Lucida Sans Unicode=Lucida Sans Unicode, Lucida Grande, sans-serif;' +
                'Palatino Linotype=Palatino Linotype, Book Antiqua, Palatino, serif;',
              fontsize_formats:
                '10px 12px 14px 16px 18px 20px 22px 24px 26px 28px 30px 32px 34px 36px 38px 40px',
              lineheight_formats: '1 1.2 1.5 1.7 2 2.5 3 3.5 4 4.5 5',
              init_instance_callback: (editor) => {
                if (readonly) {
                  editor.mode.set('readonly');
                }
              },
              paste_data_images: true,
              paste_preprocess: function (plugin, args) {
                // 删除空段落
                args.content = args.content.replace(
                  /<p>(<[^>]+>)*(\s|&nbsp;)*(<\/[^>]+>)*<\/p>/g,
                  '',
                );
              },
            }}
            onBlur={(evt, editor) => {
              // 失去焦点时触发的事件
              const c = editor.getContent(),
                e = editor.getElement(),
                w = editor.getContent({ format: 'text' });

              editorChange?.(c, w);
              if (editorHeightChange) {
                e.style.minHeight = '0px';
                const t = e.getBoundingClientRect().height;
                editorHeightChange?.(Math.ceil(t));
                e.style.minHeight = Math.ceil(t) + 'px';
              }
            }}
            onInit={(evt, editor) => {
              // 编辑器
              onEditorInit?.(editor);
            }}
          />
        )}
      </div>
    );
  }
};

export default TinyMCEEditor;
