import { Line } from '@ant-design/plots';
import React from 'react';

interface LineProps {
  height?: number;
  data: { year: string; value: number }[];
}

const LineChart: React.FC<LineProps> = ({ data, height = 300 }) => {
  const config = {
    height,
    data,
    xField: 'year',
    yField: 'value',
    point: {
      shapeField: 'square',
      sizeField: 4,
    },
    interaction: {
      tooltip: {
        marker: false,
      },
    },
    style: {
      lineWidth: 2,
    },
  };
  return <Line {...config} />;
};
export default LineChart;
