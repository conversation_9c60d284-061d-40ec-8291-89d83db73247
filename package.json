{"private": true, "author": "朱鹏亮 <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev", "watch": " node ./scripts/watchServiceFile.js"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/plots": "^2.3.3", "@ant-design/pro-components": "^2.4.4", "@tinymce/tinymce-react": "^5.1.1", "@umijs/max": "^4.4.4", "@wangeditor-next/editor": "^5.6.32", "@wangeditor-next/editor-for-react": "^1.0.9", "antd": "^5.4.0", "classnames": "^2.5.1", "cos-js-sdk-v5": "^1.8.7", "dayjs": "^1.11.13", "docx": "^9.2.0", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "html-docx-js": "^0.3.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.0", "lodash-es": "^4.17.21", "moment": "^2.30.1", "nanoid": "^5.1.5", "react-beautiful-dnd": "^13.1.1", "react-sticky-box": "^2.0.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/html-docx-js": "^0.3.4", "@types/lodash-es": "^4.17.12", "@types/react": "^18.0.33", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.0.11", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}