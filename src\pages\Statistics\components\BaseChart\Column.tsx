import { Column } from '@ant-design/plots';
import React from 'react';
import NoData<PERSON>hart from './NoDataChart';

interface ColumnChartProps {
  loading?: boolean;
  data?: {
    label: string;
    value: number;
    subject: string;
  }[];
}

const ColumnChart: React.FC<ColumnChartProps> = ({ data, loading }) => {
  const config = {
    height: 400,
    data,
    xField: 'label',
    yField: 'value',
    colorField: 'subject',
    stack: true,
    sort: {
      reverse: true,
      by: 'y',
    },
    style: {
      // 圆角样式
      radiusTopLeft: 10,
      radiusTopRight: 10,
      maxWidth: 30,
    },
    axis: {
      y: { labelFormatter: '~s' },
      x: {
        labelSpacing: 4,
        style: {
          labelTransform: 'rotate(0)',
        },
      },
    },
  };
  return (
    <NoDataChart data={data} loading={loading}>
      <Column {...config} />
    </NoDataChart>
  );
};
export default ColumnChart;
