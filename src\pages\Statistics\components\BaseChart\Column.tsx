import { Column } from '@ant-design/plots';
import React from 'react';
import NoData<PERSON>hart from './NoDataChart';

interface ColumnChartProps {
  loading?: boolean;
  data?: {
    label: string;
    value: number;
    subject: string;
    grade_section_name: string;
    teacher_name?: string;
  }[];
}

const ColumnChart: React.FC<ColumnChartProps> = ({ data, loading }) => {
  const scrollRatio =
    data && data.length > 30 ? Math.min(0.8, 30 / data.length) : undefined;
  const isTeacher = !!data?.[0]?.teacher_name;
  const config = {
    height: 400,
    data,
    xField: 'label',
    yField: 'value',
    seriesField: isTeacher ? undefined : 'grade_section_name', // 分组字段
    colorField: 'subject',
    stack: isTeacher
      ? undefined
      : {
          groupBy: ['x', 'series'],
          series: false,
        },
    sort: {
      reverse: true,
      by: 'y',
    },
    isGroup: isTeacher ? false : true, // 明确启用分组模式
    dodgePadding: 4, // 分组间距（0-4）
    intervalPadding: 2, // 柱子间距（2-8）
    style: {
      radiusTopLeft: 20,
      radiusTopRight: 20,
      maxWidth: 30, // 减小柱子宽度（16-30）
    },
    tooltip: (item: any) => {
      return { origin: item };
    },
    scrollbar:
      data && data.length > 30
        ? {
            type: 'horizontal',
            x: { ratio: scrollRatio },
          }
        : undefined,
    axis: {
      y: { labelFormatter: '~s' },
      x: {
        labelSpacing: 4,
        style: {
          labelTransform: 'rotate(0)',
        },
      },
    },
    interaction: {
      tooltip: {
        render: (_e: any, { title, items }: any) => {
          return (
            <div key={title}>
              <h4>{title}</h4>
              {items.map((item: any) => {
                const { name, color, origin } = item;
                return (
                  <div key={name + color + origin?.paramkey}>
                    <div
                      style={{
                        margin: 0,
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <div>
                        <span
                          style={{
                            display: 'inline-block',
                            width: 6,
                            height: 6,
                            borderRadius: '50%',
                            backgroundColor: color,
                            marginRight: 6,
                          }}
                        ></span>
                        <span>
                          {name}
                          {origin['subject']}
                        </span>
                      </div>
                      <b>{origin['value']}</b>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        },
      },
    },
  };
  return (
    <NoDataChart data={data} loading={loading}>
      <Column {...config} />
    </NoDataChart>
  );
};
export default ColumnChart;
