export const convertStructure = (structure: any) => {
  let data: any = {
    bigQuestions: {},
    bigQuestionOrder: [],
  };
  if (structure) {
    for (let i = 0; i < structure.length; i++) {
      const value = structure[i];
      // 检查 value 是否存在且为对象
      if (
        value &&
        typeof value === 'object' &&
        value.children &&
        Array.isArray(value.children)
      ) {
        try {
          // 提取 children 中的 parent_question_id 或 question_bank_id
          const ids = value.children
            .map(
              (child: {
                parent_question_id?: number;
                question_bank_id?: number;
              }) => child.parent_question_id || child.question_bank_id || null, // 默认值为 null
            )
            .filter((id: string) => id !== null); // 过滤掉无效的 null 值

          // 确保 ids 不为空数组后再进行处理
          if (ids.length > 0) {
            data.bigQuestions[value.id] = {
              id: value.id,
              name: value.name || '', // 确保 name 不为 undefined
              questionIds: [...new Set(ids)], // 去重后的 ids
            };
            data.bigQuestionOrder.push(value.id);
          }
        } catch (error) {
          console.error(`处理数据结构时发生错误:`, error);
        }
      }
    }
  }
  return data;
};
