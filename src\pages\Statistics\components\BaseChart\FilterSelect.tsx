import ConditionalRender from '@/components/ConditionalRender';
import { DictionarieState } from '@/models/dictionarie';
import { index as areaAPI } from '@/services/area';
import { index as semesterAPI } from '@/services/semesters';
import { index } from '@/services/subjects';
import { convertSystemToGrade, convertToTreeData } from '@/utils/calc';
import { connect, useModel } from '@umijs/max';
import { message, Select, Space, TreeSelect } from 'antd';
import React, { Dispatch, useEffect, useState } from 'react';

interface FilterSelectProps {
  mode: 'global' | 'grade';
  type?: 'city' | 'county' | 'school';
  oriType?: 'city' | 'county' | 'school';
  onChange?: (value: any) => void;
  dictionarie?: DictionarieState;
  gradeOption?: any[];
  gradeSection?: string;
}

const FilterSelect: React.FC<FilterSelectProps> = ({
  mode,
  oriType,
  dictionarie,
  onChange,
  gradeSection,
  gradeOption,
}) => {
  const { initialState } = useModel('@@initialState');
  /** 区县 */
  const [countyOptions, setCountyOptions] = useState<any[]>([]);
  const [selectedCounty, setSelectedCounty] = useState<string | undefined>();
  /** 学校类型 */
  const [schoolTypeOptions, setSchoolTypeOptions] = useState<any[]>([]);
  const [selectedSchoolType, setSelectedSchoolType] = useState<string>();

  /** 学年学期 */
  const [yearTermOptions, setYearTermOptions] = useState<any[]>([]);
  const [selectedYearTerm, setSelectedYearTerm] = useState<string>();
  /** 年级 */
  const [gradeOptions, setGradeOptions] = useState<any[]>([]);
  const [selectedGrade, setSelectedGrade] = useState<string>();
  /** 科目 */
  const [subjectOptions, setSubjectOptions] = useState<any[]>([]);
  const [selectedSubject, setSelectedSubject] = useState<string>();
  /** 设置 options */
  const setOptions = (
    type: string,
    setOptions: Dispatch<React.SetStateAction<any[]>>,
  ) => {
    if (!dictionarie?.list) setOptions([]);
    const newOptions = dictionarie?.list
      .filter((item) => item?.type === type)
      .map((item) => ({
        label: item.name ?? '',
        value: item.code ?? '',
      }));
    if (oriType === 'school') {
      if (type === 'grade_section') {
        const sectionData = convertSystemToGrade(
          'section',
          initialState?.enterprise?.school_system,
          newOptions,
        );
        console.log(sectionData);

        const { roles } = initialState as any;

        // 获取用户的管理角色类型
        const adminRoles = roles
          .filter((v: any) => v.name.includes('管理员'))
          .map((v: any) => v.name.substring(0, 2)); // 提取角色前缀，如"小学"、"初中"
        console.log(adminRoles);

        // 如果有管理权限，则过滤出对应的学段
        const filteredSectionData =
          adminRoles.length > 0
            ? sectionData.filter((v: any) =>
                adminRoles.some((role: any) => v.label.includes(role)),
              )
            : sectionData;

        // 如果过滤后只有一个选项，自动选中
        if (filteredSectionData?.length === 1) {
          setSelectedSchoolType(filteredSectionData[0].value);
        }

        setOptions(filteredSectionData);
      }
      if (type === 'grade') {
        const gradeData = convertSystemToGrade(
          'gradeCode',
          initialState?.enterprise?.school_system,
          newOptions,
        );
        setOptions(gradeData);
      }
    } else {
      setOptions(newOptions || []);
    }
  };

  /** 获取学年学期 */
  const getYearTermOptions = async () => {
    const { errCode, data, msg } = await semesterAPI({});
    if (errCode) {
      message.warning(`获取学年学期失败，请稍后重试 ${msg}`);
      setYearTermOptions([]);
      return;
    }
    const newOptions = data?.list?.map((item) => ({
      label: `${item.year}学年 ${item.term === 1 ? '第一学年' : '第二学年'}`,
      value: item.code,
    }));
    const activeYearTerm = data?.list?.find((item) => item.status === 1)?.code;
    setYearTermOptions(newOptions || []);
    setSelectedYearTerm(activeYearTerm);
  };

  /** 获取省市区 */
  const getAreaOptions = async () => {
    let filteredList: any[] = [];
    const { errCode, data, msg } = await areaAPI({});
    if (errCode) {
      message.warning(`获取省市区失败，请稍后重试 ${msg}`);
      setCountyOptions([]);
      return;
    }
    const { city } = initialState?.enterprise || {};

    if (city) {
      filteredList = data.list
        ? data.list.filter((item) => item.parentCode === city)
        : [];
    } else {
      filteredList = data.list || [];
    }

    data?.list?.forEach((item) => {
      if (item.name.includes('市辖区')) {
        const parent = data?.list?.find(
          (v) => v.code === item?.parentCode,
        )?.name;
        item.name = parent + '-' + item.name;
      }
    });

    setCountyOptions(() => convertToTreeData(filteredList) || []);
  };

  /** 获取科目 */
  const getSubjectOptions = async (value?: string) => {
    const { errCode, data, msg } = await index({});
    if (errCode) {
      message.warning(`获取科目失败，请稍后重试 ${msg}`);
      setSubjectOptions([]);
      return;
    }
    const newOptions = convertSystemToGrade(
      'subject',
      value || initialState?.enterprise?.school_system,
      data?.list,
    );
    setSubjectOptions(newOptions);
  };

  useEffect(() => {
    if (mode === 'global' && dictionarie?.list) {
      setOptions('grade_section', setSchoolTypeOptions);
      getYearTermOptions();
      getAreaOptions();
    } else {
      setSelectedGrade(undefined);
      setSelectedSubject(undefined);
      if (gradeOption && gradeOption.length > 0) {
        setGradeOptions(gradeOption);
      } else {
        setOptions('grade', setGradeOptions);
      }
    }
  }, [mode, dictionarie, gradeOption]);

  useEffect(() => {
    if (mode === 'grade' || gradeSection) {
      getSubjectOptions(gradeSection);
    }
  }, [mode, gradeSection]);

  useEffect(() => {
    if (mode === 'global') {
      onChange?.({
        county: selectedCounty,
        schoolType: selectedSchoolType,
        yearTerm: selectedYearTerm,
        grade: selectedGrade,
        subject: selectedSubject,
      });
    } else {
      onChange?.({
        grade: selectedGrade,
        subject: selectedSubject,
      });
    }
  }, [
    selectedCounty,
    selectedSchoolType,
    selectedYearTerm,
    selectedGrade,
    selectedSubject,
  ]);
  return (
    <>
      <ConditionalRender
        hasAccess={mode === 'global'}
        accessComponent={
          <Space>
            <div>
              <label htmlFor="">区/县：</label>
              <ConditionalRender
                hasAccess={oriType === 'city'}
                accessComponent={
                  <TreeSelect
                    value={selectedCounty}
                    onChange={(value) => {
                      setSelectedCounty(value);
                    }}
                    showSearch
                    style={{ width: 200 }}
                    allowClear
                    treeDefaultExpandAll
                    treeData={countyOptions}
                    filterTreeNode={(inputValue, treeNode) => {
                      const title = treeNode.title;
                      if (typeof title === 'string') {
                        return title
                          .toLowerCase()
                          .includes(inputValue.toLowerCase());
                      }
                      return false;
                    }}
                  />
                }
                noAccessComponent={
                  <span>
                    {initialState?.enterprise?.city_name}
                    {initialState?.enterprise?.area_name}
                  </span>
                }
              />
            </div>
            <div>
              <label htmlFor="">学段：</label>
              <Select
                allowClear
                style={{ width: 200 }}
                options={schoolTypeOptions}
                value={selectedSchoolType}
                onChange={(value) => setSelectedSchoolType(value)}
              />
            </div>
            <div>
              <label htmlFor="">学年学期：</label>
              <Select
                style={{ width: 200 }}
                options={yearTermOptions}
                value={selectedYearTerm}
                onChange={(value) => setSelectedYearTerm(value)}
              />
            </div>
          </Space>
        }
      />
      <ConditionalRender
        hasAccess={mode === 'grade'}
        accessComponent={
          <Space>
            <div>
              <label htmlFor="">年级：</label>
              <Select
                value={selectedGrade}
                style={{ width: 120 }}
                allowClear
                options={gradeOptions}
                onChange={(value) => {
                  setSelectedSubject(undefined);
                  setSelectedGrade(value);
                  // 优化后的查找逻辑
                  // const sectionGradeEntries = Object.entries(SECTION_GRADE);
                  // const foundSection = sectionGradeEntries.find(
                  //   ([, grades]) => grades.some((grade) => grade === value),
                  // );
                  // getSubjectOptions(foundSection?.[0]);
                }}
              />
            </div>
            <div>
              <label htmlFor="">科目：</label>
              <Select
                value={selectedSubject}
                style={{ width: 120 }}
                allowClear
                options={subjectOptions}
                onChange={(value) => setSelectedSubject(value)}
              />
            </div>
          </Space>
        }
      />
    </>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(FilterSelect);
