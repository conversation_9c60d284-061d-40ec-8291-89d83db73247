import { request } from '@umijs/max';

/**
 * 使用第三方SSO令牌获取用户信息
 *
 * 这个接口只用来解析第三方令牌，前端在频繁获取到token时，会调用这个接口与自身登录信息进行对比，以确定是否需要重新登录
 */
export async function getUserInfoBySsoToken(ssoToken: string) {
  console.log('getUserInfoBySsoToken', ssoToken);
  return request<API.ResType<API.User>>('/sso/userInfo/ssoToken', {
    method: 'GET',
    params: {
      ssoToken,
    },
  });
}

export async function loginWithSsoToken(ssoToken: string) {
  return request<API.ResType<{ token: string; expiresIn: number }>>(
    '/sso/login/ssoToken',
    {
      method: 'POST',
      data: {
        ssoToken,
      },
    },
  );
}

/** 请求token POST /sso/callback */
export async function getToken(params: API.TokenQueryInfo) {
  return request<API.ResType<{ token: string; expiresIn: number }>>(
    '/sso/callback',
    {
      method: 'GET',
      params,
    },
  );
}

/** 请求token POST /sso/refresh */
export async function refreshToken(token: string) {
  return request<API.ResType<{ token: string; expiresIn: number }>>(
    '/sso/refresh',
    {
      params: {
        refreshToken: token,
      },
    },
  );
}

export async function currentUser(token: string) {
  return request<API.ResType<API.User>>('/sso/userinfo', {
    params: {
      access_token: token,
    },
  });
}

export async function doLogout(access_token: string) {
  return request('/sso/logout', {
    params: {
      access_token,
    },
  });
}

/** 本地登录接口 */
export async function localLogin(params: {
  username: string;
  password: string;
}) {
  return request<API.ResType<{ token: string; expiresIn: number }>>(
    '/auth/login',
    {
      method: 'POST',
      data: params,
    },
  );
}

/** 本地获取当前用户信息 */
export async function localCurrentUser() {
  return request<API.ResType<API.User>>('/auth/userinfo');
}
