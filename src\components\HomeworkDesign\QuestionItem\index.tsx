import { convertSpecialNumber } from '@/utils/calc';
import classNames from 'classnames';
import styles from './index.less';
/**
 * 选择题
 * @param param0
 * @returns
 */
export const ChoiceItem = ({
  settingScore = false,
  data,
}: {
  data: any;
  settingScore?: boolean;
}) => {
  const { positionQuestion, showDuration, question } = data;
  const { options = [] } = question || {};
  const valuesArray: string[] = options.map((obj: any) => {
    if (typeof obj === 'object') {
      return Object.values(obj)[0];
    } else {
      return obj;
    }
  });

  return (
    <div
      onClick={(e) => {
        if (e.currentTarget && e.currentTarget.children?.length > 1) {
          const editNode: any = e.currentTarget.children[1];
          if (editNode.style.display === 'block') {
            editNode.style.display = 'none';
          } else {
            editNode.style.display = 'block';
          }
        }
      }}
    >
      <div className={styles.questionItem}>
        <div
          className={styles.questionItemContent}
          dangerouslySetInnerHTML={{
            __html:
              (showDuration
                ? '<div title="预计作答时长：' +
                  (question?.duration || 0) +
                  '分钟" style="margin-right:10px; padding: 5px 0;cursor: pointer;display: flex;align-items: center;height: 32px;"><svg t="1746772922118" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5233" width="18" height="18"><path d="M512 924.266a316.3 316.3 0 0 0 170.893-48.64 346.792 346.792 0 0 0 122.025-128.91 361.526 361.526 0 0 0 45.34-178.8c0-64.852-15.132-124.414-45.34-178.743A349.465 349.465 0 0 0 682.893 261.46 322.273 322.273 0 0 0 512 214.014c-62.008 0-118.897 15.815-170.893 47.445a349.465 349.465 0 0 0-122.025 127.714 361.526 361.526 0 0 0-45.34 178.744c0 64.852 15.132 124.472 45.34 178.8a346.792 346.792 0 0 0 122.025 128.909A316.3 316.3 0 0 0 512 924.266z m0-812.367c79.814 0 153.428 20.65 220.84 62.009a427.516 427.516 0 0 1 155.761 162.985c39.538 70.542 59.278 147.512 59.278 231.024s-19.74 160.539-59.278 231.08a427.516 427.516 0 0 1-155.76 162.986A414.489 414.489 0 0 1 512 1023.99a414.489 414.489 0 0 1-220.84-62.008 427.516 427.516 0 0 1-155.761-162.986C95.86 728.456 76.12 651.43 76.12 567.917s19.74-160.482 59.278-231.024a427.516 427.516 0 0 1 155.76-162.985A414.489 414.489 0 0 1 512 111.899z m25.6 204.287v268.797L730.508 705.36l-39.48 62.008-226.7-147.113V316.186H537.6zM314.37 83.91L90.059 276.08l-61.61-76.63L250.484 7.283 314.37 83.91zM995.55 199.394l-61.61 76.685-224.31-199.45L773.515 0 995.55 199.45z" p-id="5234" fill="#1989fa"></path></svg>&nbsp;' +
                  (question?.duration || 0) +
                  '’' +
                  '</div>'
                : '') +
              (positionQuestion !== undefined
                ? '<div class="questionNum">' + positionQuestion + '、'
                : '') +
              (settingScore ? `（ ${question?.score || 0} 分）` : '') +
              '</div><div class="questionTitle">' +
              (question?.name || '') +
              '</div>',
          }}
        />
        <div className={styles.questionOption}>
          <ul key={question?.id}>
            {valuesArray?.map((item: any, index: number) => {
              const label = String.fromCharCode(64 + index + 1);
              return (
                <li key={label + question?.id} value={label}>
                  {label}、
                  <span dangerouslySetInnerHTML={{ __html: item }} />
                </li>
              );
            })}
          </ul>
        </div>
      </div>
      <div className={classNames(styles.questionAnswerArea, styles.hide)}>
        <div
          className={styles.questionAnswerItem}
          dangerouslySetInnerHTML={{
            __html:
              '<div class="questionPoint">【答案】</div><div class="questionTitle">' +
              (question?.answer ? question?.answer : '') +
              '</div>',
          }}
        />
        <div
          className={styles.questionAnswerItem}
          dangerouslySetInnerHTML={{
            __html:
              '<div class="questionPoint">【解析】</div><div class="questionTitle">' +
              (question?.analysis ? question?.analysis : '') +
              '</div>',
          }}
        />
      </div>
    </div>
  );
};
/**
 * 判断题
 */
export const JudgeItem = ({
  settingScore = false,
  data,
}: {
  data: any;
  settingScore?: boolean;
}) => {
  const { positionQuestion, showDuration, question } = data;
  return (
    <div
      onClick={(e) => {
        if (e.currentTarget && e.currentTarget.children?.length > 1) {
          const editNode: any = e.currentTarget.children[1];
          if (editNode.style.display === 'block') {
            editNode.style.display = 'none';
          } else {
            editNode.style.display = 'block';
          }
        }
      }}
    >
      <div className={styles.questionItem} key={question?.id}>
        <div
          className={styles.questionItemContent}
          dangerouslySetInnerHTML={{
            __html:
              (showDuration
                ? '<div title="预计作答时长：' +
                  (question?.duration || 0) +
                  '分钟" style="margin-right:10px; padding: 5px 0;cursor: pointer;display: flex;align-items: center;height: 32px;"><svg t="1746772922118" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5233" width="18" height="18"><path d="M512 924.266a316.3 316.3 0 0 0 170.893-48.64 346.792 346.792 0 0 0 122.025-128.91 361.526 361.526 0 0 0 45.34-178.8c0-64.852-15.132-124.414-45.34-178.743A349.465 349.465 0 0 0 682.893 261.46 322.273 322.273 0 0 0 512 214.014c-62.008 0-118.897 15.815-170.893 47.445a349.465 349.465 0 0 0-122.025 127.714 361.526 361.526 0 0 0-45.34 178.744c0 64.852 15.132 124.472 45.34 178.8a346.792 346.792 0 0 0 122.025 128.909A316.3 316.3 0 0 0 512 924.266z m0-812.367c79.814 0 153.428 20.65 220.84 62.009a427.516 427.516 0 0 1 155.761 162.985c39.538 70.542 59.278 147.512 59.278 231.024s-19.74 160.539-59.278 231.08a427.516 427.516 0 0 1-155.76 162.986A414.489 414.489 0 0 1 512 1023.99a414.489 414.489 0 0 1-220.84-62.008 427.516 427.516 0 0 1-155.761-162.986C95.86 728.456 76.12 651.43 76.12 567.917s19.74-160.482 59.278-231.024a427.516 427.516 0 0 1 155.76-162.985A414.489 414.489 0 0 1 512 111.899z m25.6 204.287v268.797L730.508 705.36l-39.48 62.008-226.7-147.113V316.186H537.6zM314.37 83.91L90.059 276.08l-61.61-76.63L250.484 7.283 314.37 83.91zM995.55 199.394l-61.61 76.685-224.31-199.45L773.515 0 995.55 199.45z" p-id="5234" fill="#1989fa"></path></svg>&nbsp;' +
                  (question?.duration || 0) +
                  '’' +
                  '</div>'
                : '') +
              (positionQuestion !== undefined
                ? '<div class="questionNum">' + positionQuestion + '、'
                : '') +
              (settingScore ? `（ ${question?.score || 0} 分）` : '') +
              '</div><div class="questionTitle">' +
              (question?.name || '') +
              '</div>',
          }}
        />
      </div>
      <div className={classNames(styles.questionAnswerArea, styles.hide)}>
        <div
          className={styles.questionAnswerItem}
          dangerouslySetInnerHTML={{
            __html:
              '<div class="questionPoint">【答案】</div><div class="questionTitle">' +
              (question?.answer ? question?.answer : '') +
              '</div>',
          }}
        />
        <div
          className={styles.questionAnswerItem}
          dangerouslySetInnerHTML={{
            __html:
              '<div class="questionPoint">【解析】</div><div class="questionTitle">' +
              (question?.analysis ? question?.analysis : '') +
              '</div>',
          }}
        />
      </div>
    </div>
  );
};

/**
 * 主观题，客观题等，暂时用一个组件处理
 * @param param0
 * @returns
 */
export const BraftItem = ({
  settingScore = false,
  data,
}: {
  data: any;
  settingScore?: boolean;
}) => {
  const { positionQuestion, showDuration, question } = data;
  return (
    <div
      onClick={(e) => {
        if (e.currentTarget && e.currentTarget.children?.length > 1) {
          const editNode: any = e.currentTarget.children[1];
          if (editNode.style.display === 'block') {
            editNode.style.display = 'none';
          } else {
            editNode.style.display = 'block';
          }
        }
      }}
    >
      <div className={styles.questionItem} key={question?.id}>
        <div
          className={styles.questionItemContent}
          dangerouslySetInnerHTML={{
            __html:
              (showDuration
                ? '<div title="预计作答时长：' +
                  (question?.duration || 0) +
                  '分钟" style="margin-right:10px; padding: 5px 0;cursor: pointer;display: flex;align-items: center;height: 32px;"><svg t="1746772922118" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5233" width="18" height="18"><path d="M512 924.266a316.3 316.3 0 0 0 170.893-48.64 346.792 346.792 0 0 0 122.025-128.91 361.526 361.526 0 0 0 45.34-178.8c0-64.852-15.132-124.414-45.34-178.743A349.465 349.465 0 0 0 682.893 261.46 322.273 322.273 0 0 0 512 214.014c-62.008 0-118.897 15.815-170.893 47.445a349.465 349.465 0 0 0-122.025 127.714 361.526 361.526 0 0 0-45.34 178.744c0 64.852 15.132 124.472 45.34 178.8a346.792 346.792 0 0 0 122.025 128.909A316.3 316.3 0 0 0 512 924.266z m0-812.367c79.814 0 153.428 20.65 220.84 62.009a427.516 427.516 0 0 1 155.761 162.985c39.538 70.542 59.278 147.512 59.278 231.024s-19.74 160.539-59.278 231.08a427.516 427.516 0 0 1-155.76 162.986A414.489 414.489 0 0 1 512 1023.99a414.489 414.489 0 0 1-220.84-62.008 427.516 427.516 0 0 1-155.761-162.986C95.86 728.456 76.12 651.43 76.12 567.917s19.74-160.482 59.278-231.024a427.516 427.516 0 0 1 155.76-162.985A414.489 414.489 0 0 1 512 111.899z m25.6 204.287v268.797L730.508 705.36l-39.48 62.008-226.7-147.113V316.186H537.6zM314.37 83.91L90.059 276.08l-61.61-76.63L250.484 7.283 314.37 83.91zM995.55 199.394l-61.61 76.685-224.31-199.45L773.515 0 995.55 199.45z" p-id="5234" fill="#1989fa"></path></svg>&nbsp;' +
                  (question?.duration || 0) +
                  '’' +
                  '</div>'
                : '') +
              (positionQuestion !== undefined
                ? '<div class="questionNum">' + positionQuestion + '、'
                : '') +
              (settingScore ? `（ ${question?.score || 0} 分）` : '') +
              '</div><div class="questionTitle">' +
              (question?.name || '') +
              '</div>',
          }}
        />
      </div>
      <div className={classNames(styles.questionAnswerArea, styles.hide)}>
        <div
          className={styles.questionAnswerItem}
          dangerouslySetInnerHTML={{
            __html:
              '<div class="questionPoint">【答案】</div><div class="questionTitle">' +
              (question?.answer ? question?.answer : '') +
              '</div>',
          }}
        />
        <div
          className={styles.questionAnswerItem}
          dangerouslySetInnerHTML={{
            __html:
              '<div class="questionPoint">【解析】</div><div class="questionTitle">' +
              (question?.analysis ? question?.analysis : '') +
              '</div>',
          }}
        />
      </div>
    </div>
  );
};
/**
 * 父子题
 * @param param0
 * @returns
 */
export const ParentQzItem = ({
  settingScore = false,
  data,
}: {
  data: any;
  settingScore?: boolean;
}) => {
  const { positionQuestion, showDuration, question } = data;
  const { _id, children, name } = question;
  return (
    <div key={_id}>
      <div
        style={{
          lineHeight: '32px',
          display: 'flex',
        }}
        dangerouslySetInnerHTML={{
          __html:
            (showDuration
              ? '<div title="预计作答时长：' +
                (question?.duration || 0) +
                '分钟" style="margin-right:10px; padding: 5px 0;cursor: pointer;display: flex;align-items: center;height: 32px;"><svg t="1746772922118" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5233" width="18" height="18"><path d="M512 924.266a316.3 316.3 0 0 0 170.893-48.64 346.792 346.792 0 0 0 122.025-128.91 361.526 361.526 0 0 0 45.34-178.8c0-64.852-15.132-124.414-45.34-178.743A349.465 349.465 0 0 0 682.893 261.46 322.273 322.273 0 0 0 512 214.014c-62.008 0-118.897 15.815-170.893 47.445a349.465 349.465 0 0 0-122.025 127.714 361.526 361.526 0 0 0-45.34 178.744c0 64.852 15.132 124.472 45.34 178.8a346.792 346.792 0 0 0 122.025 128.909A316.3 316.3 0 0 0 512 924.266z m0-812.367c79.814 0 153.428 20.65 220.84 62.009a427.516 427.516 0 0 1 155.761 162.985c39.538 70.542 59.278 147.512 59.278 231.024s-19.74 160.539-59.278 231.08a427.516 427.516 0 0 1-155.76 162.986A414.489 414.489 0 0 1 512 1023.99a414.489 414.489 0 0 1-220.84-62.008 427.516 427.516 0 0 1-155.761-162.986C95.86 728.456 76.12 651.43 76.12 567.917s19.74-160.482 59.278-231.024a427.516 427.516 0 0 1 155.76-162.985A414.489 414.489 0 0 1 512 111.899z m25.6 204.287v268.797L730.508 705.36l-39.48 62.008-226.7-147.113V316.186H537.6zM314.37 83.91L90.059 276.08l-61.61-76.63L250.484 7.283 314.37 83.91zM995.55 199.394l-61.61 76.685-224.31-199.45L773.515 0 995.55 199.45z" p-id="5234" fill="#1989fa"></path></svg>&nbsp;' +
                (question?.duration || 0) +
                '’' +
                '</div>'
              : '') +
            (positionQuestion !== undefined
              ? '<div class="questionNum" style="display: inline-block;vertical-align:text-top;line-height: 32px;">' +
                positionQuestion +
                '、</div>'
              : '') +
            (settingScore ? `（ ${question?.score || 0} 分）` : '') +
            '<div class="questionTitle" style="display: inline-block;vertical-align:text-top">' +
            name +
            '</div>',
        }}
      />
      {children?.map((question: any, ind: number) => {
        const { baseType = {}, _id } = question;
        const tmlx = baseType?.name;
        if (tmlx === '单选题' || tmlx === '多选题')
          return (
            <ChoiceItem
              key={_id}
              data={{
                positionQuestion: convertSpecialNumber(
                  ind + 1,
                  'numberToCircled',
                ),
                question,
              }}
            />
          );
        if (tmlx === '判断题')
          return (
            <JudgeItem
              key={_id}
              data={{
                positionQuestion: convertSpecialNumber(
                  ind + 1,
                  'numberToCircled',
                ),
                question,
              }}
            />
          );
        return (
          <BraftItem
            key={_id}
            data={{
              positionQuestion: convertSpecialNumber(
                ind + 1,
                'numberToCircled',
              ),
              question,
            }}
          />
        );
      })}
    </div>
  );
};
/**试题不存在 */
export const NoQuestion = ({ data }: { data: any }) => {
  const { positionQuestion } = data;
  return (
    <div>
      <div className={styles.questionItem} key={positionQuestion}>
        <div
          className={styles.questionItemContent}
          dangerouslySetInnerHTML={{
            __html:
              (positionQuestion !== undefined
                ? '<div class="questionNum">' + positionQuestion + '、'
                : '') +
              '</div><div class="questionTitle">' +
              '<div class="content" style="color: #ff5555">原试题已被删除，请更换试题</div>' +
              '</div>',
          }}
        />
      </div>
    </div>
  );
};
