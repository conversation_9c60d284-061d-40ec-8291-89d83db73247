.title {
  background: rgb(242, 121, 186);
}

.selectCard {
  margin-bottom: 16px;
}

.treeCard {
  min-height: 400px;
}

.treeNodeTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .actionButtons {
    visibility: hidden;
    margin-left: 16px;
  }

  &:hover {
    .actionButtons {
      visibility: visible;
    }
  }
}

.topBar {
  :global {
    .ant-tabs-nav-wrap {
      border-radius: 8px;
      background-color: #fff;
      padding: 0 20px;
    }
  }
}
