import { useCurrentAccess } from '@/common/useCurrentAccess';
import {
  templateHomeworkRemove,
  templateHomeworkUpdate,
} from '@/services/class_work_detail';
import { getCommonQuestions } from '@/services/common_question';
import { downPaperToWord } from '@/services/htmlToWord';
import {
  lessonWorkDesignDetailRemove,
  lessonWorkDesignDetailUpdate,
} from '@/services/lesson_work_design_detail';
import { getSSOToken } from '@/utils/auth';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import { Link, useLocation } from '@umijs/max';
import { Button, Popconfirm, Spin, message } from 'antd';
import { useEffect, useState } from 'react';
import CommonDesc from '../CommonDesc';
import ConditionalRender from '../ConditionalRender';
import styles from './index.less';
import QuestionPart from './PaperQuestionList/QuestionPart';

const QuestionList = ({
  homework,
  homeworkKsid,
  homeworkCatalogId,
  homeworkVolumeId,
  subjectId,
  setIsCheck,
  reload,
}: {
  homework: any;
  homeworkCatalogId?: string;
  homeworkKsid?: string;
  homeworkVolumeId?: string;
  subjectId?: any;
  setIsCheck?: React.Dispatch<React.SetStateAction<boolean>>;
  reload: () => Promise<void>;
}) => {
  const { pathname } = useLocation() as {
    state: API.TemplateWork;
    pathname: string;
  };
  const { isSysAdmin } = useCurrentAccess();
  const [listLoading, setListLoading] = useState<boolean>(false);
  const [listData, setListData] = useState<any>();
  const [isWordDownloading, setIsWordDownloading] = useState<boolean>(false);
  const homeworkDesign = pathname.includes('homeworkDesign');
  const queryParams = new URLSearchParams({
    ksId: homeworkKsid || '',
    subjectId: subjectId || '',
    ...(homeworkCatalogId && { catalogId: homeworkCatalogId }),
    ...(homeworkVolumeId && { volumeId: homeworkVolumeId }),
    homeworkId: homework?.id || '',
  }).toString();
  const deleteEvent = async () => {
    let result: any;
    if (homeworkDesign) {
      result = await lessonWorkDesignDetailRemove(homework.id);
    } else {
      result = await templateHomeworkRemove(homework.id);
    }
    if (result?.errCode) {
      message.warning('作业删除失败,请稍后重试！' + result?.msg);
    } else {
      message.success('删除成功');
      reload();
    }
  };
  const updateHomework = async (id?: string, status?: string) => {
    let result: any;
    if (homeworkDesign) {
      result = await lessonWorkDesignDetailUpdate(id || '', {
        status: status || '草稿',
      });
    } else {
      result = await templateHomeworkUpdate(id || '', {
        status: status || '草稿',
      });
    }
    if (result?.errCode) {
      message.warning('操作失败,请稍后重试！' + result?.msg);
      return;
    }
    message.success('操作成功');
    reload();
  };
  const handleDownloadWord = async () => {
    const { name, questions, structs } = homework;
    const curquestions = questions.map((item: any) => {
      return {
        question_id: item.question_id,
        source_table: item.source_table || item.tableName,
      };
    });
    try {
      setIsWordDownloading(true);
      const {
        errCode,
        data: responseData,
        msg,
      } = await downPaperToWord({
        name: name,
        struct: structs,
        questions: curquestions,
        options: {
          fontSize: 14,
          fontFamily:
            'Microsoft YaHei, Helvetica Neue, PingFang SC, sans-serif',
          lineHeight: 1.5,
        },
      });
      if (errCode) {
        setIsWordDownloading(false);
        return message.error('下载失败，请联系管理员后重试！' + msg);
      }
      const uint8Array = new Uint8Array(responseData.data);
      // 假设response.data是Buffer数据
      const blob = new Blob([uint8Array], { type: 'application/msword' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `${name}.doc`;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error('下载失败:', error);
    } finally {
      setIsWordDownloading(false);
    }
  };
  const getInitData = async (
    params: { question_id: string; source_table: string }[],
  ) => {
    setListLoading(true);
    const { errCode, data, msg } = await getCommonQuestions(params);
    if (errCode) {
      message.warning('数据获取失败,请稍后重试' + msg);
    }
    setListData(data);
    setListLoading(false);
  };
  useEffect(() => {
    if (homework) {
      const params = homework?.questions?.map((item: any) => {
        return {
          question_id: item.question_id,
          source_table: item.source_table || item.tableName,
        };
      });
      if (params?.length === 0 || !params) return;
      getInitData(params);
    } else {
      setListData([]);
    }
  }, [homework]);
  return (
    <div className={styles.containWrapper}>
      <div className={styles.titleBar}>
        <h3>{homework?.name}</h3>
        <ConditionalRender
          hasAccess={homeworkDesign || homework?.status === '草稿'}
          accessComponent={
            <div>
              {!homeworkDesign && (
                <Popconfirm
                  key="publish"
                  title="发布后当前版本不可再做修改，确认发布吗?"
                  onConfirm={() => updateHomework(homework?.id, '发布')}
                  okText="确定"
                  cancelText="取消"
                  placement="topRight"
                >
                  <Button type="text" icon={<RotateRightOutlined />}>
                    发布
                  </Button>
                </Popconfirm>
              )}
              <Button
                type="text"
                icon={<VerticalAlignBottomOutlined />}
                onClick={handleDownloadWord}
                loading={isWordDownloading}
              >
                下载试卷
              </Button>
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => {
                  setIsCheck?.(true);
                }}
              >
                查看
              </Button>
              <Link
                to={`${pathname}/edit?ddtab=true&${queryParams}&isSysAdmin=${isSysAdmin}&token=${getSSOToken()}`}
                target="_blank"
                type="text"
                style={{ paddingInline: '15px' }}
              >
                <EditOutlined
                  style={{
                    marginInlineEnd: '5px',
                  }}
                />
                编辑
              </Link>
              <Popconfirm
                key="clear"
                title="确定删除当前作业吗？"
                description="删除后无法恢复，请谨慎操作！"
                onConfirm={deleteEvent}
              >
                <Button type="text" icon={<DeleteOutlined />}>
                  删除
                </Button>
              </Popconfirm>
            </div>
          }
          noAccessComponent={
            <div>
              <Button
                type="text"
                icon={<VerticalAlignBottomOutlined />}
                onClick={handleDownloadWord}
                loading={isWordDownloading}
              >
                下载试卷
              </Button>
              {!homework?.isHidden && (
                <Popconfirm
                  key="withdraw"
                  title="确认撤销当前作业范本的发布状态吗?"
                  onConfirm={() => updateHomework(homework?.id, '草稿')}
                  okText="确定"
                  cancelText="取消"
                  placement="topRight"
                >
                  <Button type="text" icon={<RotateLeftOutlined />}>
                    撤销
                  </Button>
                </Popconfirm>
              )}
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => {
                  setIsCheck?.(true);
                }}
              >
                查看
              </Button>
            </div>
          }
        />
      </div>
      <Spin tip="数据加载中..." spinning={listLoading}>
        {listData?.length ? (
          <div className={styles.questionList}>
            {listData?.map((item: any, ind: number) => {
              return (
                <QuestionPart
                  key={item?._id || ind}
                  data={{
                    question: item,
                    ind,
                  }}
                />
              );
            })}
          </div>
        ) : (
          <CommonDesc
            style={{
              margin: '0 auto',
            }}
            title="暂无试题"
            desc="请先添加试题"
            extra={
              <Link
                to={`${pathname}/edit?ddtab=true&ksId=${homeworkKsid}&catalogId=${homeworkCatalogId}&subjectId=${
                  homework?.subject_id
                }&homeworkId=${
                  homework?.id
                }&isSysAdmin=${isSysAdmin}&type=add&token=${getSSOToken()}`}
                target="_blank"
                type="text"
              >
                <Button type="primary">添加试题</Button>
              </Link>
            }
          />
        )}
      </Spin>
    </div>
  );
};

export default QuestionList;
