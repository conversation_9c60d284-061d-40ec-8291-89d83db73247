import ProFormImg from '@/components/ProFormItem/ProFormImg';
import ProFormSection from '@/components/ProFormItem/ProFormSection';
import WangEditor, { WangEditorRef } from '@/components/WangEditor';
import { SECTION } from '@/constants';
import { DictionarieState } from '@/models/dictionarie';
import { ModalForm, ProForm, ProFormText } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Col } from 'antd';
import React, { useRef } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Subject;
  onSave: (
    /** 要保存的信息 */
    info: API.Subject,
    /** 保存成功后的回调，让富文本组件从服务器上清除已删除的图片 */
    callback?: () => void,
  ) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const editorRef = useRef<WangEditorRef>(null);

  return (
    <ModalForm<API.Subject>
      title={info ? '编辑学科' : '添加学科'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={async (data) => {
        onSave(data, editorRef.current?.clearDeletedMedia);
      }}
      initialValues={info || { grade_section: SECTION.小学 }}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormSection
        name="grade_section"
        label="学段"
        rules={[{ required: true, message: '请选择学段！' }]}
        colProps={{ span: 24 }}
      />
      <ProFormText
        name="subject"
        label="学科名称"
        rules={[{ required: true, message: '请输入学科名称！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormImg
        name="cover"
        label="封面"
        maxSize={{ size: 2 * 1024 * 1024, message: '图片大小不能超过2M' }}
      />
      <Col span={24}>
        <ProForm.Item name="description" label="描述">
          <WangEditor ref={editorRef} />
        </ProForm.Item>
      </Col>
    </ModalForm>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(EditModal);
