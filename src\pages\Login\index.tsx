/*
 * @Description: 本地登录页面
 * @Date: 2025-01-07 17:44:07
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-06-05 10:01:31
 */
import { localLogin } from '@/services/auth';
import { saveJWT } from '@/utils/auth';
import { history } from '@umijs/max';
import { Button, Card, Form, Input, message, Typography } from 'antd';
import { useEffect, useState } from 'react';

const { Title } = Typography;

const Login = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // 如果已经登录，直接跳转到首页
    const token = localStorage.getItem(STORAGE_PREFIX + 'token');
    if (token && token !== 'null') {
      history.push('/');
    }
  }, []);

  const handleSubmit = async (values: { username: string; password: string }) => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await localLogin(values);
      if (errCode) {
        message.error(msg);
      } else {
        message.success('登录成功');
        saveJWT(data.token, `${new Date().getTime() + data.expiresIn * 1000}`);
        // 跳转到首页
        setTimeout(() => {
          window.location.href = BASE_URL + '/';
        }, 500);
      }
    } catch (error) {
      message.error('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
      }}
    >
      <Card
        style={{
          width: '100%',
          maxWidth: '400px',
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <img
            src="/homework-design/logo.png"
            alt="Logo"
            style={{ height: '64px', marginBottom: '16px' }}
          />
          <Title level={3} style={{ margin: 0, color: '#1f2937' }}>
            智慧作业设计系统
          </Title>
          <p style={{ color: '#6b7280', marginTop: '8px' }}>
            请输入您的账号和密码登录
          </p>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 2, message: '用户名至少2个字符' },
            ]}
          >
            <Input
              size="large"
              placeholder="请输入用户名"
              style={{ borderRadius: '8px' }}
            />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password
              size="large"
              placeholder="请输入密码"
              style={{ borderRadius: '8px' }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              loading={loading}
              style={{
                width: '100%',
                borderRadius: '8px',
                height: '48px',
                fontSize: '16px',
                fontWeight: 500,
              }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <p style={{ color: '#9ca3af', fontSize: '14px' }}>
            © 2025 智慧作业设计系统. All rights reserved.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default Login;
