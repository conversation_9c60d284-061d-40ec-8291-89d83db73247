import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();
  useEffect(() => {
    if (open && info) {
      form.setFieldsValue(info);
    } else {
      form.resetFields();
    }
  }, [open, info]);

  return (
    <ModalForm<any>
      width={560}
      form={form}
      title={info ? '编辑方案原则' : '新增方案原则'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '4em' }}
      onFinish={onSave}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText name="target_name" hidden />
      {/* <ProFormSelect
        name="target_id"
        label="培养目标"
        request={async () => {
          const { errCode, data, msg } = await index({});
          if (errCode) {
            message.warning(`获取培养目标列表失败 ${msg}`);
            return [];
          }
          return (data.list ?? []).map((item: any) => ({
            value: item.id,
            label: item.name,
          }));
        }}
        onChange={(_value, options) => {
          form.setFieldValue('target_name', options?.title);
        }}
        rules={[{ required: true, message: '请选择培养目标！' }]}
      /> */}
      <ProFormText
        name="name"
        label="名称"
        fieldProps={{ maxLength: 100 }}
        rules={[{ required: true, message: '请输入名称！' }]}
      />
      <ProFormTextArea
        name="describe"
        label="描述"
        fieldProps={{
          autoSize: { minRows: 2, maxRows: 8 },
        }}
        rules={[{ required: true, message: '请输入描述！' }]}
      />
      <ProFormTextArea name="analy" label="解析" />
    </ModalForm>
  );
};

export default EditModal;
