import { index as indexTextbook } from '@/services/textbooks';
import { Col, ColProps, message, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { RadioGroupStyled } from './styled';

const Textbook: React.FC<{
  initalValue?: number;
  onChange?: (value?: API.Textbook) => void;
  /** 学科ID */
  subjectId?: number;
  /** 标题列属性 */
  labelCol?: ColProps;
  /** 自定义行样式 */
  rowStyles?: React.CSSProperties;
}> = ({ initalValue, onChange, subjectId, labelCol, rowStyles }) => {
  const [currentTextbookId, setCurrentTextbookId] = useState<
    number | undefined
  >(initalValue);
  const [textbookList, setTextbookList] = useState<API.Textbook[]>([]);

  useEffect(() => {
    if (subjectId) {
      (async () => {
        const { errCode, data, msg } = await indexTextbook({
          subject_id: subjectId,
        });
        if (errCode) {
          message.error(msg || '教材列表获取失败');
          return;
        }
        setTextbookList(data.list || []);
      })();
    }
  }, [subjectId]);

  // 教材列表切换后，检查currentTextbookId是否在列表中
  useEffect(() => {
    if (currentTextbookId) {
      if (!textbookList.find((d) => d.id === currentTextbookId)) {
        setCurrentTextbookId(undefined);
      }
    } else {
      setCurrentTextbookId(textbookList?.[0]?.id);
      onChange?.(textbookList?.[0]);
    }
  }, [textbookList, currentTextbookId]);

  return (
    <Row className={styles.row} style={rowStyles}>
      <Col className={styles.label} {...labelCol}>
        <label>版材</label>
      </Col>
      <Col>
        <RadioGroupStyled
          size="small"
          options={textbookList.map((tb) => ({
            label: tb.alias || `${tb.textbook_version}`,
            value: tb.id,
            title: tb.alias || `${tb.textbook_version}`,
          }))}
          onChange={(e) => {
            setCurrentTextbookId(e.target.value);
            const item = textbookList.find((d) => d.id === e.target.value);
            onChange?.(item);
          }}
          value={currentTextbookId}
          optionType="button"
          buttonStyle="solid"
        />
      </Col>
    </Row>
  );
};

export default Textbook;
