/**
 * @description 全校各科目教师作业次数分析
 */
import { getTeacherWorkStatistic } from '@/services/statistics';
import { useModel } from '@umijs/max';
import { Card, Col, message } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import ColumnChart from '../BaseChart/Column';
import FilterSelect from '../BaseChart/FilterSelect';
import StatisticsContext from '../Context';
import styles from './styles/index.less';

interface TeacherAssignmentsTotalProps {
  span: number;
  title: string;
}

const TeacherAssignmentsTotal: React.FC<TeacherAssignmentsTotalProps> = ({
  span,
  title,
}) => {
  const { initialState } = useModel('@@initialState');
  const [data, setData] = useState<any>([]);
  const [filterInfo, setFilterInfo] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const { info, options } = useContext(StatisticsContext);
  const { yearTerm, county, schoolType } = info || {};

  const getInitData = async () => {
    setLoading(true);
    if (!yearTerm) return;
    let params: any = {
      semester_code: yearTerm,
      enterprise_code: initialState?.enterprise?.code,
      grade_section_code: schoolType,
      grade_code: filterInfo?.grade,
      subject_id: filterInfo?.subject,
    };

    const res = await getTeacherWorkStatistic(params);

    if (res?.errCode) {
      setLoading(false);
      message.warning(`获取数据失败，请稍后重试 ${res?.msg}`);
      return;
    }
    setLoading(false);
    const newDatta = res?.data?.map(
      (item: { teacher_name: any; total_homework_number: string }) => {
        return {
          ...item,
          label: item?.teacher_name,
          value: parseFloat(item.total_homework_number),
        };
      },
    );

    setData(newDatta || []);
  };

  useEffect(() => {
    getInitData();
  }, [yearTerm, county, schoolType, filterInfo]);

  return (
    <Col span={span}>
      <Card
        className={styles.card}
        title={title}
        extra={
          <FilterSelect
            mode="grade"
            gradeSection={schoolType}
            gradeOption={options}
            onChange={(value) => {
              setFilterInfo(value);
            }}
          />
        }
      >
        <ColumnChart loading={loading} data={data} />
      </Card>
    </Col>
  );
};
export default TeacherAssignmentsTotal;
