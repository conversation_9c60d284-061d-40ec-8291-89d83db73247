import {
  AssignmentsTotal,
  DifficultyAnalysis,
  DurationAnalysis,
  QuantitativeAnalysis,
  QuestionsTotalTrend,
  ShareSchoolRankings,
  TrendHomeworkQuantity,
} from './components/Chart';
import StatisticsContext from './components/Context';

const CountyPart = ({ params }: { params: any }) => {
  return (
    <StatisticsContext.Provider value={params}>
      <AssignmentsTotal
        span={16}
        type="county"
        title="各校分科目作业数量分析"
      />
      <ShareSchoolRankings type="county" title={'范本共享学校排名'} span={8} />
      <TrendHomeworkQuantity
        type="county"
        title={'本区县月度作业数量趋势'}
        span={24}
      />
      <DurationAnalysis
        type="county"
        title={'各校分科目作业平均时长分析'}
        span={24}
      />
      <QuantitativeAnalysis
        type="county"
        title={'各校分科目试题数量分析'}
        span={12}
      />
      <DifficultyAnalysis
        type="county"
        title={'本区县试题难度分析'}
        span={12}
      />
      <QuestionsTotalTrend
        type="county"
        title={'本区县月度试题量趋势'}
        span={24}
      />
    </StatisticsContext.Provider>
  );
};
export default CountyPart;
