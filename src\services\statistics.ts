import { request } from '@umijs/max';

interface baseParamsType {
  /** 学年学期编码 */
  semester_code?: string;
  /** 市code */
  city_code?: string;
  /** 区code */
  area_code?: string;
  /** 学段code */
  grade_section_code?: string;
}

/** 获取范本共享学校排名  GET /analysis_template_share_record */
export async function getTemplateShareRecord(params: baseParamsType) {
  return request<API.ResType<{ total?: number; list?: any[] }>>(
    '/analysis_template_share_record',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取试题难易程度分析 GET /analysis_question_difficulty/statistic */
export async function getQuestionDifficultyStatistic(params: baseParamsType) {
  return request<API.ResType<any>>('/analysis_question_difficulty/statistic', {
    method: 'GET',
    params,
  });
}

type getQuestionWorkTypeParams = baseParamsType & {
  /** 年级code */
  grade_code?: string;
  /** 学科id */
  subject_id?: string;
  /** 企业code */
  enterprise_code?: string;
};

/** 获取作业数量、平均时长、试题数量 分析信息 GET  /analysis_homework_record/statistic/homework_analysis */
export async function getQuestionWork(params: getQuestionWorkTypeParams) {
  return request<API.ResType<any>>(
    '/analysis_homework_record/statistic/homework_analysis',
    {
      method: 'GET',
      params,
    },
  );
}
/**获取平均作业时长分析信息 */
export async function getQuestionWorkDuration(params: any) {
  return request<API.ResType<any>>(
    '/analysis_homework_record/statistic/average_time_analysis',
    {
      method: 'GET',
      params,
    },
  );
}
/** 按月汇总作业数量和试题数量 GET /analysis_homework_record/statistic/month */
export async function getQuestionWorkMonth(
  params: baseParamsType & { enterprise_code?: string },
) {
  return request<API.ResType<any>>(
    '/analysis_homework_record/statistic/month',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取对应区县学校、作业次数、作业时长、试题数量汇总 GET /analysis_homework_record/statistic */
export async function getQuestionWorkStatistic(params: baseParamsType) {
  return request<API.ResType<{ total?: number; list?: any[] }>>(
    '/analysis_homework_record/statistic',
    {
      method: 'GET',
      params,
    },
  );
}

/** 学校-获取汇总数据 GET /analysis_homework_record/statistic/by_school */
export async function getSchoolAnalysis(params: { enterprise_code?: string }) {
  return request<API.ResType<any>>(
    '/analysis_homework_record/statistic/by_school',
    {
      method: 'GET',
      params,
    },
  );
}

/** 学校-获取教师作业次数、作业时长分析 GET /analysis_teacher_work_record/statistic */
export async function getTeacherWorkStatistic(params: {
  enterprise_code?: string;
  semester_code?: string;
  grade_code?: string;
  subject_id?: string;
}) {
  return request<API.ResType<any>>('/analysis_teacher_work_record/statistic', {
    method: 'GET',
    params,
  });
}
