import { Empty, Spin } from 'antd';
import React from 'react';

interface NoDataChartProps {
  data: any;
  height?: number;
  children?: React.ReactNode;
  loading?: boolean;
}

const NoDataChart: React.FC<NoDataChartProps> = ({
  data,
  height = 400,
  children,
  loading,
}) => {
  return (
    <Spin tip={'数据加载中...'} spinning={loading}>
      <div style={{ width: '100%', height, display: 'flex' }}>
        {data && data?.length ? (
          children
        ) : (
          <Empty style={{ margin: 'auto  auto' }} description="暂无数据" />
        )}
      </div>
    </Spin>
  );
};
export default NoDataChart;
