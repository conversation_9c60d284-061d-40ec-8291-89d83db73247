/*
 * @Description: 教材选用管理
 * @Date: 2025-03-11 09:41:17
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-17 15:45:39
 */
import { SECTION } from '@/constants';
import { AreaState } from '@/models/area';
import { DictionarieState } from '@/models/dictionarie';
import { create, index, remove, update } from '@/services/textbook_choose';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const Choose: React.FC<{ dictionarie: DictionarieState; area: AreaState }> = ({
  dictionarie,
  area,
}) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.TextbookChoose | undefined>(
    undefined,
  );

  const handleSave = async (values: API.TextbookChoose) => {
    let response;
    if (current) {
      const { id, items, ...info } = values;
      const updateItems = (items as unknown as string[]).map((item) => {
        return {
          textbook_choose_id: id,
          area_code: item,
        };
      });
      response = await update(id, { ...info, items: updateItems });
    } else {
      const createItems = (values.items as unknown as string[]).map((item) => {
        return {
          area_code: item,
        };
      });
      response = await create({ ...values, items: createItems });
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.TextbookChoose) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.TextbookChoose, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '学年',
      dataIndex: 'semester',
      key: 'semester',
    },
    {
      title: '学段',
      dataIndex: ['subject', 'grade_section'],
      valueEnum: {
        [SECTION.小学]: '小学',
        [SECTION.初中]: '初中',
        [SECTION.高中]: '高中',
      },
    },
    {
      title: '学科',
      dataIndex: ['subject', 'subject'],
      key: 'subject',
    },
    {
      title: '教材版本',
      dataIndex: ['textbook', 'textbook_version'],
      key: 'textbook',
    },
    {
      title: '适用年级',
      dataIndex: 'grade_list',
      key: 'grade_list',
      ellipsis: true,
      render: (_, record) => {
        const { grade_list } = record;
        return grade_list
          .split(',')
          .map(
            (key) =>
              dictionarie.list.find((d) => d.type === 'grade' && d.code === key)
                ?.name,
          )
          .join('、');
      },
    },
    {
      title: '适用区域',
      dataIndex: 'area_list',
      key: 'area_list',
      ellipsis: true,
      render: (_, record) => {
        const { items } = record;
        const list = (items || [])
          .sort((a, b) => a.area_code!.localeCompare(b.area_code!))
          .map((item) => area.list.find((d) => d.code === item.area_code))
          .filter((v) => !!v);
        return list
          .map(
            (v) =>
              `${
                v.parentCode
                  ? area.list.find((a) => a.code === v.parentCode)?.name + '-'
                  : ''
              }${v.name}`,
          )
          .join('、');
      },
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.TextbookChoose>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data.list,
            total: data.total,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ dictionarie, area }) => ({ dictionarie, area }))(
  Choose,
);
