/**
 * @description 获取范本共享学校排名
 */
import { getTemplateShareRecord } from '@/services/statistics';
import { useModel } from '@umijs/max';
import { Col, message } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import Ranking from '../BaseChart/Ranking';
import StatisticsContext from '../Context';
import styles from './styles/index.less';

interface ShareSchoolRankingsProps {
  span: number;
  type: 'city' | 'county' | 'school';
  title: string;
}

const ShareSchoolRankings: React.FC<ShareSchoolRankingsProps> = ({
  span,
  type,
  title,
}) => {
  const { initialState } = useModel('@@initialState');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>([]);
  const { info } = useContext(StatisticsContext);
  const { yearTerm, county, schoolType } = info || {};

  const buildParams = () => {
    const baseParams = {
      semester_code: yearTerm,
      grade_section_code: schoolType,
    };

    switch (type) {
      case 'city':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county,
        };
      case 'county':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county || initialState?.enterprise?.area,
        };
      case 'school':
        return {
          ...baseParams,
          enterprise_code: initialState?.enterprise?.code,
        };
      default:
        return baseParams;
    }
  };
  const getInitData = async () => {
    const params = buildParams();
    setLoading(true);
    if (!yearTerm) return;
    const { errCode, data, msg } = await getTemplateShareRecord(params);
    if (errCode) {
      setLoading(false);
      message.warning(`获取数据失败，请稍后重试 ${msg}`);
      return;
    }
    setLoading(false);
    data.list?.forEach((item: any, index: number) => {
      item.index = index + 1;
    });
    setData(data?.list || []);
  };

  useEffect(() => {
    if (type) getInitData();
  }, [type, yearTerm, county, schoolType]);
  return (
    <Col span={span} className={styles.card}>
      <h3> {title}</h3>
      <Ranking type="book" loading={loading} data={data} />
    </Col>
  );
};
export default ShareSchoolRankings;
