import { RoleState } from '@/models/role';
import {
  DeleteOutlined,
  EditOutlined,
  PartitionOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { AnyAction, connect } from '@umijs/max';
import {
  Button,
  Divider,
  Popconfirm,
  Space,
  Spin,
  Tag,
  theme,
  Tooltip,
} from 'antd';
import React, { Dispatch, useEffect, useState } from 'react';
import BindUsers from './BindUsers';
import EditModal from './EditModal';
import EditPermissions from './EditPermissions';

const Roles: React.FC<{
  role: RoleState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ role, dispatch, loading }) => {
  const { token } = theme.useToken();

  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [userDrawerVisible, setUserDrawerVisible] = useState(false);
  const [current, setCurrent] = useState<API.Role | undefined>(undefined);

  const handleSave = async (values: API.Role) => {
    if (current) {
      const { id, ...info } = values;
      dispatch({
        type: 'role/update',
        payload: {
          id,
          info,
        },
      });
    } else {
      dispatch({
        type: 'role/add',
        payload: values,
      });
    }
  };

  const handleDel = async (record: API.Role) => {
    const { id } = record;
    dispatch({
      type: 'role/remove',
      payload: {
        id,
      },
    });
  };

  const columns: ProColumns<API.Role, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      align: 'center',
      render: (_, record) => (
        <Tooltip
          title={
            record.permissions && record.permissions.length > 3
              ? record.permissions
                  .sort((a, b) => a.featureCode.localeCompare(b.featureCode))
                  .map((per) => (
                    <div key={per.id}>
                      {per.feature?.name}-{per.name}
                    </div>
                  ))
              : null
          }
        >
          <Space>
            {record.permissions
              ?.sort((a, b) => a.featureCode.localeCompare(b.featureCode))
              .slice(0, 3)
              .map((per) => (
                <Tag key={per.id}>{`${per.feature?.name}-${per.name}`}</Tag>
              ))}
            {record.permissions && record.permissions.length > 3 && (
              <Tag key="more">...</Tag>
            )}
          </Space>
        </Tooltip>
      ),
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space split={<Divider type="vertical" />}>
          <UsergroupAddOutlined
            title="绑定用户"
            onClick={() => {
              setCurrent(record);
              setUserDrawerVisible(true);
            }}
            style={{ color: token.colorPrimary }}
          />
          <PartitionOutlined
            title="编辑权限"
            onClick={() => {
              setCurrent(record);
              setDrawerVisible(true);
            }}
            style={{ color: token.colorPrimary }}
          />
          {record.name !== '管理员' && (
            <>
              <EditOutlined
                title="编辑"
                onClick={() => {
                  setCurrent(record);
                  setModalVisible(true);
                }}
                style={{ color: token.colorPrimary }}
              />
              <Popconfirm
                title="确认删除？"
                onConfirm={() => {
                  handleDel(record);
                }}
              >
                <DeleteOutlined title="删除" style={{ color: '#ff4d4f' }} />
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setModalVisible(false);
  }, [role]);

  return (
    <Spin spinning={loading.models.role}>
      <ProTable<API.Role>
        rowKey="id"
        headerTitle="系统角色"
        columns={columns}
        options={false}
        dataSource={role.currentList}
        onChange={(_pagination, filters, sorter) => {
          dispatch({
            type: 'role/onChange',
            payload: {
              filters,
              sorter,
            },
          });
        }}
        beforeSearchSubmit={(params) => {
          dispatch({
            type: 'role/onSearch',
            payload: params,
          });
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
      <EditPermissions
        open={drawerVisible}
        current={current}
        saveHandl={() => {
          dispatch({ type: 'role/queryList', payload: {} });
        }}
        cancleHandl={() => {
          setDrawerVisible(false);
        }}
      />
      <BindUsers
        open={userDrawerVisible}
        current={current}
        saveHandl={() => {
          dispatch({ type: 'role/queryList', payload: {} });
        }}
        cancleHandl={() => {
          setUserDrawerVisible(false);
        }}
      />
    </Spin>
  );
};

export default connect(({ role, loading }) => ({
  role,
  loading,
}))(Roles);
