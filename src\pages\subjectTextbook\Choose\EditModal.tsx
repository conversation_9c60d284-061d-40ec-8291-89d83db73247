import ProFormArea from '@/components/ProFormItem/ProFormArea';
import { GRADE, SECTION } from '@/constants';
import { index as index_subjects } from '@/services/subjects';
import { index as index_textbooks } from '@/services/textbooks';
import {
  ModalForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormSegmented,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.TextbookChoose;
  onSave: (info: API.TextbookChoose) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<API.TextbookChoose>
      title={info ? '编辑选用信息' : '新增选用信息'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            padding: '10px',
            maxHeight: '60vh',
            overflowY: 'auto',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormSelect
        name="semester"
        label="学年"
        request={async () => {
          const currentYear = new Date().getFullYear();
          const options = [];
          for (let i = -5; i <= 2; i++) {
            const startYear = currentYear + i;
            const endYear = startYear + 1;
            options.push({
              label: `${startYear}-${endYear}`,
              value: `${startYear}-${endYear}`,
            });
          }
          return options;
        }}
        rules={[{ required: true, message: '请选择学年！' }]}
        colProps={{ span: 8 }}
      />
      <ProFormSegmented
        name="section"
        label="学段"
        valueEnum={{
          [SECTION.小学]: '小学',
          [SECTION.初中]: '初中',
          [SECTION.高中]: '高中',
        }}
        colProps={{ span: 8 }}
        initialValue={info?.subject?.grade_section || SECTION.小学}
      />
      <ProFormDependency name={['section']}>
        {({ section }, form) => {
          return (
            <>
              <ProFormSelect
                name="subject_id"
                label="学科"
                params={{ section }}
                request={async (params) => {
                  const { errCode, data, msg } = await index_subjects({
                    grade_section: params.section,
                  });
                  if (errCode) {
                    message.error(msg || '获取学科列表失败');
                    return [];
                  }
                  const items =
                    data.list?.map((item) => ({
                      label: item.subject,
                      value: item.id as any,
                    })) || [];
                  form.setFieldValue('subject_id', items?.[0]?.value);
                  return items;
                }}
                rules={[{ required: true, message: '请选择学年！' }]}
                colProps={{ span: 8 }}
              />
              <ProFormCheckbox.Group
                name="grade_list"
                label="适用年级"
                params={{ section }}
                request={async (params) => {
                  switch (params.section) {
                    case SECTION.小学:
                      return [
                        { label: '一年级', value: GRADE.一年级 },
                        { label: '二年级', value: GRADE.二年级 },
                        { label: '三年级', value: GRADE.三年级 },
                        { label: '四年级', value: GRADE.四年级 },
                        { label: '五年级', value: GRADE.五年级 },
                        { label: '六年级', value: GRADE.六年级 },
                      ];
                    case SECTION.初中:
                      return [
                        { label: '初一', value: GRADE.初一 },
                        { label: '初二', value: GRADE.初二 },
                        { label: '初三', value: GRADE.初三 },
                      ];
                    case SECTION.高中:
                      return [
                        { label: '高一', value: GRADE.高一 },
                        { label: '高二', value: GRADE.高二 },
                        { label: '高三', value: GRADE.高三 },
                      ];
                    default:
                      return [];
                  }
                }}
                transform={(value) => {
                  if (Array.isArray(value)) {
                    return value.join(',');
                  }
                  return value;
                }}
              />
            </>
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={['subject_id']}>
        {({ subject_id }) => (
          <ProFormSelect
            name="textbook_id"
            label="教材版本"
            params={{ subject_id }}
            request={async (params) => {
              const { errCode, data, msg } = await index_textbooks({
                subject_id: params.subject_id,
              });
              if (errCode) {
                message.error(msg || '获取教材版本列表失败');
                return [];
              }
              return (
                data.list?.map((item) => ({
                  label: item.textbook_version,
                  value: item.id,
                })) || []
              );
            }}
            rules={[{ required: true, message: '请选择教材版本！' }]}
            colProps={{ span: 16 }}
          />
        )}
      </ProFormDependency>
      <ProFormArea
        name="items"
        fieldProps={{
          labelCol: { flex: '7em' },
        }}
        convertValue={(value) => {
          if (Array.isArray(value)) {
            return value.map((v) => (typeof v === 'string' ? v : v.area_code));
          }
          return value;
        }}
      />
    </ModalForm>
  );
};

export default EditModal;
