import TopicList from '@/components/QuestionBank/TopicList';
import SearchTopicBox, {
  SearchTopicBoxResult,
} from '@/components/SearchTopicBox';
import { DictionarieState } from '@/models/dictionarie';
import { getCommonAllQuestions } from '@/services/common_question';
import { getQueryParams } from '@/utils/calc';
import { connect, useModel } from '@umijs/max';
import { Spin, message } from 'antd';
import { Dispatch, useCallback, useEffect, useState } from 'react';
import styles from './index.less';

const QuestionBankList = ({
  type,
  choosedQuestion,
  dispatch,
  choosedEvent,
}: {
  type: string;
  dispatch: Dispatch<any>;
  dictionarie: DictionarieState;
  choosedQuestion: API.SystemQuestion[];
  choosedEvent: (value: API.SystemQuestion, status: boolean) => void;
}) => {
  const { initialState } = useModel('@@initialState');
  const { ksId, catalogIds, subjectId, volumeId } = getQueryParams();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>([]);
  const [allSelect, setAllSelect] = useState<string[]>([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 5 });
  const [searchTopicInfo, setSearchTopicInfo] = useState<SearchTopicBoxResult>(
    {},
  );
  useEffect(() => {
    dispatch({
      type: 'dictionarie/queryList',
      payload: {},
    });
    dispatch({
      type: 'questionTypes/queryList',
      payload: {},
    });
  }, []);

  /** 搜索 */
  const handleSearch = async (data: any) => {
    const isDataChanged =
      JSON.stringify(data) !== JSON.stringify(searchTopicInfo);
    if (isDataChanged) {
      setAllSelect([]);
      setPagination({ current: 1, pageSize: 5 });
      setSearchTopicInfo(data);
    }
  };

  /** 获取试题列表 */
  const getAllQuestion = useCallback(async () => {
    setLoading(true);
    const {
      difficulty,
      questionTypes,
      knowledgePoint,
      searchTopic,
      searchTag,
    } = searchTopicInfo ?? {};
    const { errCode, data, msg } = await getCommonAllQuestions(type, {
      offset:
        Number((pagination?.current || 1) - 1) *
        Number(pagination?.pageSize || 10),
      limit: Number(pagination?.pageSize || 10),
      enterpriseCode:
        type !== 'system' ? initialState?.enterprise?.code : undefined,
      author: type === 'personal' ? initialState?.id : undefined,
      userId: initialState?.id,
      order: { createdAt: 1 }, // 升序1,降序-1
      catalogIds: catalogIds?.split(','),
      catalog: ksId ? Number(ksId) : undefined,
      difficulty: difficulty?.code,
      type: questionTypes?.code,
      volume: volumeId,
      points: knowledgePoint?.join(',') || undefined,
      stem: searchTopic || undefined,
      userTag: searchTag || undefined,
    });
    if (errCode) {
      setLoading(false);
      message.warning(`获取试题列表失败，请稍后重试！ ${msg}`);
    } else {
      setData(data ?? []);
      setLoading(false);
    }
  }, [type, pagination, searchTopicInfo]);
  useEffect(() => {
    if (pagination.current && pagination.pageSize) {
      getAllQuestion();
    }
  }, [pagination.current, pagination.pageSize, searchTopicInfo]);
  return (
    <div className={styles.questionList}>
      <SearchTopicBox
        className={styles.searchTopicBox}
        onChange={handleSearch}
        showAll={INDEPENDENT_QUESTION_BANK}
        subjectId={subjectId ? Number(subjectId) : undefined}
        showDifficulty
        showTypes
        showKeyword
        showTag
      />
      <Spin tip="数据加载中..." spinning={loading}>
        <TopicList
          isGroupModel
          data={data}
          onreload={getAllQuestion}
          choosedQuestion={choosedQuestion}
          choosedEvent={choosedEvent}
          onSelect={(value: string, status: boolean) => {
            if (status) {
              setAllSelect([...allSelect, value]);
            } else {
              setAllSelect(allSelect.filter((item) => item !== value));
            }
          }}
          allSelect={allSelect}
          pagination={{
            total: data?.total,
            current: pagination?.current,
            pageSize: pagination?.pageSize,
            onChange: (page: any, pageSize: any) => {
              setPagination({
                current: page,
                pageSize: pageSize,
              });
            },
          }}
        />
      </Spin>
    </div>
  );
};
export default connect(({ dictionarie, questionTypes }: any) => ({
  dictionarie,
  questionTypes,
}))(QuestionBankList);
