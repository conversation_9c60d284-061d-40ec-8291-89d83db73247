import { request } from '@umijs/max';

/** 查询列表  GET /city_homework_requirement */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: any[] }>>(
    '/city_homework_requirement',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /city_homework_requirement */
export async function create(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>('/city_homework_requirement', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /city_homework_requirement/:id */
export async function show(id: string) {
  return request<API.ResType<any>>(`/city_homework_requirement/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /city_homework_requirement/:id */
export async function update(id: string, body: Partial<any>) {
  return request<API.ResType<unknown>>(`/city_homework_requirement/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /city_homework_requirement/:id */
export async function remove(id: string) {
  return request<API.ResType<unknown>>(`/city_homework_requirement/${id}`, {
    method: 'DELETE',
  });
}

/** 查询列表  GET /city_homework_requirement_duration */
export async function getDuration(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: any[] }>>(
    '/city_homework_requirement_duration',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /city_homework_requirement_duration */
export async function createDuration(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>('/city_homework_requirement_duration', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /city_homework_requirement_duration/:id */
export async function showDuration(id: string) {
  return request<API.ResType<any>>(
    `/city_homework_requirement_duration/${id}`,
    {
      method: 'GET',
    },
  );
}

/** 修改  PUT /city_homework_requirement_duration/:id */
export async function updateDuration(id: string, body: Partial<any>) {
  return request<API.ResType<unknown>>(
    `/city_homework_requirement_duration/${id}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
    },
  );
}

/** 删除  DELETE /city_homework_requirement_duration/:id */
export async function removeDuration(id: string) {
  return request<API.ResType<unknown>>(
    `/city_homework_requirement_duration/${id}`,
    {
      method: 'DELETE',
    },
  );
}
