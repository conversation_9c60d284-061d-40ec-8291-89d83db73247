import { DictionarieState } from '@/models/dictionarie';
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons';
import { connect } from '@umijs/max';
import { Descriptions, DescriptionsProps, Image, Modal } from 'antd';
import React, { useState } from 'react';

const PreviewModal: React.FC<{
  open: boolean;
  value?: API.Subject;
  onClose?: () => void;
  dictionarie: DictionarieState;
}> = ({ open, value, onClose, dictionarie }) => {
  const [fullscreen, setFullscreen] = useState<boolean>(false);

  const items: DescriptionsProps['items'] = [
    {
      key: 'grade_section',
      label: '学段',
      children: value
        ? dictionarie.list.find(
            (d) => d.type === 'grade_section' && d.code === value.grade_section,
          )?.name
        : '',
    },
    {
      key: 'subject',
      label: '学科',
      children: value ? value.subject : '',
    },
    {
      key: 'cover',
      label: '封面',
      children: value?.cover ? (
        <Image src={value ? value.cover : ''} width={100} height={100} />
      ) : (
        ''
      ),
      span: 2,
    },
    {
      key: 'description',
      label: '描述',
      children: value ? (
        <div
          style={{
            position: fullscreen ? 'fixed' : 'relative',
            zIndex: fullscreen ? 2000 : 'auto',
            width: fullscreen ? '100vw' : '100%',
            height: fullscreen ? '100vh' : '100%',
            left: 0,
            top: 0,
            background: '#fff',
          }}
        >
          {!!value.description ? (
            fullscreen ? (
              <FullscreenExitOutlined
                style={{
                  position: 'absolute',
                  right: 10,
                  top: 10,
                  cursor: 'pointer',
                }}
                title="退出全屏"
                onClick={() => setFullscreen(false)}
              />
            ) : (
              <FullscreenOutlined
                style={{
                  position: 'absolute',
                  right: 10,
                  top: 10,
                  cursor: 'pointer',
                }}
                title="全屏"
                onClick={() => setFullscreen(true)}
              />
            )
          ) : (
            ''
          )}

          <div
            dangerouslySetInnerHTML={{ __html: value.description || '' }}
            style={{
              width: '100%',
              maxHeight: '55vh',
              overflow: 'auto',
              background: '#fff',
              padding: 10,
            }}
          />
        </div>
      ) : (
        ''
      ),
      span: 2,
    },
  ];
  return (
    <Modal
      open={open}
      width={800}
      onCancel={onClose}
      maskClosable
      okButtonProps={{ hidden: true }}
      cancelButtonProps={{ hidden: true }}
    >
      <Descriptions title="学科详情" items={items} bordered column={2} />
    </Modal>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(PreviewModal);
