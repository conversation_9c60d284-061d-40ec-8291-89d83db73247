.questionList {
  height: calc(100vh - 210px);

  .noPaper {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
  }

  .questionItemTop {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .checkListBox {
    display: flex;

    :global(.ant-checkbox-wrapper) {
      width: 30px;
    }

    .questionItem {
      flex: 1;
    }
  }

  :global {
    .ant-list {
      padding-bottom: 24px;
    }
  }
}

:global(.commonWapper).questionItem {
  margin-bottom: 16px;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;

  .questionItemTop,
  .questionItemBottom {
    padding: 0;
    color: #888;
    line-height: 30px;

    span {
      & + span {
        &::before {
          position: relative;
          top: -0.06em;
          display: block;
          display: inline-block;
          height: 0.9em;
          margin: 0 8px;
          border-top: 0;
          content: '';
          border-inline-start: 1px solid rgba(5, 5, 5, 6%);
        }
      }
    }
  }

  .questionItemContent {
    display: flex;

    > div[class*='questionNum'] {
      min-width: 20px;
      line-height: 28px;
    }

    > div[class*='questionTitle'] {
      flex: 1;
      line-height: 28px;

      * {
        max-width: 100%;
        line-height: 28px;
        word-wrap: break-word;
        word-break: break-word;
        white-space: break-spaces;
      }
    }
  }

  div[class*='questionTitle'] {
    flex: 1;

    * {
      max-width: 100%;
      line-height: 28px;
      word-wrap: break-word;
      word-break: break-word;
      white-space: break-spaces;
    }

    p {
      margin-bottom: 0;
    }
  }

  .questionAnswerArea {
    margin: 16px 0;
    padding: 8px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 10%), -1px -1px 3px rgba(0, 0, 0, 10%);

    .questionAnswerItem {
      display: flex;

      > div[class*='questionPoint'] {
        width: 70px;
        color: var(--primary-color);
        line-height: 28px;
      }
    }
  }

  .questionItemBottom {
    display: flex;
    justify-content: space-between;
  }
}

.templateModal {
  .desc {
    margin-bottom: 20px;
  }
}
