/**
 * 功能编号到路由路径的映射
 * 此文件由 UMI 插件自动生成，请勿手动修改
 * 生成时间: 2025/5/29 17:50:34
 */

export const FEATURE_ROUTE_MAPPING: Record<string, string> = {
  '010000': '/home',
  '020000': '/coursePlan',
  '020100': '/coursePlan/adminTarget',
  '020200': '/coursePlan/designPrinciple',
  '020300': '/coursePlan/designCourseSystem',
  '020400': '/coursePlan/designCourseSubject',
  '020500': '/coursePlan/designCourseSetting',
  '020600': '/coursePlan/designCourseTime',
  '020700': '/coursePlan/designCourseCarry',
  '030000': '/courseStandard',
  '030100': '/courseStandard/character',
  '030200': '/courseStandard/idea',
  '030300': '/courseStandard/objective',
  '030400': '/courseStandard/content',
  '030500': '/courseStandard/quality',
  '030600': '/courseStandard/carry',
  '030700': '/courseStandard/appendix',
  '040100': '/subjectTextbook/subject',
  '040200': '/subjectTextbook/publisher',
  '040300': '/subjectTextbook/textbook',
  '040400': '/subjectTextbook/textbookChecklist',
  '040500': '/subjectTextbook/catalog',
  '040600': '/subjectTextbook/choose',
  '050000': '/provincialDesign',
  '050100': '/provincialDesign/objective',
  '050200': '/provincialDesign/idea',
  '050300': '/provincialDesign/basic',
  '050400': '/provincialDesign/develop',
  '050500': '/provincialDesign/extend',
  '050600': '/provincialDesign/implement',
  '060000': '/municipalDesign',
  '060100': '/municipalDesign/idea',
  '060200': '/municipalDesign/requirement',
  '060300': '/municipalDesign/principle',
  '060400': '/municipalDesign/type',
  '060500': '/municipalDesign/form',
  '060600': '/municipalDesign/suggest',
  '060700': '/municipalDesign/case',
  '070000': '/homeworkTemplate',
  '070100': '/homeworkTemplate/primarySchool',
  '070200': '/homeworkTemplate/juniorSchool',
  '070300': '/homeworkTemplate/highSchool',
  '080000': '/complianceTestTemplate',
  '080100': '/complianceTestTemplate/primarySchool/unit',
  '080200': '/complianceTestTemplate/primarySchool/midsemester',
  '080300': '/complianceTestTemplate/juniorSchool/unit',
  '080400': '/complianceTestTemplate/juniorSchool/midsemester',
  '080500': '/complianceTestTemplate/highSchool/unit',
  '080600': '/complianceTestTemplate/highSchool/midsemester',
  '090000': '/homeworkDesign',
  '090100': '/homeworkDesign/primarySchool',
  '090200': '/homeworkDesign/juniorSchool',
  '090300': '/homeworkDesign/highSchool',
  '100000': '/complianceTestDesign',
  '100100': '/complianceTestDesign/primarySchoolUnit',
  '100200': '/complianceTestDesign/primarySchoolMidsemester',
  '100300': '/complianceTestDesign/juniorSchoolUnit',
  '100400': '/complianceTestDesign/juniorSchoolMidsemester',
  '100500': '/complianceTestDesign/highSchoolUnit',
  '100600': '/complianceTestDesign/highSchoolMidsemester',
  '110000': '/testPaperManagement',
  '110100': '/testPaperManagement/manual',
  '110300': '/testPaperManagement/auto',
  '120000': '/questionManagement',
  '120100': '/questionManagement/entry',
  '120200': '/questionManagement/knowledge',
  '120300': '/questionManagement/analysis',
  '120400': '/questionManagement/classification',
  '120500': '/questionManagement/audit',
  '120600': '/questionManagement/hierarchy',
  '130000': '/question/statistics',
  '980000': '/basicData',
  '980100': '/basicData/semester',
  '980200': '/basicData/area',
  '980300': '/basicData/dictionarie',
  '980400': '/basicData/questionType',
  '980500': '/basicData/knowledge',
  '980600': '/basicData/ability',
  '980700': '/basicData/level',
  '980800': '/basicData/classification',
  '980900': '/basicData/hierarchy',
  '990100': '/settings/enterprises',
  '990200': '/settings/users',
  '990300': '/settings/roles',
  '990400': '/settings/permissions',
  '990500': '/settings/features',
};

/**
 * 根据功能编号获取对应的路由路径
 * @param featureCode 功能编号
 * @returns 路由路径，如果未找到则返回 null
 */
export const getRouteByFeatureCode = (featureCode: string): string | null => {
  return FEATURE_ROUTE_MAPPING[featureCode] || null;
};

/**
 * 获取所有有效的功能编号列表（按编号排序）
 * @returns 排序后的功能编号数组
 */
export const getAllFeatureCodes = (): string[] => {
  return Object.keys(FEATURE_ROUTE_MAPPING).sort((a, b) => a.localeCompare(b));
};

/**
 * 检查功能编号是否存在对应的路由
 * @param featureCode 功能编号
 * @returns 是否存在对应路由
 */
export const hasRouteForFeature = (featureCode: string): boolean => {
  return featureCode in FEATURE_ROUTE_MAPPING;
};
