import classNames from 'classnames';
import React from 'react';
import ConditionalRender from '../ConditionalRender';
import {
  QuestionBankDifficulty,
  QuestionBankSearch,
  QuestionBankSmart,
  QuestionBankSource,
  QuestionBankTag,
  QuestionBankType,
} from './components';

import { Col, Radio, Row } from 'antd';
import styles from './index.less';

// 基础选项类型
type OptionItem = {
  label?: string;
  value?: string;
  name?: string;
  code?: string;
};

// 搜索框返回参数
export interface SearchTopicBoxResult {
  source?: OptionItem | null;
  difficulty?: OptionItem | null;
  questionTypes?: OptionItem | null;
  knowledgePoint?: any[] | null;
  searchTopic?: string;
  platType?: string;
  searchCategoryLabels?: string;
  searchTag?: string;
}

// 组件属性
interface SearchTopicBoxProps {
  onChange?: (info: SearchTopicBoxResult) => void;
  showFields?: string[];
  className?: string;
  style?: React.CSSProperties;
  /**显示题库范围 */
  showPlat?: boolean;
  /** 显示来源 */
  showSource?: boolean;
  /** 显示难度 */
  showDifficulty?: boolean;
  /** 显示类型 */
  showTypes?: boolean;
  /** 显示知识点 */
  showSmart?: boolean;
  /** 显示搜索主题 */
  showKeyword?: boolean;
  /** 显示搜索标记 */
  showTag?: boolean;
  /** 显示类题标签 */
  showLabels?: boolean;
  /** 显示全部，注意：显示全部时，其余的选项将不生效，默认fasle */
  showAll?: boolean;
  /** 科目id */
  subjectId?: any;
}

const SearchTopicBox: React.FC<SearchTopicBoxProps> = ({
  onChange,
  showSource,
  showPlat,
  showDifficulty,
  showTypes,
  showSmart,
  showKeyword,
  showLabels,
  showTag,
  showAll = false,
  className,
  style,
  subjectId,
}) => {
  const [data, setData] = React.useState<SearchTopicBoxResult>({
    source: { label: '平台', value: '平台' },
    difficulty: { label: '全部', value: 'all' },
    questionTypes: { label: '全部', value: 'all' },
    knowledgePoint: null,
    searchTopic: '',
    searchCategoryLabels: '',
  });

  const isNoShow =
    !showAll &&
    !showPlat &&
    !showSource &&
    !showDifficulty &&
    !showTypes &&
    !showSmart &&
    !showKeyword &&
    !showTag &&
    !showLabels;

  // 统一处理变化
  const handleChange = (field: keyof SearchTopicBoxResult) => (value: any) => {
    setData((prev) => ({
      ...prev,
      [field]: field === 'searchTopic' && value === '' ? undefined : value,
    }));
  };

  React.useEffect(() => {
    if (onChange) {
      onChange(data);
    }
  }, [data]);
  return (
    <div className={classNames(styles.wapper, className)} style={style}>
      <Row gutter={[16, 16]}>
        <ConditionalRender
          hasAccess={isNoShow}
          accessComponent={<Col span={24}>注意：请至少显示一个选项！</Col>}
        />
        <ConditionalRender
          hasAccess={showAll || showSource}
          accessComponent={
            <Col span={24}>
              <QuestionBankSource
                onChange={handleChange('source')}
                defaultValue="all"
              />
            </Col>
          }
        />
        <ConditionalRender
          hasAccess={showAll || showPlat}
          accessComponent={
            <Col span={24}>
              <label>范围：</label>
              <Radio.Group
                options={[
                  { value: 'system', label: '系统题库' },
                  { value: 'school', label: '学校题库' },
                  { value: 'personal', label: '个人题库' },
                ]}
                defaultValue={data.platType || 'system'}
                onChange={(e) => {
                  handleChange('platType')(e.target.value);
                }}
                optionType="button"
                buttonStyle="solid"
              />
            </Col>
          }
        />
        <ConditionalRender
          hasAccess={showAll || showDifficulty}
          accessComponent={
            <Col span={24}>
              <QuestionBankDifficulty
                onChenge={handleChange('difficulty')}
                defaultValue="all"
              />
            </Col>
          }
        />
        <ConditionalRender
          hasAccess={showAll || showTypes}
          accessComponent={
            <Col span={24}>
              <QuestionBankType
                onChenge={handleChange('questionTypes')}
                defaultValue="all"
                subjectId={subjectId}
              />
            </Col>
          }
        />
        <ConditionalRender
          hasAccess={showAll || showSmart}
          accessComponent={
            <Col span={8}>
              <QuestionBankSmart
                subjectId={subjectId}
                onChange={handleChange('knowledgePoint')}
              />
            </Col>
          }
        />
        <ConditionalRender
          hasAccess={showAll || showKeyword}
          accessComponent={
            <Col span={8}>
              <QuestionBankSearch
                label="题目搜索"
                onChange={handleChange('searchTopic')}
              />
            </Col>
          }
        />
        <ConditionalRender
          hasAccess={showAll || showTag}
          accessComponent={
            <Col span={8}>
              <QuestionBankSearch
                label="标记搜索"
                placeholder="请输入标记名称"
                onChange={handleChange('searchTag')}
              />
            </Col>
          }
        />
        <ConditionalRender
          hasAccess={showAll || showLabels}
          accessComponent={
            <Col span={8}>
              <QuestionBankTag
                onChange={handleChange('searchCategoryLabels')}
              />
            </Col>
          }
        />
      </Row>
    </div>
  );
};
export default SearchTopicBox;
