/*
 * @Description: 权限点维护
 * @Date: 2025-01-24 08:45:23
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-06 12:05:06
 */
import { FeatureState } from '@/models/feature';
import { ProCard } from '@ant-design/pro-components';
import { AnyAction, connect } from '@umijs/max';
import { Result, Tree, TreeDataNode } from 'antd';
import { Dispatch, FC, useEffect, useState } from 'react';
import PermissionsTable from './PermissionsTable';

const Permissions: FC<{
  feature: FeatureState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ feature, dispatch, loading }) => {
  const [currentFeature, setCurrentFeature] = useState<API.Feature>();
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);

  useEffect(() => {
    // 不能选中父节点，只考虑三层结构
    const currentData: FeatureState['treeData'] = JSON.parse(
      JSON.stringify(feature.treeData),
    );
    currentData.forEach((item) => {
      if (item.children) {
        item.disabled = !!item.children;
        item.children.forEach((child: any) => {
          child.disabled = !!child.children;
        });
      }
    });
    setTreeData(currentData);
  }, [feature]);

  useEffect(() => {
    if (!feature.list.length) {
      dispatch({
        type: 'feature/queryList',
        payload: {},
      });
    }
  }, [feature.list.length]);

  return (
    <ProCard split="vertical">
      <ProCard title="系统功能" colSpan="30%" loading={loading.models.feature}>
        <Tree
          onSelect={(keys) => {
            if (keys.length) {
              setCurrentFeature(
                feature.list.find((item) => item.code === keys[0]),
              );
            } else {
              setCurrentFeature(undefined);
            }
          }}
          treeData={treeData}
          style={{
            height: 'calc(100vh - 11.1rem)',
            overflowY: 'auto',
          }}
        />
      </ProCard>
      <ProCard
        title={currentFeature ? `权限点 - ${currentFeature.name}` : '权限点'}
        headerBordered
      >
        {currentFeature ? (
          <PermissionsTable currentFeature={currentFeature} />
        ) : (
          <Result subTitle="请选择功能" icon={null} />
        )}
      </ProCard>
    </ProCard>
  );
};

export default connect(({ feature, loading }) => ({ feature, loading }))(
  Permissions,
);
