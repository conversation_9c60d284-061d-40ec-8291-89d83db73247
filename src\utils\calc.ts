/*
 * @Description: 计算类工具
 * @Date: 2025-01-23 12:16:12
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-24 11:19:00
 */
import { SECTION_GRADE } from '@/constants';
import { parse } from 'querystring';

/**
 * 将列表转换为树形结构
 *
 * @param {Object} params 参数
 * @param {Array} param.list 列表
 * @param {String} [param.parentKey] 父级key，不传递为根节点
 * @param {String} [param.childKey] 返回数据的子级key，默认为children
 * @param {{parent: string; child: string}} [param.fieldName] 定义父子关系的字段名称，默认为{parent: 'code', child: 'parentCode'}
 * @param {number} [param.maxLevel] 最大返回层级数，默认不限制
 * @returns {Object} res
 */
export const convertListToTree = ({
  list,
  parentKey,
  childKey = 'children',
  fieldName = {
    parent: 'code',
    child: 'parentCode',
  },
  maxLevel,
}: {
  list: any[];
  parentKey?: string;
  childKey?: string;
  fieldName?: {
    parent: string;
    child: string;
  };
  maxLevel?: number;
}) => {
  const tree: any[] = [];
  const map = new Map();
  list.forEach((item) => {
    map.set(item[fieldName.parent], { ...item, [childKey]: [], level: 1 }); // 初始化时添加level属性
  });
  list.forEach((item) => {
    const parent = map.get(item[fieldName.child]);
    if (parent) {
      const node = map.get(item[fieldName.parent]);
      node.level = parent.level + 1; // 设置子节点的level为父节点level + 1
      parent[childKey].push(node);
    } else if (!parentKey || !item[fieldName.child]) {
      tree.push(map.get(item[fieldName.parent]));
    }
  });

  // 删除children长度为0的节点，同时控制层级
  const removeEmptyChildren = (node: any, level = 1) => {
    if (!node) {
      console.warn('node is null');
      return;
    }

    // 如果达到最大层级，删除所有子节点
    if (maxLevel && level >= maxLevel) {
      delete node[childKey];
      return;
    }

    if (node[childKey] && node[childKey].length === 0) {
      delete node[childKey];
    } else if (node[childKey]) {
      node[childKey].forEach((child: any) =>
        removeEmptyChildren(child, level + 1),
      );
    }
  };

  tree.forEach((node) => removeEmptyChildren(node));
  return tree;
};

export type OnChangePayload<T> = {
  filters: Record<string, (React.Key | boolean)[] | null>;
  sorter: {
    field: keyof T;
    order: 'descend' | 'ascend' | null;
  };
};

/**
 * proTable组件的过滤和排序操作，分页不用处理，组件自己会完成
 *
 * @template T 列表数据类型
 * @param {T[]} list 列表数据
 * @param {OnChangePayload<T>} payload 过滤和排序操作的参数
 * @return {T[]}
 */
export const onProTableChange = <T = Record<string, any>>(
  list: T[],
  payload: OnChangePayload<T>,
): T[] => {
  const { filters, sorter } = payload;
  const { field, order } = sorter;

  // 实现过滤，字符串类型支持模糊查询
  let filteredList = [...list];
  if (Object.keys(filters).length > 0) {
    filteredList = list.filter((item) => {
      return Object.keys(filters).every((key) => {
        const filterValues = filters[key];
        const value = item[key as keyof T];
        if (
          filterValues &&
          Array.isArray(filterValues) &&
          ['string', 'number', 'boolean'].includes(typeof value)
        ) {
          if (typeof value === 'string') {
            // 字符串类型支持模糊查询
            return filterValues.some((filterValue) =>
              value.includes(filterValue as string),
            );
          } else {
            return filterValues.includes(value as unknown as string);
          }
        }
        return true;
      });
    });
  }

  // 实现排序
  if (field && order) {
    filteredList = filteredList.sort((a, b) => {
      const aValue = a[field];
      const bValue = b[field];
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        if (order === 'ascend') {
          return aValue - bValue;
        } else if (order === 'descend') {
          return bValue - aValue;
        }
      } else if (typeof aValue === 'string' && typeof bValue === 'string') {
        if (order === 'ascend') {
          return aValue.localeCompare(bValue);
        } else if (order === 'descend') {
          return bValue.localeCompare(aValue);
        }
      } else if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
        const av = aValue ? 1 : 0;
        const bv = bValue ? 1 : 0;
        if (order === 'ascend') {
          return av - bv;
        } else if (order === 'descend') {
          return bv - av;
        }
      }
      return 0;
    });
  }

  return filteredList;
};

/** proTable组件的查询操作 */
/**
 * proTable组件的查询操作
 *
 * @template T - 列表数据类型
 * @param {T[]} list - 列表数据
 * @param {Partial<T>} payload - 查询参数
 * @return {T[]}
 */
export const onProTableSearch = <T = Record<string, any>>(
  list: T[],
  payload: Partial<T>,
): T[] => {
  const newList = list.filter((item: T) => {
    return Object.keys(payload).every((key) => {
      // 判断key在数据结构中是否存在
      if (Object.prototype.hasOwnProperty.call(item, key)) {
        return String(item[key as keyof T])
          .toLowerCase()
          .includes(String(payload[key as keyof T]).toLowerCase());
      }
      return true;
    });
  });
  return newList;
};

/**
 * 中文数字与阿拉伯数字相互转换
 *
 * @param {string | number} input 输入的数字，可以是中文或阿拉伯数字
 * @returns {string | number} 转换后的数字
 */
export const convertNumber = (input: string | number): string | number => {
  const chineseToArabic = (chinese: string): number => {
    const chineseNumbers: { [key: string]: number } = {
      零: 0,
      一: 1,
      二: 2,
      三: 3,
      四: 4,
      五: 5,
      六: 6,
      七: 7,
      八: 8,
      九: 9,
    };
    const units: { [key: string]: number } = {
      十: 10,
      百: 100,
      千: 1000,
      万: 10000,
      亿: 100000000,
    };

    let result = 0;
    let unit = 1;
    let temp = 0;

    for (let i = chinese.length - 1; i >= 0; i--) {
      const char = chinese[i];
      if (units[char]) {
        unit = units[char];
        if (unit === 10 && (i === 0 || chinese[i - 1] in units)) {
          temp += unit;
        }
      } else {
        const num = chineseNumbers[char];
        if (num !== undefined) {
          temp += num * unit;
        }
      }
    }
    result += temp;
    return result;
  };

  const arabicToChinese = (arabic: number): string => {
    const chineseNumbers = [
      '零',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九',
    ];
    const units = ['', '十', '百', '千', '万', '亿'];

    if (arabic === 0) return chineseNumbers[0];

    let result = '';
    let unitIndex = 0;
    let remaining = arabic;

    while (remaining > 0) {
      const digit = remaining % 10;
      if (digit > 0) {
        result = chineseNumbers[digit] + units[unitIndex] + result;
      } else if (result && result[0] !== chineseNumbers[0]) {
        result = chineseNumbers[0] + result;
      }
      remaining = Math.floor(remaining / 10);
      unitIndex++;
    }

    return result.replace(/^一十/, '十');
  };

  if (typeof input === 'string') {
    return chineseToArabic(input);
  } else if (typeof input === 'number') {
    return arabicToChinese(input);
  }
  return input;
};

/**
 * 数字转换，例如：1 -> 1）
 * @param num
 * @param type
 * @returns
 */
export const convertSpecialNumber = (num: number, type?: string) => {
  if (type === 'numberToCircled') {
    return num + '）';
  } else {
    return num;
  }
};
/**
 * 交换数组中前后2个元素的位置
 *
 * @returns 返回重新排序后的数组
 */
export const swapArray = (array: string[], index: number, type?: string) => {
  const newArray = [...array]; // 创建数组的副本
  if (type === 'remove') {
    return newArray.filter((_, i) => i !== index); // 返回新数组，不修改原数组
  } else {
    if (array.length < 2) return; // 确保数组至少有两个元素
    // 调换位置
    if (type === 'up') {
      [newArray[index - 1], newArray[index]] = [
        newArray[index],
        newArray[index - 1],
      ];
    } else {
      [newArray[index], newArray[index + 1]] = [
        newArray[index + 1],
        newArray[index],
      ];
    }
    return newArray;
  }
};
/**
 * 从URL中获取查询参数
 * @param url 可选，默认为当前窗口的location.search
 * @returns 包含所有查询参数的对象
 */
export function getQueryParams(url?: string) {
  const searchParams = new URLSearchParams(url || window.location.search);
  const params: Record<string, string | null> = {};

  searchParams.forEach((value, key) => {
    params[key] = value;
  });

  return params;
}

/**
 * url中的hash字符串转化为json格式
 *
 * @param {string} [queryString] 待转换的查询串
 */
export const getHashObj = (queryString?: string) =>
  parse(
    queryString || window.location.href.split('#')[1],
  ) as NodeJS.Dict<string>;

export function convertToTree(data: any[]) {
  // 创建映射表
  const map = new Map();
  data.forEach((item) => {
    map.set(item.id, {
      title: item.name,
      value: item.id.toString(),
      children: [],
    });
  });

  // 构建树结构
  const tree: any[] = [];
  data.forEach((item) => {
    const node = map.get(item.id);
    if (item.pid) {
      const parent = map.get(item.pid);
      if (parent) {
        parent.children.push(node);
      } else {
        // 如果父节点不存在，作为根节点
        tree.push(node);
      }
    } else {
      // 没有父节点的作为根节点
      tree.push(node);
    }
  });

  return tree;
}

/** 转换成tree 需要的树形结构 */

export const convertToTreeData = (data: any[]) => {
  const map = new Map();
  const tree: any[] = [];

  data.forEach((item) => {
    map.set(item.code, {
      title: item.name,
      value: item.code,
      key: item.code,
      children: [],
    });
  });

  data.forEach((item) => {
    const node = map.get(item.code);
    if (item.parentCode && map.has(item.parentCode)) {
      const parentNode = map.get(item.parentCode);
      if (parentNode) {
        parentNode.children.push(node);
      }
    } else {
      tree.push(node);
    }
  });
  return tree;
};

export const convertSystemToGrade = (
  type?: string,
  data1?: any,
  data2?: any,
) => {
  let curData;
  if (type === 'section') {
    switch (data1) {
      case 'HIGH':
      case 'GRADE_HIGH':
        curData = data2.filter((item: any) => item.value === 'GRADE_HIGH');
        break;
      case 'YEAR6':
        curData = data2.filter(
          (item: any) =>
            item.value === 'GRADE_HIGH' || item.value === 'GRADE_MIDDLE',
        );
        break;
      case 'YEAR9':
        curData = data2.filter(
          (item: any) =>
            item.value === 'GRADE_PRIMARY' || item.value === 'GRADE_MIDDLE',
        );
        break;
      case 'MIDDLE':
      case 'GRADE_MIDDLE':
        curData = data2.filter((item: any) => item.value === 'GRADE_MIDDLE');
        break;
      case 'YEAR12':
        curData = data2;
        break;
      case 'PRIMARY':
      case 'GRADE_PRIMARY':
        curData = data2.filter((item: any) => item.value === 'GRADE_PRIMARY');
        break;
      default:
        break;
    }
  }
  if (type === 'grade') {
    const gradeItems =
      SECTION_GRADE[(data1 as keyof typeof SECTION_GRADE) || ''] || [];

    if (gradeItems) {
      // 过滤出对应学段的年级
      const newOptions = data2
        ?.filter((v: { code: string }) =>
          gradeItems.find((value: string) => value === v.code),
        )
        .map((v: { name: string }) => ({
          label: v.name,
          value: v.name,
        }));
      curData = [...(newOptions || [])];
    } else {
      curData = [];
    }
  }
  if (type === 'gradeCode') {
    let gradeItems: any = [];
    switch (data1) {
      case 'HIGH':
      case 'GRADE_HIGH':
        gradeItems = SECTION_GRADE['GRADE_HIGH'] || [];
        break;
      case 'YEAR6':
        gradeItems = (SECTION_GRADE['GRADE_MIDDLE'] || []).concat(
          SECTION_GRADE['GRADE_HIGH'],
        );
        break;
      case 'YEAR9':
        gradeItems = (SECTION_GRADE['GRADE_PRIMARY'] || []).concat(
          SECTION_GRADE['GRADE_MIDDLE'],
        );
        break;
      case 'MIDDLE':
      case 'GRADE_MIDDLE':
        gradeItems = SECTION_GRADE['GRADE_MIDDLE'];
        break;
      case 'YEAR12':
        gradeItems = (SECTION_GRADE['GRADE_PRIMARY'] || []).concat(
          SECTION_GRADE['GRADE_MIDDLE'].concat(SECTION_GRADE['GRADE_HIGH']),
        );
        break;
      case 'PRIMARY':
      case 'GRADE_PRIMARY':
        gradeItems = SECTION_GRADE['GRADE_PRIMARY'];
        break;
      default:
        break;
    }
    if (gradeItems) {
      // 过滤出对应学段的年级
      const newOptions = data2
        ?.filter((v: { code: string }) =>
          gradeItems.find((value: string) => value === v.code),
        )
        .map((v: { name: string; code: any }) => ({
          label: v.name,
          value: v.code,
        }));
      curData = [...(newOptions || [])];
    } else {
      curData = [];
    }
  }
  if (type === 'subject') {
    let subjectItems: any = [];
    switch (data1) {
      case 'HIGH':
      case 'GRADE_HIGH':
        subjectItems = data2.filter(
          (v: any) => v.grade_section === 'GRADE_HIGH',
        );
        break;
      case 'YEAR6':
        subjectItems = data2.filter(
          (v: any) =>
            v.grade_section === 'GRADE_MIDDLE' ||
            v.grade_section === 'GRADE_HIGH',
        );
        break;
      case 'YEAR9':
        subjectItems = data2.filter(
          (v: any) =>
            v.grade_section === 'GRADE_MIDDLE' ||
            v.grade_section === 'GRADE_PRIMARY',
        );
        break;
      case 'MIDDLE':
      case 'GRADE_MIDDLE':
        subjectItems = data2.filter(
          (v: any) => v.grade_section === 'GRADE_MIDDLE',
        );
        break;
      case 'YEAR12':
        subjectItems = data2;
        break;
      case 'PRIMARY':
      case 'GRADE_PRIMARY':
        subjectItems = data2.filter(
          (v: any) => v.grade_section === 'GRADE_PRIMARY',
        );
        break;
      default:
        subjectItems = data2;
        break;
    }
    if (subjectItems) {
      const newOptions = subjectItems.map((v: any) => ({
        label: v.grade_section_name
          ? v.grade_section_name + v.subject
          : v.subject,
        value: v.id,
      }));
      curData = [...(newOptions || [])];
    } else {
      curData = [];
    }
  }
  return curData;
};
