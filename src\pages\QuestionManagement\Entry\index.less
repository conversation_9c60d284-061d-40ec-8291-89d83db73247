.searchTopicBoxWrapper {
  height: calc(100vh - 235px);
  min-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 16px;
  margin-right: -16px;

  .searchTopicBox {
    margin-bottom: 16px;
  }

  .noUnitHoursWrapper {
    background-color: #fff;
    height: 100%;
    width: 100%;
    border-radius: 8px;

    .noUnitHours {
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .questionListWrapper {
    margin-top: 16px;
    height: calc(100vh - 388px);
    border-radius: 8px;
    position: relative;

    .noUnitHoursWrapper {
      background-color: #fff;
      height: 100%;
      width: 100%;
      border-radius: 8px;

      .noUnitHours {
        position: absolute;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.addQuestionBtn {
  min-height: 52px;
}

.container {
  :global {
    .ant-tabs-nav-wrap {
      border-radius: 8px;
      background-color: #fff;
      padding: 0 20px;
    }
  }
}

.uploadWarning {
  ol {
    margin-bottom: 0;

    li {
      font-size: 12px;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.submitBtn {
  text-align: center;
  margin-top: 15px;
}
