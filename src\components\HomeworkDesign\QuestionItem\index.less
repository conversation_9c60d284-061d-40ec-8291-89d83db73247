.questionItem {
  position: relative;
  // padding: 12px;

  p {
    margin-bottom: 0 !important;
    line-height: 28px;
  }

  .questionItemContent {
    display: flex;
    font-size: var(--font-size);

    > div[class*='questionNum'] {
      min-width: 20px;
      line-height: 28px;
    }

    > div[class*='questionTitle'] {
      flex: 1;

      * {
        max-width: 100%;
        line-height: 28px;
        word-wrap: break-word;
        word-break: break-word;
        white-space: break-spaces;
      }

      table {
        border-collapse: collapse;

        th,
        td {
          text-align: left;
          border: 1px solid #ccc;
          line-height: 1.5;
          min-width: 30px;
          padding: 3px 5px;
        }

        th {
          background-color: #f5f2f0;
          font-weight: 700;
          text-align: center;
        }
      }
    }
  }

  .questionOption {
    padding: 0 8px 0 0;

    ul {
      padding: 0 0 0 16px;
      margin-bottom: 0;
      list-style: none;

      li {
        p {
          display: inline-block;
          margin: 0;
          line-height: 30px;
          font-size: var(--font-size);
        }
      }
    }
  }
}

.questionAnswerArea {
  margin: 16px 0;
  padding: 8px;
  // background-color: #f1f1f1;
  border-radius: 6px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 10%), -1px -1px 3px rgba(0, 0, 0, 10%);

  &.hide {
    display: none;
  }

  p {
    margin-bottom: 0 !important;
    line-height: 28px;
  }

  .questionAnswerItem {
    display: flex;
    align-items: baseline;

    > div[class*='questionPoint'] {
      width: 65px;
      color: var(--primary-color);
      line-height: 28px;
    }

    > div[class*='questionTitle'] {
      flex: 1;
    }
  }
}
