import {
  ModalForm,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<any>
      width={460}
      title={info ? '编辑科目' : '新增科目'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText
        width={260}
        name="subject_name"
        label="科目名称"
        fieldProps={{ maxLength: 50 }}
        rules={[{ required: true, message: '请输入科目名称！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormSwitch name="is_used" label="是否启用" />
    </ModalForm>
  );
};

export default EditModal;
