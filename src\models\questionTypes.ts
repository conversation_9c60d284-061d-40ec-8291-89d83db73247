import { question_extend_type } from '@/services';
import { message } from 'antd';

export type QuestionTypesState = {
  total: number;
  list: API.QuestionTypes[];
  inited: boolean;
};

export default {
  state: {
    total: 0,
    list: [],
    currentList: [],
    inited: false,
  } as QuestionTypesState,

  effects: {
    /** 查询列表 **/
    *queryList(
      { payload }: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(
        question_extend_type.getQuestionExtendType,
        payload,
      );
      if (errCode) {
        message.error(msg || '字典列表查询失败');
      } else {
        yield put({ type: 'querySuccess', payload: data });
      }
    },
    *add(
      { payload }: { payload: API.QuestionTypes },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(
        question_extend_type.createQuestionExtendType,
        payload,
      );
      if (errCode) {
        message.error(msg || '字典创建失败');
      } else {
        message.success('字典创建成功');
        yield put({ type: 'addSuccess', payload: data });
      }
    },
    *update(
      {
        payload,
      }: { payload: { id: number; info: Partial<API.QuestionTypes> } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(
        question_extend_type.updateQuestionExtendType,
        payload.id,
        payload.info,
      );
      if (errCode) {
        message.error(msg || '字典更新失败');
      } else {
        message.success('字典更新成功');
        yield put({ type: 'updateSuccess', payload });
      }
    },
    *remove(
      { payload }: { payload: { id: number } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(
        question_extend_type.removeQuestionExtendType,
        payload.id,
      );
      if (errCode) {
        message.error(msg || '字典删除失败');
      } else {
        message.success('字典删除成功');
        yield put({ type: 'removeSuccess', payload });
      }
    },
  },

  reducers: {
    /** 查询列表成功后更新数据，触发渲染 */
    querySuccess(
      _state: QuestionTypesState,
      { payload }: { payload: any }, // 修改参数类型为 any 或具体API返回类型
    ) {
      return {
        total: payload.total || 0,
        list: payload.list || [],
        currentList: payload.list || [],
        inited: true,
      };
    },
    addSuccess(
      state: QuestionTypesState,
      { payload }: { payload: API.QuestionTypes },
    ) {
      const newList = [...state.list, payload]
        .sort((a, b) => a.code.localeCompare(b.code))
        .sort((a, b) => a.name.localeCompare(b.name));

      return {
        ...state,
        total: newList.length,
        list: newList,
      };
    },
    updateSuccess(
      state: QuestionTypesState,
      {
        payload,
      }: { payload: { id: number; info: Partial<API.QuestionTypes> } },
    ) {
      const newList = state.list
        .map((item) => {
          if (item.id === payload.id) {
            return { ...item, ...payload.info };
          }
          return item;
        })
        .sort((a, b) => a.code.localeCompare(b.code))
        .sort((a, b) => a.name.localeCompare(b.name));

      return {
        ...state,
        list: newList,
      };
    },
    removeSuccess(
      state: QuestionTypesState,
      { payload }: { payload: { id: number } },
    ) {
      const newList = state.list.filter((item) => item.id !== payload.id);

      return {
        ...state,
        list: newList,
      };
    },
  },
};
