/*
 * @Description: 认证类工具
 * @Date: 2024-12-27 13:52:17
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-05-09 17:07:27
 */
import { doLogout, refreshToken as refresh } from '@/services/auth';
import { terminal } from '@umijs/max';

/**
 * 跳转到SSO登录页
 */
export const gotoSSOLoginPage = () => {
  if (location.search.includes('token=')) {
    return;
  }
  terminal.log('跳转到SSO登录页 - ' + window.location.href);
  localStorage.clear();
  const url = new URL(`${SSO.Host}/v1/login`);
  url.searchParams.append('grant_type', 'authorization_code');
  url.searchParams.append('response_type', 'code');
  url.searchParams.append('client_id', SSO.AppID);
  url.searchParams.append('state', SSO.state);
  url.searchParams.append(
    'redirect_uri',
    encodeURI(`${window.location.origin}${BASE_URL}/oauth2/callback`),
  );
  window.location.href = url.href;
};

/**
 * 跳转到本地登录页
 */
export const gotoLocalLoginPage = () => {
  if (location.search.includes('token=')) {
    return;
  }
  terminal.log('跳转到本地登录页 - ' + window.location.href);
  localStorage.clear();
  window.location.href = `${window.location.origin}${BASE_URL}/login`;
};

/**
 * 根据配置跳转到对应的登录页
 */
export const gotoLoginPage = () => {
  if (AUTH_MODE === 'local') {
    gotoLocalLoginPage();
  } else {
    gotoSSOLoginPage();
  }
};

export const getSSOToken = () => {
  return localStorage.getItem(STORAGE_PREFIX + 'sso_token');
};

export const setSSOToken = (token: string) => {
  localStorage.setItem(STORAGE_PREFIX + 'sso_token', token);
};

export const clearSSOToken = () => {
  localStorage.removeItem(STORAGE_PREFIX + 'sso_token');
};

export const saveJWT = (token: string, expires_in: string) => {
  localStorage.setItem(STORAGE_PREFIX + 'token', token);
  localStorage.setItem(STORAGE_PREFIX + 'expires_in', expires_in);
};

/**
 * 退出登录
 */
export const logout = async () => {
  const token = localStorage.getItem(STORAGE_PREFIX + 'token');
  if (token && token !== 'null') {
    // 只有SSO模式才调用SSO的退出接口
    if (AUTH_MODE === 'sso') {
      await doLogout(token);
    }
  }
  gotoLoginPage();
};

export const getToken = () => {
  return localStorage.getItem(STORAGE_PREFIX + 'token');
};

export const clearToken = () => {
  localStorage.removeItem(STORAGE_PREFIX + 'token');
};

const refreshToken = async () => {
  const token = localStorage.getItem(STORAGE_PREFIX + 'token');
  if (!token) {
    return;
  }
  const state = localStorage.getItem(STORAGE_PREFIX + 'refresh_state');
  if (!state) {
    localStorage.setItem(STORAGE_PREFIX + 'refresh_state', '1');
    await refresh(token).then(({ errCode, msg, data }) => {
      if (errCode) {
        console.error(msg);
      } else {
        if (data.token) {
          saveJWT(
            data.token,
            `${new Date().getTime() + data.expiresIn * 1000}`,
          );
        }
      }
      localStorage.removeItem(STORAGE_PREFIX + 'refresh_state');
    });
  }
};

export const checkToken = async () => {
  const expires_in = localStorage.getItem(STORAGE_PREFIX + 'expires_in');
  if (expires_in) {
    const expires_time = Number(expires_in);
    const now = new Date().getTime();
    // 提前5分钟刷新token
    if (now > expires_time - 5 * 60 * 1000) {
      await refreshToken();
    }
  }
};
