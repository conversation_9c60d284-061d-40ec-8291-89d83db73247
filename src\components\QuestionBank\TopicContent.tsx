import ConditionalRender from '@/components/ConditionalRender';
import { DeleteOutlined } from '@ant-design/icons';
import { Image, Popconfirm } from 'antd';
import React, { useRef, useState } from 'react';
import { PartialWithRequired } from 'typings';
import styles from './common.less';

interface TopicContentProps {
  info: API.SystemQuestion;
  className?: string;
  showAnalysis?: Record<string, boolean>;
  onShowAnalysis?: (id: string) => void;
  /** 删除父子题里的子题 */
  onDeleteChild?: (id: string, isFather?: boolean) => void;
  /** 显示全部显示解析 */
  allShowAnalysis?: boolean;
  /** 显示删除图标 默认显示 */
  showDeleteIcon?: boolean;
  /** 组题模式 默认：false*/
  isGroupModel?: boolean;
}
export const optionShowlist = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
];

const QuestionOptions = ({ info }: { info: any }) => {
  const { options, baseType } = info || {};
  switch (baseType?.name) {
    case '单选题':
    case '多选题':
    case '选择题':
      return (
        <div className="question-options">
          {((options as unknown as Record<string, string>[]) ?? [])?.map(
            (item, index) => {
              const optionKey = optionShowlist[index];
              const optionValue = item[Object.keys(item)[0]];
              return (
                <div key={index} className="option-item">
                  <span className="option-label">
                    <span className="option-key">{optionKey}</span>、
                  </span>
                  <span dangerouslySetInnerHTML={{ __html: optionValue }} />
                </div>
              );
            },
          )}
        </div>
      );
    case '判断题':
      return null;
    case '填空题':
      return null;
    default:
      return null;
  }
};

const QuestionName = ({ name, index }: { name?: string; index?: number }) => {
  return (
    <div className="question">
      <ConditionalRender
        hasAccess={index !== undefined}
        accessComponent={
          <span className="question-index">{(index ?? 0) + 1}、 </span>
        }
        noAccessComponent={<span className="question-label">题目：</span>}
      />
      <span
        className="question-name"
        dangerouslySetInnerHTML={{ __html: name || '' }}
      />
    </div>
  );
};

export const QuestionAnalysis = ({
  info,
  showAnalysis,
  allShowAnalysis,
}: {
  info: any;
  showAnalysis?: Record<string, boolean>;
  allShowAnalysis?: boolean;
}) => {
  return (
    <ConditionalRender
      hasAccess={allShowAnalysis ?? showAnalysis?.[info._id] ?? false}
      accessComponent={
        <div className={styles.topicAnswer}>
          <div className={styles.answerTitle}>
            <span className={styles.lable}>参考答案：</span>
            <span
              className={styles.analysis}
              dangerouslySetInnerHTML={{
                __html:
                  info?.baseType?.name === '多选题'
                    ? JSON.parse(info?.answer || '[]')?.join('、 ')
                    : info?.answer || '暂无答案',
              }}
            />
          </div>
          <div className={styles.answerAnalysis}>
            <span className={styles.lable}>试题解析：</span>
            <span
              className={styles.analysis}
              dangerouslySetInnerHTML={{
                __html: info?.analysis || '暂无解析',
              }}
            />
          </div>
        </div>
      }
    />
  );
};

const TopicContent: React.FC<TopicContentProps> = ({
  info,
  className,
  showAnalysis,
  onShowAnalysis,
  onDeleteChild,
  allShowAnalysis,
  // showDeleteIcon = true,
  // isGroupModel,
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  React.useEffect(() => {
    if (contentRef.current) {
      const images = contentRef.current.querySelectorAll('img');

      const handleImageClick = (img: HTMLImageElement) => {
        setPreviewImage(img.src);
        setPreviewVisible(true);
      };

      images.forEach((img) => {
        img.style.cursor = 'zoom-in';
        img.onclick = (e) => {
          e.stopPropagation();
          handleImageClick(img);
        };
      });

      return () => {
        images.forEach((img) => {
          img.onclick = null;
        });
      };
    }
  }, [contentRef.current]);
  return (
    <>
      <ConditionalRender
        hasAccess={info?.isCompose ?? false}
        accessComponent={
          <div className={className} ref={contentRef}>
            <QuestionName name={info?.name} />
            {info?.children?.map(
              (
                item: PartialWithRequired<API.SystemQuestion, '_id'>,
                index: number | undefined,
              ) => (
                <div key={item._id} className="sub-question">
                  <div className="question-box">
                    <div
                      className="question-content"
                      onClick={() => onShowAnalysis?.(item?._id)}
                    >
                      <QuestionName name={item?.name} index={index} />
                      <QuestionOptions info={item} />
                    </div>
                    <ConditionalRender
                      hasAccess={
                        // showDeleteIcon && index !== undefined && !isGroupModel
                        // TODO 子题不可删除，防止编辑时出现问题
                        false
                      }
                      accessComponent={
                        <Popconfirm
                          title="确定删除该题目吗？"
                          description="删除后将无法恢复，请谨慎操作！"
                          onConfirm={() => {
                            onDeleteChild?.(item._id, item?.isCompose);
                          }}
                        >
                          <div className="question-delete">
                            <DeleteOutlined className={styles.icon} />
                            &nbsp; 删除
                          </div>
                        </Popconfirm>
                      }
                    />
                  </div>
                  <QuestionAnalysis
                    info={item}
                    allShowAnalysis={allShowAnalysis}
                    showAnalysis={showAnalysis}
                  />
                </div>
              ),
            )}
          </div>
        }
        noAccessComponent={
          <div
            className={className}
            onClick={() => onShowAnalysis?.(info?._id)}
            ref={contentRef}
          >
            <QuestionName name={info?.name} />
            <QuestionOptions info={info} />
            <QuestionAnalysis
              info={info}
              allShowAnalysis={allShowAnalysis}
              showAnalysis={showAnalysis}
            />
          </div>
        }
      />
      <Image
        width={0}
        style={{ display: 'none' }}
        preview={{
          visible: previewVisible,
          src: previewImage,
          onVisibleChange: (visible) => {
            setPreviewVisible(visible);
          },
        }}
      />
    </>
  );
};
export default TopicContent;
