// 运行时配置

import UnAccessible from '@/pages/403';
import { currentUser as getCurrentUser, localCurrentUser } from '@/services/auth';
import { LogoutOutlined } from '@ant-design/icons';
import {
  history,
  RequestConfig,
  RequestOptions,
  RuntimeAntdConfig,
  RunTimeLayoutConfig,
} from '@umijs/max';
import { ConfigProvider, Dropdown, Image, message, Typography } from 'antd';
import zhCN from 'antd/es/locale/zh_CN'; // 引入中文 locale
import GuidanceModalButton from './components/GuidanceModleBotton.tsx';
import * as authUtil from './utils/auth';

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<API.User | null> {
  const token = authUtil.getToken();
  if (!token) {
    return null;
  }

  let result;
  if (AUTH_MODE === 'local') {
    // 本地认证模式，不需要传token参数
    result = await localCurrentUser();
  } else {
    // SSO认证模式
    result = await getCurrentUser(token);
  }

  const { errCode, data, msg } = result;
  if (errCode) {
    message.error(msg);
    return null;
  }
  authUtil.checkToken();
  localStorage.removeItem(STORAGE_PREFIX + 'refresh_state');
  return data;
}

export function onRouteChange({ location }: { location: Location }) {
  const s = new URLSearchParams(location.search);
  if (s.get('token') && s.get('sso_token') !== 'null') {
    sessionStorage.setItem('homework-ischild', 'true');
  }
}

export const layout: RunTimeLayoutConfig = (initData) => {
  const ischild = sessionStorage.getItem('homework-ischild');
  return {
    logo: '/homework-design/logo.png',
    menu: {
      locale: false,
    },
    layout: 'mix',
    pure: !initData.initialState?.username || ischild === 'true',
    onMenuHeaderClick: () => {
      history.push('/');
    },
    unAccessible: <UnAccessible />,
    avatarProps: {
      src:
        initData.initialState?.avatar ||
        'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg',
      size: 'small',
      title: `${
        initData.initialState?.nickname || initData.initialState?.username
      }，欢迎`,
      render: (_, dom) => {
        return (
          <Dropdown
            menu={{
              items: [
                {
                  key: 'logout',
                  icon: <LogoutOutlined />,
                  label: '退出登录',
                  onClick: authUtil.logout,
                },
              ],
            }}
          >
            {dom}
          </Dropdown>
        );
      },
    },
    menuHeaderRender: (_logo, _title, props) => {
      return (
        <div
          // onClick={() => {
          //   history.push('/');
          // }}
          style={
            props
              ? {
                  width: '100%',
                  height: '80px',
                  color: '#fff',
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'center',
                  gap: '8px',
                  padding: props.collapsed ? 0 : '0 8px',
                }
              : { display: 'none' }
          }
        >
          <Image
            src={
              initData.initialState?.avatar ||
              'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg'
            }
            style={{
              height: props ? (props.collapsed ? '24px' : '80px') : 0,
              maxWidth: '80px',
            }}
            wrapperStyle={{
              borderRadius: '10px',
              overflow: 'hidden',
            }}
            preview={false}
          />
          {!props?.collapsed && (
            <div
              style={{
                flex: 1,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-evenly',
                alignItems: 'center',
              }}
            >
              <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
                {initData.initialState?.nickname}
              </div>
              <div style={{ fontSize: '14px' }}>
                {initData.initialState?.username}
              </div>
              <Typography.Paragraph
                style={{ color: '#aaa', margin: 0 }}
                ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}
              >
                {initData.initialState?.roles
                  ?.map((role) => role.name)
                  .join(', ')}
              </Typography.Paragraph>
            </div>
          )}
        </div>
      );
    },
    menuFooterRender: (props) => {
      return (
        <div
          style={{
            width: '100%',
            height: '32px',
            lineHeight: '32px',
            color: '#fff',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '0 16px 0 24px',
            letterSpacing: '0.15px',
          }}
        >
          {props?.collapsed ? '' : 'Version: '}1.0.0
        </div>
      );
    },
    childrenRender: (dom) => {
      return (
        <div
          style={{
            minHeight: 'calc(100vh - 56px)',
            padding: '24px',
          }}
        >
          {dom} <GuidanceModalButton />
        </div>
      );
    },
    // antd layout全局样式
    token: {
      sider: {
        colorMenuBackground: 'linear-gradient(#0b1755, #1662b9)',
        colorTextMenu: '#aaa',
        colorTextMenuItemHover: '#fff',
        colorTextMenuActive: '#fff',
        colorTextMenuSelected: '#fff',
        colorTextCollapsedButton: 'rgb(22 98 185 / 60%)',
        colorBgMenuItemHover: 'rgb(22 98 185 / 60%)',
        colorBgMenuItemActive: 'rgb(22 98 185 / 60%)',
        colorBgMenuItemSelected: 'rgb(22 98 185 / 60%)',
      },
      pageContainer: {
        paddingInlinePageContainerContent: 0,
        paddingBlockPageContainerContent: 0,
        colorBgPageContainer: 'rgb(203 226 255 / 60%)',
      },
    },
  };
};
// 在应用最外层包裹 ConfigProvider
export function rootContainer(container: React.ReactNode) {
  return <ConfigProvider locale={zhCN}>{container}</ConfigProvider>;
}
// antd 全局样式
export const antd: RuntimeAntdConfig = (memo: any) => {
  // 按需加载
  memo.import = true;

  // 配置 antd 的 App 包裹组件
  memo.appConfig = {
    message: {
      // 配置 message 最大显示数，超过限制时，最早的消息会被自动关闭
      maxCount: 1,
    },
  };

  // antd 全局化配置
  memo.configProvider = {};
  // 配置 antd@5 的 theme token，等同于配置 configProvider.theme，且该配置项拥有更高的优先级。
  memo.theme ??= {
    components: {
      Segmented: {
        itemSelectedBg: '#2979ff',
        itemSelectedColor: '#fff',
      },
      Collapse: {
        headerBg: '#2979ff',
      },
    },
  };
  // memo.theme.algorithm = theme.darkAlgorithm; // 配置 antd5 的预设 dark 算法
  memo.theme.token = {
    colorPrimary: '#2979ff', // 主题色
  };

  return memo;
};

export const request: RequestConfig = {
  baseURL: '/homework_design_api',
  timeout: 30000 * 30,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },
  errorConfig: {
    errorThrower() {},
    errorHandler(error: any) {
      if (error.response) {
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        switch (error.response.status) {
          case 401:
            message.warning('认证信息无效，请重新登录');
            authUtil.logout();
            break;
          case 404:
            message.warning('请求的资源不存在');
            break;
          default:
            message.error('请求失败，请重试。');
            throw error;
        }
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        message.error('没有响应！请重试。');
      } else {
        // 发送请求时出了点问题
        message.error('请求错误，请重试。');
      }
    },
  },
  requestInterceptors: [
    (config: RequestOptions) => {
      // TODO 这里需要实现token刷新
      authUtil.checkToken();
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${authUtil.getToken()}`,
      };
      // 拦截请求配置，进行个性化处理。
      return config;
    },
  ],
  responseInterceptors: [],
};
