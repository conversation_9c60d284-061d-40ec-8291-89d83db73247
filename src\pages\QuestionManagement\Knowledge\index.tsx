/*
 * @Description: 题库知识点管理
 * @Date: 2025-04-21 17:11:13
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-21 18:55:49
 */
import { useCurrentAccess } from '@/common/useCurrentAccess';
import CatalogTree from '@/components/CatalogTree';
import CommonSelect from '@/components/CommonSelect';
import ConditionalRender from '@/components/ConditionalRender';
import * as knowledgePointService from '@/services/knowledge_point';
import {
  bulkCatalogCreate,
  getKnowledgePointByCatalogId,
} from '@/services/knowledge_point';
import { PlusOutlined } from '@ant-design/icons';
import {
  Alert,
  Button,
  Card,
  Col,
  Empty,
  message,
  Popconfirm,
  Row,
  Space,
  Tabs,
  Tree,
} from 'antd';
import { DataNode } from 'antd/es/tree';
import { useEffect, useState } from 'react';
import EditModal from './EditModal';
import styles from './index.less';

/**
 * 将知识点数据转换为Tree组件所需的数据结构
 */
const convertToTreeData = (
  data: API.KnowledgePoint[],
): (DataNode & API.KnowledgePoint)[] => {
  return data.map((item) => ({
    ...item,
    key: item.id,
    title: item.name,
    data: item,
    children: item.children ? convertToTreeData(item.children) : undefined,
  }));
};

export default function KnowledgeManagement() {
  const { isAdmin } = useCurrentAccess();
  // 当前选中的学科
  const [currentSubject, setCurrentSubject] = useState<API.Subject>();
  // 知识点树数据
  const [treeData, setTreeData] = useState<(DataNode & API.KnowledgePoint)[]>(
    [],
  );
  // 加载状态
  const [loading, setLoading] = useState<boolean>(false);
  // 当前选中的知识点
  const [selectedNode, setSelectedNode] = useState<API.KnowledgePoint>();
  // 编辑模态框状态
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  // 编辑模态框数据
  const [editInfo, setEditInfo] = useState<API.KnowledgePoint>();
  /** 当前选中的教材信息 */
  const [data, setData] = useState<{
    section?: API.Dictionarie;
    grade?: API.Dictionarie;
    subject?: API.Subject;
    textbook?: API.Textbook;
    volume?: API.TextbookChecklist;
  }>({});
  /** 选中的章节 */
  const [section, setSection] = useState<number>();
  /** 选中需要关联的知识点 */
  const [knowledgePoint, setKnowledgePoint] = useState<any[]>();
  const [queryLoading, setQueryLoading] = useState<boolean>(false);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  /**
   * 获取知识点树数据
   */
  const fetchKnowledgePointTree = async () => {
    if (!currentSubject?.id) return;

    setLoading(true);
    try {
      const { errCode, data, msg } = await knowledgePointService.tree({
        subjectId: currentSubject.id,
      });
      if (errCode) {
        message.error(msg || '获取知识点数据失败');
        return;
      }
      if (data) {
        setTreeData(convertToTreeData(data));
      }
    } catch (error) {
      console.error('获取知识点数据出错:', error);
      message.error('获取知识点数据出错');
    } finally {
      setLoading(false);
    }
  };

  // 当学科变化时，获取知识点树数据
  useEffect(() => {
    fetchKnowledgePointTree();
  }, [currentSubject]);

  /**
   * 处理学段和学科选择变化
   */
  const handleSelectChange = (info: {
    section?: API.Dictionarie;
    subject?: API.Subject;
  }) => {
    setKnowledgePoint([]);
    setCurrentSubject(info.subject);
    setData(info);
  };

  /**
   * 处理新增知识点
   */
  const handleAdd = () => {
    setEditInfo(undefined);
    setModalVisible(true);
  };

  /**
   * 处理编辑知识点
   */
  const handleEdit = (node: API.KnowledgePoint) => {
    setEditInfo(node);
    setModalVisible(true);
  };

  /**
   * 处理删除知识点
   */
  const handleDelete = async (node: API.KnowledgePoint) => {
    try {
      const res = await knowledgePointService.remove(String(node.id));
      if (res.errCode) {
        message.error(res.msg || '删除失败');
      }
      message.success('删除成功');
      fetchKnowledgePointTree();
    } catch (error) {
      console.error('删除知识点出错:', error);
      message.error('删除知识点出错');
    }
  };

  /**
   * 处理保存知识点
   */
  const handleSave = async (values: API.KnowledgePoint) => {
    let res;
    if (values.id) {
      // 编辑现有知识点
      res = await knowledgePointService.update(String(values.id), values);
    } else {
      // 创建新知识点
      const newValues = {
        ...values,
        pid: selectedNode?.id || 0,
        subjectId: currentSubject?.id,
      };
      res = await knowledgePointService.create(
        newValues as Omit<API.KnowledgePoint, 'id'>,
      );
    }

    if (res.errCode) {
      message.error(res.msg || (values.id ? '更新失败' : '创建失败'));
      return;
    }
    message.success(values.id ? '更新成功' : '创建成功');
    setModalVisible(false);
    fetchKnowledgePointTree();
  };

  /**
   * 渲染树节点的标题，包含操作按钮
   */
  const renderTreeNodeTitle = (node: API.KnowledgePoint) => {
    return (
      <div className={styles.treeNodeTitle}>
        <span>{node.name}</span>
        <Space className={styles.actionButtons}>
          <Button
            type="link"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleEdit(node);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此知识点吗？"
            description="删除后将无法恢复，且会同时删除其下所有子知识点。"
            onConfirm={(e) => {
              e?.stopPropagation();
              handleDelete(node);
            }}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              size="small"
              onClick={(e) => e.stopPropagation()}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      </div>
    );
  };

  /**
   * 自定义树节点渲染
   */
  const renderTreeNodes = (
    data: (DataNode & API.KnowledgePoint)[],
  ): (DataNode & API.KnowledgePoint)[] => {
    return data.map((item) => {
      const nodeData = item as API.KnowledgePoint;
      const newItem = {
        ...item,
        title: renderTreeNodeTitle(nodeData),
      };
      if (item.children) {
        return {
          ...newItem,
          children: renderTreeNodes(
            item.children as (DataNode & API.KnowledgePoint)[],
          ),
        };
      }
      return newItem;
    });
  };

  /** 章节关联知识点 */
  const handleChapterAssociation = async (checkedKeys: React.Key[]) => {
    if (!section) {
      return message.warning('请选择需要关联的章节');
    }
    const { errCode, msg } = await bulkCatalogCreate(section, checkedKeys);
    if (errCode) {
      return message.warning(`关联失败，${msg}`);
    }
    message.success('关联成功');
  };

  /** 根据id 查询当前关联的知识点 */
  const fetchKnowledgePoint = async (id?: number) => {
    if (!id) {
      setKnowledgePoint([]);
      setExpandedKeys([]); // 清空展开状态
      return;
    }
    setQueryLoading(true);
    try {
      const res = await getKnowledgePointByCatalogId(id);
      if (res?.errCode) {
        message.warning(`查询失败，${res?.msg}`);
        setKnowledgePoint([]);
        setExpandedKeys([]);
        return;
      }

      if (res) {
        setKnowledgePoint(res.data);
        // 自动展开已关联的知识点及其父节点
        const keys = new Set<React.Key>();
        const findParents = (nodes: any[], targetId: number): boolean => {
          for (const node of nodes) {
            if (node.id === targetId) {
              keys.add(node.id);
              return true;
            }
            if (node.children) {
              if (findParents(node.children, targetId)) {
                keys.add(node.id);
                return true;
              }
            }
          }
          return false;
        };

        res.data.forEach((id: number) => {
          findParents(treeData, id);
        });
        setExpandedKeys(Array.from(keys));
      } else {
        setKnowledgePoint([]);
        setExpandedKeys([]);
      }
    } catch (error) {
      console.error('查询知识点出错:', error);
      message.error('查询知识点出错');
      setKnowledgePoint([]);
      setExpandedKeys([]);
    } finally {
      setQueryLoading(false);
    }
  };

  return (
    <div className={styles.topBar}>
      <Tabs
        destroyInactiveTabPane
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: '知识点管理',
            children: (
              <>
                <Card className={styles.selectCard}>
                  <CommonSelect level="subject" onChange={handleSelectChange} />
                </Card>

                <Card
                  extra={
                    <ConditionalRender
                      hasAccess={isAdmin}
                      accessComponent={
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={handleAdd}
                        >
                          新增知识点
                        </Button>
                      }
                    />
                  }
                  loading={loading}
                  className={styles.treeCard}
                >
                  {treeData.length > 0 ? (
                    <Tree
                      showLine
                      defaultExpandAll
                      treeData={isAdmin ? renderTreeNodes(treeData) : treeData}
                      onSelect={(_, info) => {
                        setSelectedNode(info.node as API.KnowledgePoint);
                      }}
                    />
                  ) : (
                    <Empty
                      description={
                        currentSubject ? '暂无知识点数据' : '请先选择学科'
                      }
                    />
                  )}
                </Card>

                <EditModal
                  open={modalVisible}
                  info={editInfo}
                  onSave={handleSave}
                  onClose={() => setModalVisible(false)}
                  subjectId={currentSubject?.id}
                  parentId={selectedNode?.id}
                />
              </>
            ),
          },
          {
            key: '2',
            label: '章节关联知识点',
            children: (
              <>
                <Card className={styles.selectCard}>
                  <CommonSelect level="volume" onChange={handleSelectChange} />
                </Card>
                <div className="commonWapper">
                  <ConditionalRender
                    hasAccess={isAdmin}
                    accessComponent={
                      <Alert
                        message="注意：勾选知识点将立即关联到选中的章节，请谨慎操作！"
                        type="warning"
                        showIcon
                        style={{
                          marginBottom: 16,
                        }}
                      />
                    }
                  />
                  <ConditionalRender
                    hasAccess={treeData.length > 0}
                    accessComponent={
                      <Row gutter={16}>
                        <Col span={12}>
                          <CatalogTree
                            cardTitle="章节课时"
                            readonly
                            currentTextbookChecklist={data?.volume}
                            onSelect={(node) => {
                              setSection(node?.id);
                              fetchKnowledgePoint(node?.id);
                            }}
                          />
                        </Col>
                        <Col span={12}>
                          <Card loading={queryLoading} title="知识点">
                            <Tree
                              showLine
                              checkable={isAdmin}
                              treeData={treeData}
                              checkedKeys={knowledgePoint || []}
                              expandedKeys={expandedKeys}
                              onExpand={(keys) => setExpandedKeys(keys)}
                              onSelect={(_, info) => {
                                setSelectedNode(
                                  info.node as API.KnowledgePoint,
                                );
                              }}
                              onCheck={(checkedKeys) => {
                                setKnowledgePoint(checkedKeys as React.Key[]);
                                handleChapterAssociation(
                                  checkedKeys as React.Key[],
                                );
                              }}
                            />
                          </Card>
                        </Col>
                      </Row>
                    }
                    noAccessComponent={
                      <Empty
                        description={
                          currentSubject ? '暂无知识点数据' : '请先选择学科'
                        }
                      />
                    }
                  />
                </div>
              </>
            ),
          },
        ]}
      />
    </div>
  );
}
