import { Table } from 'antd';
import React from 'react';
import NoDataChart from './NoDataChart';

interface RankingProps {
  data?: any[];
  type?: 'school' | 'county' | 'city';
  loading?: boolean;
  shouSuggest?: boolean;
}

const Ranking: React.FC<RankingProps> = ({
  data,
  type,
  loading,
  shouSuggest,
}) => {
  const columns: any[] = [
    { title: '排名', align: 'center', dataIndex: 'index', key: 'index' },
    {
      title: '学校名称',
      align: 'center',
      dataIndex: 'enterprise_name',
      key: 'enterprise_name',
    },
    {
      title: '学段',
      align: 'center',
      dataIndex: 'grade_section_name',
      key: 'grade_section_name',
    },
    {
      title: '引用次数',
      align: 'center',
      dataIndex: 'template_cite_number',
      key: 'template_cite_number',
      render: (text: any) => <div>{text} 次</div>,
    },
  ];

  const columns2: any[] = [
    { title: '排名', align: 'center', dataIndex: 'index', key: 'index' },
    {
      title: type === 'city' ? '区/县' : '学校',
      align: 'center',
      escape: false,
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: '科目',
      align: 'center',
      dataIndex: 'subject',
      key: 'subject',
    },
    {
      title: '平均时长',
      align: 'center',
      dataIndex: 'total_average_time',
      key: 'total_average_time',
      render: (text: any) => <div>{text} 分钟</div>,
    },
    {
      title: '建议时长',
      align: 'center',
      dataIndex: 'suggested_time',
      key: 'suggested_time',
      hidden: !shouSuggest,
      render: (text: any) => <div>{text} 分钟</div>,
    },
  ];

  return (
    <NoDataChart data={data} loading={loading}>
      <Table
        style={{
          width: '100%',
        }}
        columns={type === 'school' ? columns : columns2}
        dataSource={data}
        pagination={{
          pageSize: 5,
          showSizeChanger: false,
        }}
      />
    </NoDataChart>
  );
};
export default Ranking;
