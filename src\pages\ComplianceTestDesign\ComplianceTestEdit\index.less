.homeworkTemplate {
  background-color: #f1f1f1;
  position: relative;

  .paperMainTitle {
    text-align: center;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;

    :global {
      .ant-input-affix-wrapper {
        display: inline-flex;
        width: auto;
        max-width: 100%;

        .ant-input {
          min-width: 60px;
          padding: 2px 8px;
          text-align: center;
          border: none;
          font-size: 16px;
          font-weight: bold;
          transition: none; // 移除过渡效果

          &:focus-visible,
          &:hover {
            outline: none;
            background-color: var(--primary-color-1);
          }
        }

        .ant-input-suffix {
          margin-left: 0;
        }
      }
    }
  }

  .container {
    padding: 0 8px;
    height: calc(100vh - 68px);
    overflow: hidden;

    .leftWrapper,
    .centerWrapper,
    .rightWrapper {
      height: calc(100vh - 68px);
      overflow-y: auto;
    }
  }
}
