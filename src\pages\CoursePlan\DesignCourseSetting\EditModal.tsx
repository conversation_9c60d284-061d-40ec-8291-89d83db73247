import {
  ModalForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<any>
      width={460}
      title={info ? '编辑课程' : '新增课程'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormSelect
        name="type"
        label="课程类别"
        options={['国家课程', '地方课程', '校本课程']}
        rules={[{ required: true, message: '请输入课程类别！' }]}
      />
      <ProFormText
        name="course_name"
        label="课程名称"
        fieldProps={{ maxLength: 100 }}
        rules={[{ required: true, message: '请输入课程名称！' }]}
      />
      <ProFormText
        name="grade_name"
        label="适用年级"
        fieldProps={{ maxLength: 100 }}
        rules={[{ required: true, message: '请输入适用年级！' }]}
      />
    </ModalForm>
  );
};

export default EditModal;
