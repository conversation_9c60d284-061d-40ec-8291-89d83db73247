/*
 * @Description: 题库审核管理
 * @Date: 2025-04-21 17:12:02
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-21 17:13:58
 */
import ConditionalRender from '@/components/ConditionalRender';
import { DictionarieState } from '@/models/dictionarie';
import { index } from '@/services/subjects';
import { connect, useModel } from '@umijs/max';
import { Button, message, Result, Tabs, TabsProps } from 'antd';
import React, { useEffect } from 'react';
import Audited from './Audited';
import UnAudited, { UnAuditedRef } from './Unaudited';
import styles from './index.less';

const levelOrder = { 小学: 1, 初中: 2, 高中: 3 };

interface AuditProps {
  dictionarie: DictionarieState;
}

// const renderTabBar: TabsProps['renderTabBar'] = (props, DefaultTabBar) => (
//   <StickyBox offsetTop={56} offsetBottom={0} style={{ zIndex: 1 }}>
//     <DefaultTabBar
//       {...props}
//       style={{ padding: '0 16px', background: '#fff' }}
//     />
//   </StickyBox>
// );
// const renderTabBarKey: TabsProps['renderTabBar'] = (props, DefaultTabBar) => (
//   <StickyBox offsetTop={102} offsetBottom={20} style={{ zIndex: 1 }}>
//     <DefaultTabBar
//       {...props}
//       style={{ padding: '0 16px', background: '#fff' }}
//     />
//   </StickyBox>
// );
const hasRole = (roles: any, roleName: string): boolean => {
  return (
    roles?.some((item: { name: string }) => item.name === roleName) ?? false
  );
};

const Audit: React.FC<AuditProps> = ({ dictionarie }) => {
  const { initialState } = useModel('@@initialState');
  const unAuditedRef = React.useRef<UnAuditedRef>(null);
  /** 学科列表 */
  const [subjectList, setSubjectList] = React.useState<any[]>([]);
  /** 选中的学科 */
  const [selectedSubject, setSelectedSubject] = React.useState<number>();
  const [allExpanded, setAllExpanded] = React.useState(false);
  const [activeKeyList, setActiveKeyList] = React.useState<string>('1');

  /** 小学 */
  const primary = hasRole(initialState?.roles, '小学管理员');
  /** 初中 */
  const middle = hasRole(initialState?.roles, '初中管理员');
  /** 高中 */
  const senior = hasRole(initialState?.roles, '高中管理员');
  /** 管理员 */
  const admin = hasRole(initialState?.roles, '管理员');

  const onChange = (key: string) => {
    setSelectedSubject(parseInt(key));
  };

  // 获取学科列表
  const getSubjectList = async () => {
    const { errCode, data, msg } = await index({});
    if (errCode) {
      message.warning(`获取学科列表失败 ${msg}`);
      return [];
    }
    if (!dictionarie.list) return;
    // 增加基础学段信息
    let period = dictionarie.list.filter(
      (item) => item.type === 'grade_section',
    );
    /** 降级处理，当字典表未拿到值时使用 */
    if (dictionarie.list.length === 0 && period.length === 0) {
      period = [
        {
          code: 'GRADE_PRIMARY',
          type: 'grade_section',
          name: '小学',
          sortOrder: 1,
          status: 1,
        },
        {
          code: 'GRADE_MIDDLE',
          type: 'grade_section',
          name: '初中',
          sortOrder: 2,
          status: 1,
        },
        {
          code: 'GRADE_HIGH',
          type: 'grade_section',
          name: '高中',
          sortOrder: 3,
          status: 1,
        },
      ];
    }

    let newSubjectList = data.list
      ?.map((item: any) => {
        return {
          ...item,
          period:
            period?.length > 0
              ? period.find((p) => p.code === item.grade_section)?.name
              : '',
        };
      })
      .sort((a, b) => {
        const periodA = a.period as keyof typeof levelOrder;
        const periodB = b.period as keyof typeof levelOrder;
        return levelOrder[periodA] - levelOrder[periodB];
      });

    if (primary || middle || senior) {
      newSubjectList = newSubjectList?.filter((item) => {
        const periodName = item.period;
        return (
          (primary && periodName === '小学') ||
          (middle && periodName === '初中') ||
          (senior && periodName === '高中')
        );
      });
    }
    setSubjectList(newSubjectList || []);
    setSelectedSubject(newSubjectList?.[0]?.id);
  };

  useEffect(() => {
    getSubjectList();
  }, []);

  const toggleAll = () => {
    if (unAuditedRef.current) {
      setAllExpanded(!unAuditedRef.current.isAllExpanded);
      unAuditedRef.current?.expandAll(!unAuditedRef.current.isAllExpanded);
    }
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '审核列表',
      children: <UnAudited ref={unAuditedRef} subject={selectedSubject} />,
    },
    {
      key: '2',
      label: '已审核列表',
      children: <Audited ref={unAuditedRef} subject={selectedSubject} />,
    },
  ];

  return (
    <ConditionalRender
      hasAccess={primary || middle || senior || admin}
      accessComponent={
        <div className="commonWapper">
          <Tabs
            className={styles.tabs}
            destroyInactiveTabPane
            tabPosition={'top'}
            activeKey={selectedSubject?.toString()}
            // renderTabBar={renderTabBar}
            items={subjectList.map((item) => {
              return {
                key: item.id?.toString(),
                label: item.period + item.subject,
                children: (
                  <Tabs
                    tabBarExtraContent={
                      <Button type="link" onClick={toggleAll}>
                        {allExpanded ? '收起全部试题' : '展开全部试题'}
                      </Button>
                    }
                    // renderTabBar={renderTabBarKey}
                    destroyInactiveTabPane
                    activeKey={activeKeyList}
                    items={items}
                    onChange={(key) => {
                      setActiveKeyList(key);
                    }}
                  />
                ),
              };
            })}
            onChange={onChange}
          />
        </div>
      }
      noAccessComponent={
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您的账号没有学段信息，请联系管理员设置学段。"
        />
      }
    />
  );
};

export default connect(({ dictionarie }) => ({
  dictionarie,
}))(Audit);
