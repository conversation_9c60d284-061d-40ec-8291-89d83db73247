import { useCurrentAccess } from '@/common/useCurrentAccess';
import ConditionalRender from '@/components/ConditionalRender';
import {
  create,
  index,
  remove,
  update,
} from '@/services/province_homework_concept';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProList } from '@ant-design/pro-components';
import { Button, Form, Popconfirm, Space, message } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const Idea: React.FC = () => {
  const { isAdmin } = useCurrentAccess();
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<any | undefined>(undefined);
  const [form] = Form.useForm();

  const handleSave = async (values: any) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: any) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  return (
    <>
      <ProList<any>
        actionRef={actionRef}
        toolBarRender={() => {
          return [
            <ConditionalRender
              hasAccess={isAdmin}
              key="add"
              accessComponent={
                <Button
                  type="primary"
                  onClick={() => {
                    setCurrent(undefined);
                    setModalVisible(true);
                  }}
                  icon={<PlusOutlined />}
                >
                  新增
                </Button>
              }
            />,
          ];
        }}
        search={{
          filterType: 'light',
        }}
        rowKey="id"
        headerTitle="设计理念管理"
        request={async (params: any) => {
          const { current, pageSize, name, describe } = params;
          const { errCode, msg, data } = await index({
            offset: Number((current || 1) - 1) * Number(pageSize || 10),
            limit: Number(pageSize || 10),
            name: name || undefined,
            describe: describe || undefined,
          });
          if (errCode) {
            message.error(msg || '设计理念管理列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data.list,
            total: data.total,
          };
        }}
        pagination={{
          pageSize: 10,
        }}
        showActions="hover"
        metas={{
          title: {
            dataIndex: 'name',
            search: false,
            render: (_, row) => {
              return (
                <>
                  <a>
                    {row.grade_section_name}
                    {row.subject_name}
                  </a>
                </>
              );
            },
          },
          description: {
            dataIndex: 'design_concept',
            search: false,
          },
          actions: {
            render: (text, row) => {
              return (
                <ConditionalRender
                  hasAccess={isAdmin}
                  key={row.id}
                  accessComponent={
                    <Space>
                      <Button
                        type="link"
                        onClick={() => {
                          setCurrent(row);
                          setModalVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                      <Popconfirm
                        title="确认删除？"
                        onConfirm={() => {
                          handleDel(row);
                        }}
                      >
                        <Button type="link" danger>
                          删除
                        </Button>
                      </Popconfirm>
                    </Space>
                  }
                />
              );
            },
          },
        }}
      />
      <EditModal
        form={form}
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default Idea;
