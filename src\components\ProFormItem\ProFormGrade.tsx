/*
 * @Description: 年级选择组件，传入年级编号，会自动获取并显示对应的年级数据，在ProForm中使用
 * @Date: 2025-02-22 14:46:20
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-22 17:40:57
 */
import { SECTION_GRADE } from '@/constants';
import { DictionarieState } from '@/models/dictionarie';
import { ProForm, ProFormItemProps } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Segmented } from 'antd';
import React, { useEffect, useState } from 'react';
import ColWrapper from '../Wrapper/ColWrapper';

const ProFormGrade: React.FC<
  ProFormItemProps & {
    dictionarie: DictionarieState;
    /** 学期编号，与字典对应 */
    section?: string;
  }
> = (prop) => {
  const { dictionarie, section, colProps, ...others } = prop;

  const [gradeCodeList, setGradeCodeList] = useState<string[]>([]);

  useEffect(() => {
    setGradeCodeList(
      SECTION_GRADE[(section as keyof typeof SECTION_GRADE) || ''] || [],
    );
  }, [section]);

  return (
    <ColWrapper colProps={colProps}>
      <ProForm.Item {...others}>
        <Segmented
          options={dictionarie.list
            .filter(
              (item) =>
                item.type === 'grade' && gradeCodeList.includes(item.code),
            )
            .map((item) => ({
              label: item.name,
              value: item.code,
              title: item.description || item.name,
            }))}
        />
      </ProForm.Item>
    </ColWrapper>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(ProFormGrade);
