import { request } from '@umijs/max';

/** @Get('/overview', { summary: '获取系统统计概览' }) */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ enterprise_count: number; user_count: number }>>(
    '/dashboard/overview',
    {
      method: 'GET',
      params,
    },
  );
}

/**  @Get('/enterprises/statistics', { summary: '获取企业统计数据' }) */
export async function statistics(params: Record<string, any>) {
  return request<API.ResType<any>>('/dashboard/enterprises/statistics', {
    method: 'GET',
    params,
  });
}

/** @Get('/users/statistics', { summary: '获取用户统计数据' }) */
export async function userStatistics(params: Record<string, any>) {
  return request<API.ResType<any>>('/dashboard/users/statistics', {
    method: 'GET',
    params,
  });
}
