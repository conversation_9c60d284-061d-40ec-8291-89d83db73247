import {
  BraftItem,
  ChoiceItem,
  JudgeItem,
  NoQuestion,
  ParentQzItem,
} from '../QuestionItem';
import styles from './index.less';

const QuestionPart = ({
  data,
  showDuration = true,
  settingScore = false,
}: {
  data: any;
  showDuration?: boolean;
  settingScore?: boolean;
  type?: string;
  actionType?: string;
}) => {
  const { question = {}, ind } = data;
  const positionQuestion =
    ind !== undefined && ind !== null ? ind + 1 : undefined;
  const { type = {} } = question || {};
  const tmlx = type?.name;
  return (
    <div
      key={question?._id || positionQuestion}
      className={styles.questionPart}
      onMouseOver={(e) => {
        if (e.currentTarget) {
          e.currentTarget.classList.remove(`${styles.border}`);
        }
      }}
      onMouseOut={(e) => {
        if (e.currentTarget) {
          e.currentTarget.classList.add(`${styles.border}`);
        }
      }}
    >
      {question?.isCompose ? (
        <ParentQzItem
          settingScore={settingScore}
          data={{
            positionQuestion,
            type: tmlx,
            showDuration,
            question,
          }}
        />
      ) : question?.name ? (
        tmlx === '单选题' || tmlx === '多选题' ? (
          <ChoiceItem
            settingScore={settingScore}
            data={{
              positionQuestion,
              type: tmlx,
              showDuration,
              question,
            }}
          />
        ) : tmlx === '判断题' ? (
          <JudgeItem
            settingScore={settingScore}
            data={{
              positionQuestion,
              type: tmlx,
              showDuration,
              question,
            }}
          />
        ) : (
          <BraftItem
            settingScore={settingScore}
            data={{
              positionQuestion,
              type: tmlx,
              showDuration,
              question,
            }}
          />
        )
      ) : (
        <NoQuestion
          data={{
            positionQuestion,
          }}
        />
      )}
    </div>
  );
};

export default QuestionPart;
