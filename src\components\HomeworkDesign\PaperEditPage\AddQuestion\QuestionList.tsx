import QuestionPart from '@/components/HomeworkDesign/PaperQuestionList/QuestionPart';
import styles from './index.less';
const QuestionList = ({ listData }: { listData: API.SystemQuestion[] }) => {
  return (
    <div className={styles.questionList}>
      {listData?.map((item: any, ind: number) => {
        return (
          <QuestionPart
            key={item.key}
            data={{
              question: item,
              ind,
            }}
          />
        );
      })}
    </div>
  );
};

export default QuestionList;
