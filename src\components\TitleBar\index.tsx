import { Space } from 'antd';
import classNames from 'classnames';
import React from 'react';
import ConditionalRender from '../ConditionalRender';
import styles from './index.less';
interface TypeProps {
  /** 标题 */
  title: React.ReactNode;
  /** 前缀 */
  prefix?: boolean;
  /** 操作栏 */
  actionDom?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否隐藏 */
  hiden?: boolean;
  /**高度 */
  height?: number | string;
  /** 宽度 */
  width?: number | string;
  /** 背景色 */
  background?: string;
  /** 字体颜色 */
  color?: string;
}
const TitleBar = ({
  title,
  prefix = true,
  actionDom,
  className,
  style,
  hiden,
  height,
  width,
  background,
  color,
}: TypeProps) => {
  return (
    <ConditionalRender
      hasAccess={!hiden}
      accessComponent={
        <div
          className={classNames(styles.titleBar, className)}
          style={{
            height,
            width,
            ...style,
            background,
            color,
          }}
        >
          <div
            className={classNames(styles.title, prefix ? styles.prefix : '')}
          >
            {title}
          </div>
          <Space>{actionDom}</Space>
        </div>
      }
    />
  );
};

export default TitleBar;
