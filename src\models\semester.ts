import { semesters } from '@/services';
import {
  OnChangePayload,
  onProTableChange,
  onProTableSearch,
} from '@/utils/calc';
import { message } from 'antd';

export type SemesterState = {
  total: number;
  list: API.Semester[];
  currentList: API.Semester[];
  inited: boolean;
};

export default {
  state: {
    total: 0,
    list: [],
    currentList: [],
    inited: false,
  } as SemesterState,

  effects: {
    /** 查询列表 **/
    *queryList(
      { payload }: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(semesters.index, payload);
      if (errCode) {
        message.error(msg || '学年学期列表查询失败');
      } else {
        yield put({ type: 'querySuccess', payload: data });
      }
    },
    *add(
      { payload }: { payload: API.Semester },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(semesters.create, payload);
      if (errCode) {
        message.error(msg || '学年学期创建失败');
      } else {
        message.success('学年学期创建成功');
        yield put({ type: 'queryList', payload: {} });
      }
    },
    *update(
      { payload }: { payload: { id: number; info: Partial<API.Semester> } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(
        semesters.update,
        payload.id,
        payload.info,
      );
      if (errCode) {
        message.error(msg || '学年学期更新失败');
      } else {
        message.success('学年学期更新成功');
        yield put({ type: 'queryList', payload: {} });
      }
    },
    *remove(
      { payload }: { payload: { id: number } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(semesters.remove, payload.id);
      if (errCode) {
        message.error(msg || '学年学期删除失败');
      } else {
        message.success('学年学期删除成功');
        yield put({ type: 'removeSuccess', payload });
      }
    },
  },

  reducers: {
    /** 查询列表成功后更新数据，触发渲染 */
    querySuccess(
      _state: SemesterState,
      { payload }: { payload: SemesterState },
    ) {
      return { ...payload, currentList: payload.list, inited: true };
    },
    /** proTable组件的过滤和排序操作，分页不用处理，组件自己会完成 */
    onChange(
      state: SemesterState,
      { payload }: { payload: OnChangePayload<API.Semester> },
    ) {
      const filteredList = onProTableChange(state.list, payload);
      return {
        ...state,
        total: filteredList.length,
        currentList: filteredList,
      };
    },
    /** proTable组件的查询操作 */
    onSearch(
      state: SemesterState,
      { payload }: { payload: Partial<API.Semester> },
    ) {
      const newList = onProTableSearch(state.list, payload);
      return {
        ...state,
        total: newList.length,
        currentList: newList,
      };
    },
    removeSuccess(
      state: SemesterState,
      { payload }: { payload: { id: number } },
    ) {
      const newList = state.list.filter((item) => item.id !== payload.id);
      const currentList = state.currentList.filter(
        (item) => item.id !== payload.id,
      );
      return {
        ...state,
        list: newList,
        currentList,
      };
    },
  },
};
