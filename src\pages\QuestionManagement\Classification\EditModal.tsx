import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (info && open) {
      form.setFieldsValue(info);
    }
  }, [info, open]);

  return (
    <ModalForm<any>
      form={form}
      title={info ? '编辑题目分类' : '新增题目分类'}
      autoFocusFirstInput
      modalProps={{
        width: 500,
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" hidden />

      <ProFormText
        name="name"
        label="名称"
        fieldProps={{ maxLength: 50 }}
        rules={[{ required: true, message: '请输入名称！' }]}
      />
      <ProFormTextArea
        name="description"
        label="描述"
        fieldProps={{ maxLength: 200 }}
      />
    </ModalForm>
  );
};

export default EditModal;
