/*
 * @Description: 教材目录树组件，带有头部
 * @Date: 2025-02-28 12:02:24
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-24 15:46:22
 */

import CatalogTree from '@/components/CatalogTree';
import CommonSelect, { CommonSelectLevel } from '@/components/CommonSelect';
import CustomCollapse from '@/components/CustomCollapse';
import { DictionarieState } from '@/models/dictionarie';
import { connect } from '@umijs/max';
import { useEffect, useState } from 'react';
import { CatalogTreeNode } from './CatalogTree/typings';

const CatalogTreeWithHead: React.FC<{
  level?: CommonSelectLevel;
  onSelect?: (info: {
    section?: API.Dictionarie;
    grade?: API.Dictionarie;
    subject?: API.Subject;
    textbook?: API.Textbook;
    catalog?: API.TextbookCatalog;
    volume?: API.TextbookChecklist;
  }) => Promise<void> | void;
  dictionarie: DictionarieState;
}> = ({ level, onSelect }) => {
  const [title, setTitle] = useState<string>('选择教材');
  const [data, setData] = useState<{
    section?: API.Dictionarie;
    grade?: API.Dictionarie;
    subject?: API.Subject;
    textbook?: API.Textbook;
    volume?: API.TextbookChecklist;
  }>();

  const [activeKey, setActiveKey] = useState<string | undefined>();
  const [lastSelectedNode, setLastSelectedNode] =
    useState<CatalogTreeNode | null>(null);

  useEffect(() => {
    if (data) {
      const section_str = data.section ? `${data.section.name} / ` : '';
      const subject_str = data.subject ? `${data.subject.subject} / ` : '';
      const textbook_str = data.textbook
        ? `${data.textbook.textbook_version} / `
        : '';
      const grade_str = data.grade ? `${data.grade.name} / ` : '';
      const volume_str = data.volume ? `${data.volume.volume} / ` : '';
      const title = `${section_str}${grade_str}${subject_str}${textbook_str}${volume_str}`;
      // 去掉最后一个 /
      const title2 = title.slice(0, -2);
      setTitle(title2);
    } else {
      setTitle('选择教材');
    }
  }, [data]);

  return (
    <div>
      <CustomCollapse
        activeKey={activeKey}
        fixed
        items={[
          {
            key: '1',
            label: title,
            forceRender: true,
            children: (
              <CommonSelect
                level={level || 'textbook'}
                onChange={(info: {
                  section?: API.Dictionarie;
                  grade?: API.Dictionarie;
                  subject?: API.Subject;
                  textbook?: API.Textbook;
                  volume?: API.TextbookChecklist;
                }) => {
                  // console.log('info', info);
                  const { section, subject, textbook, grade, volume } = info;
                  if (
                    !data ||
                    data.section?.code !== section?.code ||
                    data.subject?.id !== subject?.id ||
                    data.textbook?.id !== textbook?.id ||
                    data.grade?.code !== grade?.code ||
                    data.volume?.id !== volume?.id
                  ) {
                    setData(info);
                    onSelect?.(info);
                  }
                }}
              />
            ),
            onMouseEnter: () => {
              // 鼠标进入时，展开下拉框
              setActiveKey('1');
            },
            onMouseLeave: () => {
              // 鼠标离开时，收起下拉框
              setActiveKey('');
              setTimeout(() => {
                setActiveKey(undefined);
              }, 10);
            },
          },
        ]}
        defaultActiveKey={['1']}
        style={{ marginBottom: 16 }}
      />
      <CatalogTree
        readonly
        currentTextbookChecklist={data?.volume}
        onSelect={(node) => {
          // 只有当节点变化时才触发回调
          if (
            (node && node.id !== lastSelectedNode?.id) ||
            (!node && lastSelectedNode)
          ) {
            onSelect?.({
              ...data,
              catalog: node as API.TextbookCatalog,
            });
            setLastSelectedNode(node);
          }
        }}
      />
    </div>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(
  CatalogTreeWithHead,
);
