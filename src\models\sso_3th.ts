/*
 * @Description: 通过第三方ssotoken 登录，放在这里的好处是可以统一维护登录调用状态，避免重复调用
 * @Date: 2025-05-08 10:05:51
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-05-09 10:09:31
 */
import { getUserInfoBySsoToken, loginWithSsoToken } from '@/services/auth';
import { saveJWT } from '@/utils/auth';
import { message } from 'antd';

export type SSOState = {
  done: boolean;
  parseDone: boolean;
  ssoUserInfo?: {
    username: string;
  };
};
export default {
  state: {
    parseDone: false,
    done: false,
  } as SSOState,

  effects: {
    /** 查询列表 **/
    *login(
      { payload }: { payload: { token: string } },
      { call, put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'setDone', payload: { done: false } });
      const { errCode, msg, data } = yield call(
        loginWithSsoToken,
        payload.token,
      );
      if (errCode) {
        message.error(msg);
      } else {
        yield put({ type: 'loginSuccess', payload: data || {} });
      }
    },

    /** 解析ssoToken */
    *parseSsoToken(
      { payload }: { payload: { token: string } },
      { call, put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'setParseDone', payload: { parseDone: false } });
      const { errCode, msg, data } = yield call(
        getUserInfoBySsoToken,
        payload.token,
      );
      if (errCode) {
        // 这个接口属于探测阶段，所以不用提示
        // message.error(msg);
        console.error(msg);
      } else {
        yield put({ type: 'parseSuccess', payload: data || {} });
      }
    },
  },

  reducers: {
    /** 查询列表成功后更新数据，触发渲染 */
    loginSuccess(
      _state: SSOState,
      {
        payload,
      }: {
        payload: {
          token: string;
          expiresIn: number;
        };
      },
    ) {
      if (payload.token) {
        saveJWT(
          payload.token,
          `${new Date().getTime() + (payload.expiresIn || 3600 * 12) * 1000}`,
        );
      }
      return {
        done: true,
      };
    },

    parseSuccess(
      _state: SSOState,
      {
        payload,
      }: {
        payload: API.User;
      },
    ) {
      if (payload) {
        return {
          parseDone: true,
          ssoUserInfo: {
            username: payload.username,
          },
        };
      }
      return {};
    },

    setDone(
      state: SSOState,
      {
        payload,
      }: {
        payload: { done: boolean };
      },
    ) {
      return {
        ...state,
        done: payload.done,
      };
    },

    setParseDone(
      state: SSOState,
      {
        payload,
      }: {
        payload: { parse: boolean };
      },
    ) {
      return {
        ...state,
        parse: payload.parse,
      };
    },
  },
};
