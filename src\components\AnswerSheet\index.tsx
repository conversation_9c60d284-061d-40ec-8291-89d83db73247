/* eslint-disable */
import { downPaperToWord } from '@/services/htmlToWord';
import { convertSpecialNumber } from '@/utils/calc';
import {
  ContainerOutlined,
  FontColorsOutlined,
  FontSizeOutlined,
  LineHeightOutlined,
} from '@ant-design/icons';
import { DrawerForm } from '@ant-design/pro-components';
import {
  Button,
  Radio,
  RadioChangeEvent,
  Select,
  Space,
  Spin,
  message,
} from 'antd';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useEffect, useRef, useState } from 'react';
import PreviewModal from '../PreviewModal';
import TinyMCEEditor from '../TinyMCEEditor';
import {
  fontFamilyOptions,
  fontSizeOptions,
  lineHeightOptions,
} from './defaultSetting';
import styles from './index.less';
import ChineseWriting from './QuestionRender/ChineseWriting';
import SubjectiveItem from './QuestionRender/SubjectiveItem';
import {
  buildLayout,
  calculateHeight,
  composeContent,
  convertData,
  reloadOptions,
  renderOptions,
  updatePageData,
  viewTemplate2,
} from './util';

const AnswerSheet = ({
  paperName,
  strucData,
  questionData,
}: {
  paperName: string;
  strucData: any;
  questionData: any;
}) => {
  const htmlRef = useRef<HTMLDivElement>(null);
  const preloadRef = useRef<HTMLDivElement>(null);
  const docRefs = useRef<any[]>([]);
  const [listLoading, setListLoading] = useState<boolean>(false);
  const [paperSetting, setPaperSetting] = useState<any>({});
  const [paperData, setPaperData] = useState<any>([]);
  const [paperLayout, setPaperLayout] = useState<any>([]);
  const [imgSrcs, setImgSrcs] = useState<any[]>([]);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isPdfDownloading, setIsPdfDownloading] = useState<boolean>(false);
  const [isWordDownloading, setIsWordDownloading] = useState<boolean>(false);
  const [fontSettings, setFontSettings] = useState({
    templateFontSize: 16,
    templateFontFamily: '微软雅黑',
    lineHeight: 1.5,
  });
  const getImgs = async () => {
    if (docRefs.current?.length) {
      const newArr = docRefs.current.filter((v) => v !== null);
      const imgSrcs: any[] = [];
      for (let index = 0; index < newArr.length; index++) {
        const refs = newArr[index];
        if (refs) {
          const canvas = await html2canvas(refs, {
            useCORS: true, // 开启跨域请求
          }); // 生成图片
          imgSrcs.push({
            type: 'image/png',
            title: `dataset_${index + 1}.png`,
            src: canvas.toDataURL('image/png'),
          });
        }
      }
      setImgSrcs(imgSrcs);
    }
  };
  const handleDownloadPDF = async () => {
    setIsPdfDownloading(true);
    const doc = new jsPDF('p', 'mm', 'a4'); // 创建 A4 PDF 文档
    if (docRefs.current?.length) {
      const newArr = docRefs.current.filter((v) => v !== null);
      for (let index = 0; index < newArr.length; index++) {
        const refs = newArr[index];
        if (refs) {
          const canvas = await html2canvas(refs, {
            scale: 2, // 使用 2 的缩放比例，可以根据需求适当调整
            useCORS: true, // 开启跨域请求
          }); // 生成图片
          // 将 Canvas 转换为 JPEG 格式，设置合适的压缩质量
          const imgData = canvas.toDataURL('image/jpeg', 0.7); // JPEG 格式与 70% 压缩，适度保持质量
          // 每页的宽高
          const imgWidth = 210; // A4 宽度 (mm)
          const pageHeight = 295; // A4 高度 (mm)
          const imgHeight = (canvas.height * imgWidth) / canvas.width; // 根据原始宽高比计算高度
          let heightLeft = imgHeight;

          let position = 0;

          // 如果图片高度超过一页
          if (heightLeft >= pageHeight) {
            // 添加第一页
            doc.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
            position -= pageHeight;

            // 处理剩余的页面
            while (heightLeft >= 0 && index < newArr.length - 1) {
              doc.addPage();
              doc.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
              heightLeft -= pageHeight;
              position -= pageHeight;
            }
          } else {
            // 如果图片可以放在当前页
            doc.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
          }
        }
      }
    }
    setIsPdfDownloading(false);
    // 保存 PDF
    doc.save(paperName + '.pdf');
  };
  const handleDownloadWord = async () => {
    // 获取所有questionIds并按顺序排序
    const sortedQuestionIds = strucData.bigQuestionOrder.flatMap(
      (bigQuestionId: string) =>
        strucData.bigQuestions[bigQuestionId].questionIds,
    );

    // 优化1：提前创建Map提高性能
    const questionMap = new Map(
      questionData.map((item: any) => [item._id, item]),
    );

    // 优化2：使用filter(Boolean)过滤掉undefined值
    const sortedQuestions = sortedQuestionIds
      .map((id: string) => questionMap.get(id))
      .filter(Boolean);
    const questions = sortedQuestions.map((item: any) => {
      return {
        question_id: item._id,
        source_table: item.source_table || item.tableName,
      };
    });
    const { bigQuestions, bigQuestionOrder } = strucData;
    const data = bigQuestionOrder?.map((key: string) => bigQuestions?.[key]);
    try {
      setIsWordDownloading(true);
      const {
        errCode,
        data: responseData,
        msg,
      } = await downPaperToWord({
        name: paperName,
        struct: data,
        questions,
        options: {
          fontSize: fontSettings.templateFontSize,
          fontFamily: fontSettings.templateFontFamily,
          lineHeight: fontSettings.lineHeight,
        },
      });
      if (errCode) {
        setIsWordDownloading(false);
        return message.error('下载失败，请联系管理员后重试！' + msg);
      }
      const uint8Array = new Uint8Array(responseData.data);
      // 假设response.data是Buffer数据
      const blob = new Blob([uint8Array], { type: 'application/msword' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `${paperName}.doc`;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error('下载失败:', error);
    } finally {
      setIsWordDownloading(false);
    }
  };
  const resizeEditor = async (data: any) => {
    const { pageData, templateData } = await viewTemplate2(
      data,
      preloadRef.current,
    );
    setPaperLayout(data);
    setPaperSetting(pageData);
    setPaperData(templateData);
  };
  const resizeOption = async (key: string, num: number) => {
    const i = paperLayout?.find((e: { key: any }) => e.key === key);
    if (i) {
      i.lineNumber = num;
      const h = renderOptions(i);
      const u = await calculateHeight(h, preloadRef.current);
      i.opHeight = Math.ceil(u);
      i.height = Math.ceil(u) + Math.ceil(i.nameHeight);
      resizeEditor(paperLayout);
    }
  };
  const optionChange = async (
    e: { name?: string; content: string; key?: string },
    t: string,
  ) => {
    const n = t.split('-')[0],
      i = paperLayout.find((e: { key: string }) => e.key === n);
    if (i) {
      const { content: t, key: k } = e;
      const o = i.options?.find((v: { key: string }) => v.key === k);
      o.content = t;
      await reloadOptions(i, preloadRef.current);
      resizeEditor(paperLayout);
    }
  };
  const editorHeightChange = (e: number, t: string, n: number) => {
    const i = paperData[n].find((e: { key: string }) => e.key === t);
    i.height = e;
    const r = t.split('-')[0],
      { content: o, height: s } = composeContent(
        paperData,
        r,
        preloadRef.current,
      ),
      a = paperLayout.find((e: { key: string }) => e.key === r);
    a && ((a.height = s), (a.content = o));

    if (i.type === 'subjective') {
      let e = 30 * (i.answerAreaRows || 1);
      a.height += e;
    }
    resizeEditor(paperLayout);
  };
  const editorChange = (e: string, t: string, n: number) => {
    const i = paperData[n].find((e: { key: string }) => e.key === t);
    i.content = e;
    const r = t.split('-')[0],
      { content: o } = composeContent(paperData, r, preloadRef.current),
      s = paperLayout.find((e: { key: string }) => e.key === r);
    s && (s.content = o);
  };
  const initData = async () => {
    setListLoading(true);
    setPaperLayout([]);
    setPaperData([]);
    const { bigQuestions, bigQuestionOrder = [] } = strucData || {};
    if (bigQuestions && bigQuestionOrder?.length > 0) {
      // 试题结构根据 order 数组的顺序重新排序
      const sortedArray = [
        ...bigQuestionOrder.map((key: string) => bigQuestions[key]),
      ];
      const newQuesData = JSON.parse(JSON.stringify(questionData));
      const questionLayout = convertData(sortedArray, newQuesData);
      questionLayout?.unshift(
        {
          key: 'paperTitle',
          desctype: 'paperTitle',
          name: paperName,
          paperName: paperName,
        },
        {
          key: 'paperInfo',
          desctype: 'groupContent',
          name: '<div style="font-size: 14px; text-align: center; line-height: 30px;" data-mce-style="font-size: 14px; text-align: center; line-height: 30px;">姓名：__________ 班级：__________ 考号：__________</div>',
        },
      );
      const { templateLayout } = await buildLayout(
        questionLayout,
        preloadRef.current,
      );
      setPaperLayout(templateLayout);
      const { pageData, templateData } = await viewTemplate2(
        templateLayout,
        preloadRef.current,
      );
      setPaperSetting(pageData);
      setPaperData(templateData);
    }
    setListLoading(false);
  };

  // 添加工具栏组件
  const Toolbar = () => (
    <div className={styles.toolbar}>
      <Space>
        字体设置：
        <Select
          value={fontSettings.templateFontFamily}
          onChange={(value) => {
            setFontSettings({ ...fontSettings, templateFontFamily: value });
          }}
          suffixIcon={<FontColorsOutlined />}
          style={{ width: 120 }}
          options={fontFamilyOptions}
        />
        <Select
          value={fontSettings.templateFontSize}
          onChange={(value) =>
            setFontSettings({ ...fontSettings, templateFontSize: value })
          }
          suffixIcon={<FontSizeOutlined />}
          style={{ width: 80 }}
          options={fontSizeOptions}
        />
        行高设置：
        <Select
          value={fontSettings.lineHeight}
          onChange={(value) =>
            setFontSettings({ ...fontSettings, lineHeight: value })
          }
          suffixIcon={<LineHeightOutlined />}
          style={{ width: 80 }}
          options={lineHeightOptions}
        />
      </Space>
    </div>
  );
  useEffect(() => {
    if (fontSettings) {
      updatePageData(fontSettings);
      setTimeout(() => {
        initData();
      });
    }
  }, [fontSettings]);
  return (
    <DrawerForm
      title="试卷排版"
      submitter={false}
      onOpenChange={(open: boolean) => {
        if (open && strucData) {
          initData();
        }
      }}
      drawerProps={{
        width: '100vw',
        placement: 'right',
        maskClosable: false,
        destroyOnClose: true,
        footer: false,
        styles: {
          content: {
            background: '#f1f1f1',
          },
          body: {
            padding: 0,
          },
        },
      }}
      className={styles.customPreviewDrawer}
      trigger={<Button icon={<ContainerOutlined />}>试卷排版</Button>}
    >
      <Spin tip="数据加载中..." spinning={listLoading}>
        <div className={styles.container}>
          <div className={styles.optionBar}>
            <Toolbar />
            <Space>
              {/* <Button
              onClick={() => {
                setImgSrcs([]);
                setIsOpen(true);
                docRefs && docRefs.current && getImgs();
              }}
            >
              预览
            </Button> */}
              <Button onClick={handleDownloadPDF} loading={isPdfDownloading}>
                下载试卷
              </Button>
              <Button
                style={{
                  display: 'none',
                }}
                onClick={handleDownloadWord}
                loading={isWordDownloading}
              >
                下载word
              </Button>
            </Space>
          </div>
          <div ref={htmlRef}>
            {paperData?.map((page: any, i: number) => {
              return (
                <div
                  key={`section-${page?.length}-${i}`}
                  className="card-content"
                  ref={(el) => docRefs?.current && (docRefs.current[i] = el)} // 保存每个数据集的 DOM 引用
                  style={{
                    width: paperSetting?.pageSize?.w,
                    height: paperSetting?.pageSize?.h,
                    paddingBlock: paperSetting?.pagePadding[0],
                    paddingInline: paperSetting?.pagePadding[1],
                    fontFamily: paperSetting?.templateFontFamily,
                    fontSize: paperSetting?.templateFontSize,
                    lineHeight: paperSetting?.lineHeight,
                  }}
                >
                  {page.map((item: any) => {
                    const t = item.pid ? 'numberToCircled' : 'normal';
                    if (item.desctype === 'paperTitle') {
                      const { key, paperName } = item;
                      return (
                        <div key={key} className="paperTitle">
                          <h1>{paperName}</h1>
                        </div>
                      );
                    }
                    if (item.desctype === 'options') {
                      const { lineNumber, options, content } = item;
                      return (
                        <>
                          <div
                            className="questionWrap"
                            // style={{
                            //   minHeight: item?.nameHeight,
                            // }}
                          >
                            <span
                              className="questionNum"
                              style={{
                                visibility: item?.maxSplitPage
                                  ? 'hidden'
                                  : 'visible',
                              }}
                            >
                              {convertSpecialNumber(item.num, t)}.
                            </span>
                            <TinyMCEEditor
                              id={item.key}
                              content={content}
                              editorChange={(t) => {
                                return editorChange(t, item.key, i);
                              }}
                              editorHeightChange={(n) => {
                                const o = paperLayout.find(
                                  (e: { key: string }) => e.key === item.key,
                                );
                                if (o) {
                                  o.height = n + o.opHeight + 20;
                                  o.nameHeight = n;
                                  resizeEditor(paperLayout);
                                }
                              }}
                            />
                          </div>
                          <div
                            key={
                              item.key + item?.opHeight + '_' + item.lineNumber
                            }
                            className="option-row ant-row"
                            // style={{
                            //   minHeight: item?.opHeight,
                            // }}
                          >
                            {options?.map((op: any, index: number) => {
                              return (
                                <div
                                  key={
                                    item.key +
                                    '_' +
                                    index +
                                    item?.height +
                                    '_' +
                                    item.lineNumber
                                  }
                                  className={`question-option ant-col-${
                                    24 / lineNumber
                                  }`}
                                >
                                  <span
                                    className="option"
                                    style={{
                                      lineHeight: paperSetting.lineHeight,
                                    }}
                                  >
                                    [
                                    <span
                                      style={{
                                        paddingInline: 3,
                                      }}
                                    >
                                      {op.name}
                                    </span>
                                    ]
                                  </span>
                                  <div className="option-content">
                                    <TinyMCEEditor
                                      id={item.key + op.key}
                                      key={item.key + op.key}
                                      content={op.content}
                                      editorChange={async (t) => {
                                        const n = {
                                          name: op.name,
                                          content: t,
                                          key: op.key,
                                        };
                                        return await optionChange(n, item.key);
                                      }}
                                      editorHeightChange={() => {}}
                                    />
                                  </div>
                                </div>
                              );
                            })}
                            <div className="set-option">
                              <span className="set-radio">
                                每行选项个数：
                                <Radio.Group
                                  onChange={(e: RadioChangeEvent) => {
                                    resizeOption(item.key, e.target.value);
                                  }}
                                  value={lineNumber}
                                >
                                  <Radio value={1}>1</Radio>
                                  <Radio value={2}>2</Radio>
                                  <Radio value={4}>4</Radio>
                                </Radio.Group>
                              </span>
                            </div>
                          </div>
                        </>
                      );
                    }
                    if (item.desctype === 'questionName') {
                      return (
                        <div
                          key={item.key + item?.height}
                          className="question-name"
                          style={{
                            height: item?.height,
                            fontWeight: 'bold',
                          }}
                        >
                          <span>{item?.num}、</span>
                          <div>{item?.content}</div>
                        </div>
                      );
                    }
                    if (item.desctype === 'subjective') {
                      return (
                        <div
                          className="questionWrap"
                          key={item.key + item?.height}
                        >
                          <span
                            className="questionNum"
                            style={{
                              visibility: item?.maxSplitPage
                                ? 'hidden'
                                : 'visible',
                            }}
                          >
                            {convertSpecialNumber(item.num, t)}.
                          </span>
                          <SubjectiveItem
                            key={item.key + item?.height}
                            data={item}
                            paperSetting={paperSetting}
                            templateLayout={paperLayout}
                            callBack={resizeEditor}
                            editorChange={(t) => {
                              return editorChange(t, item.key, i);
                            }}
                            editorHeightChange={(n) => {
                              return editorHeightChange(n, item.key, i);
                            }}
                          />
                        </div>
                      );
                    }
                    if (item.desctype === 'chineseWriting') {
                      return (
                        <ChineseWriting
                          key={`section-${item.key}-${item?.height}`}
                          data={item}
                          paperData={paperData}
                          templateLayout={paperLayout}
                          callBack={resizeEditor}
                        />
                      );
                    }
                    return (
                      <div
                        key={`section-${item.key}-${item?.height}`}
                        className="questionWrap"
                        // style={{
                        //   minHeight: item?.height,
                        // }}
                      >
                        {item.num && (
                          <span
                            className="questionNum"
                            style={{
                              visibility:
                                item?.maxSplitPage || item.typeName === '写作题'
                                  ? 'hidden'
                                  : 'visible',
                            }}
                          >
                            {convertSpecialNumber(item.num, t)}.
                          </span>
                        )}
                        <TinyMCEEditor
                          id={item.key}
                          content={item?.content}
                          editorChange={(t) => {
                            return editorChange(t, item.key, i);
                          }}
                          editorHeightChange={(n) => {
                            return editorHeightChange(n, item.key, i);
                          }}
                        />
                      </div>
                    );
                  })}
                  <div
                    style={{
                      height: paperSetting?.pageNumberHeight,
                      lineHeight: paperSetting?.pageNumberHeight + 'px',
                      width: '100%',
                      textAlign: 'center',
                      position: 'absolute',
                      bottom: 36,
                      left: 0,
                      zIndex: 99,
                    }}
                  >
                    第{i + 1}页/共{paperSetting?.totalPages}页
                  </div>
                </div>
              );
            })}
          </div>
          <div className={'preload'} ref={preloadRef}></div>
        </div>
      </Spin>

      <PreviewModal
        imgSrcs={imgSrcs}
        imgSize={{
          width: paperSetting?.pageSize?.w,
          height: paperSetting?.pageSize?.h,
        }}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
      />
    </DrawerForm>
  );
};

export default AnswerSheet;
