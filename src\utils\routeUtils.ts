import {
  getAllFeatureCodes,
  getRouteByFeatureCode,
} from '@/constants/routeMapping';

/**
 * 判断是否为父级路由
 * 基于路径层级结构自动判断：如果存在其他路由的路径是当前路由路径的子路径，则当前路由为父级路由
 */
export const isParentRoute = (code: string): boolean => {
  const currentPath = getRouteByFeatureCode(code);
  if (!currentPath) return false;

  const allCodes = getAllFeatureCodes();

  // 检查是否存在其他路由的路径是当前路径的子路径
  return allCodes.some((otherCode) => {
    if (otherCode === code) return false;

    const otherPath = getRouteByFeatureCode(otherCode);
    if (!otherPath) return false;

    // 检查 otherPath 是否是 currentPath 的子路径
    // 例如：/homeworkTemplate/primarySchool 是 /homeworkTemplate 的子路径
    return otherPath.startsWith(currentPath + '/');
  });
};

/**
 * 获取功能编号对应的第一个可访问子路由
 * 如果是父级路由，返回第一个有权限的子路由
 * 如果是子路由，直接返回对应路径
 */
export const getAccessibleRoute = (
  code: string,
  userFeatures: any[],
): string | null => {
  // 如果不是父级路由，直接返回对应路径
  if (!isParentRoute(code)) {
    return getRouteByFeatureCode(code);
  }

  // 是父级路由，查找第一个有权限的子路由
  const userFeatureCodes = userFeatures.map((feature) => feature.code);
  const allCodes = getAllFeatureCodes();

  // 找到所有以当前编号为前缀的子路由
  const childCodes = allCodes
    .filter(
      (childCode) =>
        childCode !== code &&
        childCode.startsWith(code) &&
        childCode.length > code.length,
    )
    .sort((a, b) => a.localeCompare(b));

  // 找到第一个用户有权限的子路由
  for (const childCode of childCodes) {
    if (userFeatureCodes.includes(childCode)) {
      const route = getRouteByFeatureCode(childCode);
      if (route) {
        return route;
      }
    }
  }

  return null;
};

/**
 * 过滤掉有子权限的父级权限
 * 如果用户同时拥有父级权限和子级权限，只保留子级权限
 */
const filterParentCodesWithChildren = (
  userFeatureCodes: string[],
): string[] => {
  return userFeatureCodes.filter((code) => {
    // 检查是否为父级路由
    if (!isParentRoute(code)) {
      return true; // 不是父级路由，保留
    }

    // 是父级路由，检查用户是否有对应的子权限
    // 基于路径层级关系检查，而不是编号前缀
    const currentPath = getRouteByFeatureCode(code);
    if (!currentPath) return true;

    const hasChildPermission = userFeatureCodes.some((otherCode) => {
      if (otherCode === code) return false;

      const otherPath = getRouteByFeatureCode(otherCode);
      if (!otherPath) return false;

      // 检查用户是否有当前路径的子路径权限
      return otherPath.startsWith(currentPath + '/');
    });

    // 如果有子权限，过滤掉父级权限；如果没有子权限，保留父级权限
    return !hasChildPermission;
  });
};

/**
 * 获取用户有权限访问的第一个页面
 * 自动跳过父级路由，返回第一个可访问的子路由
 */
export const getFirstAccessiblePage = (userFeatures: any[]): string | null => {
  let userFeatureCodes = userFeatures.map((feature) => feature.code);

  // 过滤掉有子权限的父级权限
  userFeatureCodes = filterParentCodesWithChildren(userFeatureCodes);

  // 按功能编号排序
  userFeatureCodes.sort((a, b) => a.localeCompare(b));
  console.log('userFeatureCodes', userFeatureCodes);

  // 查找第一个有对应路由的功能
  for (const code of userFeatureCodes) {
    const routePath = getAccessibleRoute(code, userFeatures);
    if (routePath) {
      return routePath;
    }
  }

  // 如果映射表中没有找到，尝试使用功能自带的路径
  const featuresWithPath = userFeatures
    .filter(
      (feature) =>
        feature.path &&
        feature.path !== '' &&
        !feature.path.startsWith('#') &&
        feature.path !== '/home',
    )
    .filter((feature) => userFeatureCodes.includes(feature.code)) // 只保留过滤后的权限
    .sort((a, b) => a.code.localeCompare(b.code));

  if (featuresWithPath.length > 0) {
    return featuresWithPath[0].path;
  }

  return null;
};

/**
 * 检查用户是否有某个功能区域的权限
 * 如果是父级功能，检查是否有任何子功能权限
 */
export const hasFeatureSectionAccess = (
  parentCode: string,
  userFeatures: any[],
): boolean => {
  const userFeatureCodes = userFeatures.map((feature) => feature.code);

  // 如果用户直接有父级权限
  if (userFeatureCodes.includes(parentCode)) {
    return true;
  }

  // 检查是否有任何子功能权限
  const allCodes = getAllFeatureCodes();
  const childCodes = allCodes.filter(
    (code) =>
      code !== parentCode &&
      code.startsWith(parentCode) &&
      code.length > parentCode.length,
  );

  return childCodes.some((childCode) => userFeatureCodes.includes(childCode));
};

/**
 * 获取学段相关的可访问路径
 * 根据权限自动选择合适的学段路径
 */
export const getAvailableSchoolPath = (
  moduleType:
    | 'homeworkTemplate'
    | 'complianceTestTemplate'
    | 'homeworkDesign'
    | 'complianceTestDesign',
  userFeatures: any[],
): string | null => {
  const schoolPermissions = {
    homeworkTemplate: {
      primarySchool: '070100',
      juniorSchool: '070200',
      highSchool: '070300',
    },
    complianceTestTemplate: {
      primarySchool: ['080100', '080200'],
      juniorSchool: ['080300', '080400'],
      highSchool: ['080500', '080600'],
    },
    homeworkDesign: {
      primarySchool: '090100',
      juniorSchool: '090200',
      highSchool: '090300',
    },
    complianceTestDesign: {
      primarySchool: ['100100', '100200'],
      juniorSchool: ['100300', '100400'],
      highSchool: ['100500', '100600'],
    },
  };

  const permissions = schoolPermissions[moduleType];
  if (!permissions) return null;

  const userFeatureCodes = userFeatures.map((feature) => feature.code);

  // 检查权限的辅助函数
  const hasPermission = (codes: string | string[]) => {
    if (Array.isArray(codes)) {
      return codes.some((code) => userFeatureCodes.includes(code));
    }
    return userFeatureCodes.includes(codes);
  };

  // 按优先级检查：小学 -> 初中 -> 高中
  if (hasPermission(permissions.primarySchool)) {
    if (moduleType === 'complianceTestTemplate') {
      return '/complianceTestTemplate/primarySchool/unit';
    } else if (moduleType === 'complianceTestDesign') {
      return '/complianceTestDesign/primarySchoolUnit';
    } else {
      return `/${moduleType}/primarySchool`;
    }
  }

  if (hasPermission(permissions.juniorSchool)) {
    if (moduleType === 'complianceTestTemplate') {
      return '/complianceTestTemplate/juniorSchool/unit';
    } else if (moduleType === 'complianceTestDesign') {
      return '/complianceTestDesign/juniorSchoolUnit';
    } else {
      return `/${moduleType}/juniorSchool`;
    }
  }

  if (hasPermission(permissions.highSchool)) {
    if (moduleType === 'complianceTestTemplate') {
      return '/complianceTestTemplate/highSchool/unit';
    } else if (moduleType === 'complianceTestDesign') {
      return '/complianceTestDesign/highSchoolUnit';
    } else {
      return `/${moduleType}/highSchool`;
    }
  }

  return null;
};
