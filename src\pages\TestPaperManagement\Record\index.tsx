import CommonCard from '@/components/CommonCard';
import ConditionalRender from '@/components/ConditionalRender';
import {
  getComposerTestPaper,
  removeAllComposerTestPaper,
  removeComposerTestPaper,
} from '@/services/composer_test_paper';
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { ProList } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import {
  Breadcrumb,
  Button,
  Divider,
  Empty,
  message,
  PaginationProps,
  Popconfirm,
  Spin,
} from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import PaperPreviewEdit from '../components/PaperPreviewEdit';
import styles from './index.less';

/**
 * 每个单独的卡片，为了复用样式抽成了组件
 * @param param0
 * @returns
 */
const PaperRecordCard = ({
  data,
  index,
  headEvent,
}: {
  data: API.ComposerPaperType;
  index: number;
  isAdmin: boolean;
  headEvent: (type: string, data?: API.ComposerPaperType) => void;
}) => {
  return (
    <div className={`${styles.questionItem} commonWapper`}>
      <div
        className={styles.questionItemContent}
        dangerouslySetInnerHTML={{
          __html:
            '<div class="questionNum">' +
            index +
            '、</div><div class="questionTitle">' +
            data?.name +
            '</div>',
        }}
      />
      <div className={styles.questionItemBottom}>
        <div>
          {/* 创建人：
          <span>{data?.creatorName}</span> */}
        </div>
        <div>
          <span>
            创建：{moment(data?.createdAt).format('YYYY-MM-DD HH:mm')}
          </span>
          <Divider type="vertical" />
          <a
            style={{ color: '#888888' }}
            onClick={() => {
              headEvent?.('detail', data);
            }}
          >
            <EyeOutlined />
            &nbsp; 详情
          </a>

          <>
            <Divider type="vertical" />
            <a
              onClick={() => {
                headEvent?.('edit', data);
              }}
            >
              <EditOutlined style={{ color: 'var(--primary-color)' }} />
              &nbsp; 编辑
            </a>
            <Divider type="vertical" />
            <Popconfirm
              key="deletes"
              title="确认删除当前试卷吗?"
              onConfirm={async () => {
                headEvent?.('delete', data);
              }}
              okText="确定"
              cancelText="取消"
              placement="topRight"
            >
              <span key="delete" className="delete">
                <DeleteOutlined />
                &nbsp; 删除
              </span>
            </Popconfirm>
          </>
        </div>
      </div>
    </div>
  );
};
const PaperRecordList = ({
  isAdmin = false,
}: {
  isAdmin: boolean;
  pagination: PaginationProps;
}) => {
  const { id, type } = useParams();
  const [data, setData] = useState<any[]>([]);
  /** 是否处于预览模式 */
  const [isPreview, setIsPreview] = useState(false);
  const [choosedQuestion, setChoosedQuestion] = useState<any>([]);
  /** 预览卷子 */
  const [onlyPreviewView, setOnlyPreviewView] = useState(false);
  /** 当前选中的试卷 */
  const [currentPaper, setCurrentPaper] = useState<any>({});

  /** 初始化数据 */
  const initData = async () => {
    const { errCode, data, msg } = await getComposerTestPaper({
      composerPaperId: id,
    });
    if (errCode) {
      setData([]);
      return message.warning(`获取组卷列表失败: ${msg}`);
    } else {
      setData(data?.list || []);
    }
  };

  /** 删除试卷 */
  const onDelete = async (id: number) => {
    const { errCode, msg } = await removeComposerTestPaper(id);
    if (errCode) {
      return message.warning(`删除试卷失败: ${msg}`);
    }
    message.success('删除试卷成功');
    initData();
  };

  /** 删除全部试卷 */
  const onDeleteAll = async () => {
    if (!id) {
      return message.warning('清空方案下所有试卷失败, 组卷方案id不能为空');
    }
    const { errCode, msg } = await removeAllComposerTestPaper(Number(id));
    if (errCode) {
      return message.warning(`清空方案下所有试卷失败: ${msg}`);
    }
    message.success('清空方案下所有试卷成功');
    initData();
  };

  useEffect(() => {
    initData();
  }, []);

  const headEvent = (type: string, data?: any) => {
    setCurrentPaper(data);
    switch (type) {
      case 'detail':
        setOnlyPreviewView(true);
        setChoosedQuestion(data ?? []);
        setIsPreview(true);
        break;
      case 'edit':
        setOnlyPreviewView(false);
        setChoosedQuestion(data ?? []);
        setIsPreview(true);
        break;
      case 'delete':
        onDelete(data?.id);
        break;
      default:
        break;
    }
  };

  return (
    <>
      <ConditionalRender
        hasAccess={isPreview}
        accessComponent={
          <>
            <PaperPreviewEdit
              homeworkId={choosedQuestion?.id}
              questionInfo={currentPaper}
              onlyPreviewView={onlyPreviewView}
              newQuestionData={choosedQuestion?.details}
              onClick={() => {
                setIsPreview(false);
                initData();
              }}
              onSave={() => {
                setIsPreview(false);
                initData();
              }}
            />
          </>
        }
        noAccessComponent={
          <>
            <Breadcrumb
              style={{
                marginBottom: '16px',
                cursor: 'pointer',
              }}
              items={[
                {
                  title:
                    type === 'auto' ? '自动组卷方案列表' : '手工组卷方案列表',
                  onClick: () => {
                    history.push(
                      type === 'auto'
                        ? '/testPaperManagement/auto'
                        : '/testPaperManagement/manual',
                    );
                  },
                },
                {
                  title: type === 'auto' ? '自动组卷列表' : '手工组卷列表',
                },
              ]}
            />
            <CommonCard
              title={type === 'auto' ? '自动组卷列表' : '手工组卷列表'}
            >
              <Popconfirm
                title="删除提示"
                description="确认要清空当前方案下的所有试卷吗吗？"
                onConfirm={onDeleteAll}
                okText="确定"
                cancelText="取消"
              >
                <Button danger disabled={data.length <= 0}>
                  清除全部试卷
                </Button>
              </Popconfirm>
            </CommonCard>
            <Spin spinning={false}>
              <div className={styles.questionList}>
                <ConditionalRender
                  hasAccess={data?.length !== 0}
                  accessComponent={
                    <>
                      <ProList<any>
                        rowKey="id"
                        dataSource={data}
                        pagination={{
                          pageSize: 10,
                          align: 'end',
                        }}
                        renderItem={(item: any, index: number) => (
                          <PaperRecordCard
                            key={item.id}
                            data={item}
                            index={index + 1}
                            headEvent={headEvent}
                            isAdmin={isAdmin}
                          />
                        )}
                      />
                    </>
                  }
                  noAccessComponent={
                    <div className={styles.noPaper}>
                      <Empty
                        className="commonPositionCenter"
                        description="暂无组卷方案，请先创建方案"
                      />
                    </div>
                  }
                />
              </div>
            </Spin>
          </>
        }
      />
    </>
  );
};

export default PaperRecordList;
