.statistics {
  .filter {
    line-height: 60px;
    background-color: #fff;
    padding: 0 16px;
    border-radius: 8px;

    .filterTitle {
      margin-right: 20px;
      font-weight: bold;
    }
  }

  :global {
    .ant-statistic .ant-statistic-content .ant-statistic-content-suffix {
      font-size: 14px;
      color: rgba(0, 0, 0, 45%);
    }

    .ant-card .ant-card-body {
      border: 16px !important;
    }

    .ant-card .ant-card-head {
      border: none !important;
    }
  }

  .cardTitleValue {
    margin-left: 10px;

    .percentageChangeValue {
      margin: 0 5px;
      font-weight: 500;

      .desc {
        color: rgba(0, 0, 0, 45%);
      }
    }
  }

  .schoolTrend {
    display: flex;
    margin-top: 15px;
    justify-content: space-between;

    .schoolTrendValue {
      font-size: 12px;

      .schoolTrendValueNum {
        margin-right: 5px;
        .desc {
          margin-left: 10px;
          color: rgba(0, 0, 0, 45%);
        }
      }
    }
  }
}
