import { request } from '@umijs/max';

/** 试题导入  POST /common_question/import */
export async function QuestionImport(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>('/common_question/import', {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: body,
  });
}
/** 课时作业范本导入  POST /class_work_detail/import */
export async function kszyImport(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>('/class_work_detail/import', {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: body,
  });
}
/** 达标检测范本导入  POST /compliance_detection_template_detail/import */
export async function dbjcImport(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>(
    '/compliance_detection_template_detail/import',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: body,
    },
  );
}
/** 课时作业范本设计导入  POST /lesson_work_design_detail/import */
export async function kszysjImport(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>('/lesson_work_design_detail/import', {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: body,
  });
}
/** 达标检测范本设计导入  POST /compliance_detection_design_detail/import */
export async function dbjcsjImport(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>(
    '/compliance_detection_design_detail/import',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: body,
    },
  );
}
