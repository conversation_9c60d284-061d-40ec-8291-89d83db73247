import { DictionarieState } from '@/models/dictionarie';
import {
  ModalForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  dictionarie,
}) => {
  const [systemOptions, setSystemOptions] = React.useState<any[]>([]);
  const [typeOptions, setTypeOptions] = React.useState<any[]>([]);

  useEffect(() => {
    const typeData = dictionarie.list
      .filter((item) => item.type === 'course_duration_type')
      .map((item) => ({ label: item.name, value: item.name }));
    const systemData = dictionarie.list
      .filter((item) => item.type === 'course_duration')
      .map((item) => ({ label: item.name, value: item.name }));
    setSystemOptions(systemData);
    setTypeOptions(typeData);
  }, []);

  return (
    <ModalForm<any>
      width={560}
      title={info ? '编辑学制' : '新增学制'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormSelect
        name="system"
        label="学制"
        options={systemOptions}
        rules={[{ required: true, message: '请输入学制！' }]}
      />
      <ProFormSelect
        name="type"
        label="类别"
        options={typeOptions}
        rules={[{ required: true, message: '请输入类别！' }]}
      />
      <ProFormText
        fieldProps={{ maxLength: 100 }}
        name="describe"
        label="描述"
      />
    </ModalForm>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(EditModal);
