import ConditionalRender from '@/components/ConditionalRender';
import GuidanceModalButton from '@/components/GuidanceModleBotton.tsx';
import HomeworkDesign from '@/components/HomeworkDesign';
import { getQueryParams } from '@/utils/calc';
import { Button, Drawer, Empty, Flex, Space, Tabs, message } from 'antd';
import { nanoid } from 'nanoid';
import { useEffect, useState } from 'react';
import styles from './index.less';
import QuestionBankList from './QuestionBankList';

const tabArr = [
  {
    key: 'system',
    label: '系统题库',
  },
];
const AddQuestion = ({
  questionModal,
  callback,
}: {
  questionModal: any;
  callback: (homeworkId?: string) => void;
}) => {
  const { isSysAdmin } = getQueryParams();
  const { open, strucData, setStrucData, questionData, setQuestionData } =
    questionModal;
  const [choosedQuestion, setChoosedQuestion] = useState<API.SystemQuestion[]>(
    [],
  );
  const [choosedStruc, setChoosedStruc] = useState<any>();
  useEffect(() => {
    if (isSysAdmin !== 'true') {
      tabArr.push(
        {
          key: 'school',
          label: '学校题库',
        },
        {
          key: 'personal',
          label: '我的题库',
        },
      );
    }
  }, [isSysAdmin]);
  /**
   * 选择事件处理函数
   *
   * @param value 事件携带的数据
   * @param status 事件的状态
   */
  const choosedEvent = (value: API.SystemQuestion, status: boolean) => {
    if (status) {
      setChoosedQuestion((preData: any) => [...preData, value]);
      setChoosedStruc((prevData: any) => {
        const pId = prevData?.bigQuestionOrder?.[0];
        const current = prevData?.bigQuestions?.[pId];
        const newQuestionIds: any = Array.from(current.questionIds);
        newQuestionIds.push(value._id);
        const newBigQuestion = {
          ...current,
          questionIds: newQuestionIds,
        };
        return {
          ...prevData,
          bigQuestions: {
            ...prevData.bigQuestions,
            [newBigQuestion.id]: newBigQuestion,
          },
        };
      });
    } else {
      setChoosedQuestion((prevData: any[]) => {
        return prevData.filter((item) => item._id !== value._id);
      });
      setChoosedStruc((prevData: any) => {
        // 使用新的删除方法
        const newData = { ...prevData };
        let foundBigQuestionId: string | null = null;
        // 1. 找到并删除questionId
        for (const bigQuestionId of newData.bigQuestionOrder) {
          const bigQuestion = newData.bigQuestions[bigQuestionId];
          const index = bigQuestion.questionIds.indexOf(value._id);
          if (index !== -1) {
            bigQuestion.questionIds.splice(index, 1);
            foundBigQuestionId = bigQuestionId;
            break;
          }
        }
        // 2. 检查并删除空的大题
        if (foundBigQuestionId) {
          const bigQuestion = newData.bigQuestions[foundBigQuestionId];
          if (
            bigQuestion.questionIds.length === 0 &&
            foundBigQuestionId.indexOf('hidden') === -1
          ) {
            // 保留hidden分组
            // 从order数组中删除
            newData.bigQuestionOrder = newData.bigQuestionOrder.filter(
              (id: string) => id !== foundBigQuestionId,
            );
            // 从bigQuestions对象中删除
            delete newData.bigQuestions[foundBigQuestionId];
          }
        }

        return newData;
      });
    }
  };
  /**
   * 添加试题函数
   *
   * @returns 无返回值
   */
  const addQuestions = async () => {
    if (choosedQuestion.length === 0) {
      message.warning('请选择试题');
      return;
    }
    try {
      setQuestionData(choosedQuestion);
      setStrucData(choosedStruc);
      callback();
    } catch (error) {}
  };
  /**
   * 重置事件
   *
   * 重置选择的问题和问题结构
   */
  const resetEvent = () => {
    setChoosedQuestion([]);
    let newData: any = {};
    const hideId = 'hidden_' + nanoid();
    newData = {
      bigQuestionOrder: [hideId],
      bigQuestions: {},
    };
    newData.bigQuestions[hideId] = {
      id: hideId,
      name: '暂无分组',
      questionIds: [],
    };
    setChoosedStruc(newData);
  };

  useEffect(() => {
    if (strucData) {
      setChoosedStruc(strucData);
    } else {
      let newData: any = {};
      const hideId = 'hidden_' + nanoid();
      newData = {
        bigQuestionOrder: [hideId],
        bigQuestions: {},
      };
      newData.bigQuestions[hideId] = {
        id: hideId,
        name: '暂无分组',
        questionIds: [],
      };
      setChoosedStruc(newData);
    }
    setChoosedQuestion(questionData);
  }, [questionData, strucData]);

  return (
    <Drawer
      title="选择试题"
      width="100vw"
      placement="left"
      closable={false}
      className={styles.customQuestionDrawer}
      // onClose={onClose}
      destroyOnClose={true}
      open={open}
    >
      <GuidanceModalButton />
      <Flex gap="middle" align="center" justify="space-between">
        <div className={styles.leftWrapper}>
          <header>
            <Flex align="center" justify="space-between">
              <div>已选试题</div>
              <Space>
                <Button color="default" variant="outlined" onClick={resetEvent}>
                  清除全部
                </Button>
                <Button color="primary" variant="solid" onClick={addQuestions}>
                  确认添加
                </Button>
              </Space>
            </Flex>
          </header>
          <div className={styles.selectedQuestion}>
            <ConditionalRender
              hasAccess={choosedQuestion?.length > 0}
              accessComponent={
                <HomeworkDesign
                  type="edit"
                  questionConstrue={choosedStruc}
                  questionList={choosedQuestion}
                  sortQuestion={setChoosedQuestion}
                />
              }
              noAccessComponent={
                <Empty
                  className="commonPositionCenter"
                  description="请将右侧试题加入试卷预览"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              }
            />
          </div>
        </div>
        <div className={styles.rightWrapper}>
          <Tabs
            defaultActiveKey="system"
            centered
            items={tabArr.map((item) => {
              const { label, key } = item;
              return {
                label,
                key,
                children: (
                  <QuestionBankList
                    type={key}
                    choosedQuestion={choosedQuestion}
                    choosedEvent={choosedEvent}
                  />
                ),
              };
            })}
          />
        </div>
      </Flex>
    </Drawer>
  );
};

export default AddQuestion;
