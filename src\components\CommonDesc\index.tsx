import { noData } from '@/assets';
import classNames from 'classnames';
import React from 'react';
import ConditionalRender from '../ConditionalRender';
import styles from './index.less';

interface CommonDescProps {
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 标题 */
  title?: React.ReactNode;
  /** 描述 */
  desc?: string;
  /** 图片 */
  icon?: string;
  /** 操作 */
  extra?: React.ReactNode;
}

const CommonDesc: React.FC<CommonDescProps> = ({
  className,
  style,
  title,
  desc,
  extra,
  icon = noData,
}) => {
  return (
    <>
      <div className={classNames(styles.commonDesc, className)} style={style}>
        <img src={icon} className={styles.icon} alt="" />
        <ConditionalRender
          hasAccess={!!title}
          accessComponent={<h2 className={styles.title}>{title}</h2>}
        />
        <ConditionalRender
          hasAccess={!!desc}
          accessComponent={<div className={styles.subtitle}>{desc}</div>}
        />
        <ConditionalRender
          hasAccess={!!extra}
          accessComponent={<div className={styles.extra}>{extra}</div>}
        />
      </div>
    </>
  );
};
export default CommonDesc;
