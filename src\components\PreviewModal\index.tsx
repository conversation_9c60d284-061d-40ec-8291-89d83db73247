import { But<PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';

const PreviewModal = ({
  imgSrcs,
  imgSize,
  isOpen,
  setIsOpen,
}: {
  imgSrcs: any[];
  imgSize: {
    width: any;
    height: any;
  };
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}) => {
  const { width } = imgSize;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
    }
  }, [isOpen]);
  useEffect(() => {
    if (imgSrcs?.length && isLoading) {
      setTimeout(() => setIsLoading(false), 500);
    }
  }, [imgSrcs, isLoading]);
  return (
    <Modal
      title="内容预览"
      width={width + 100}
      styles={{
        body: {
          padding: 0,
          height: '70vh',
          overflowY: 'auto',
          textAlign: 'center',
        },
      }}
      destroyOnClose={true}
      maskClosable={false}
      footer={[
        <Button
          key="submit"
          type="primary"
          onClick={() => {
            setIsOpen(false);
          }}
        >
          确定
        </Button>,
      ]}
      open={isOpen}
      onOk={() => {
        setIsOpen(false);
      }}
      onCancel={() => {
        setIsOpen(false);
      }}
      onClose={() => {
        setIsOpen(false);
      }}
    >
      <Spin tip="内容加载中" size="large" spinning={isLoading}>
        <div
          style={{
            minHeight: '200px',
            width: width,
            margin: '0 auto',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
          }}
        >
          {imgSrcs?.map((v) => {
            return (
              <img
                src={v.src}
                style={{
                  width: '100%',
                }}
                alt=""
                key={v.title}
              />
            );
          })}
        </div>
      </Spin>
    </Modal>
  );
};

export default PreviewModal;
