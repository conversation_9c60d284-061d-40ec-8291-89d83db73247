import {
  ModalForm,
  ProFormDatePicker,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormSegmented,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Semester;
  onSave: (info: API.Semester) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<API.Semester & { date?: string }>
      title={info ? '编辑学年学期' : '注册学年学期'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      width={420}
      labelCol={{ flex: '7em' }}
      onFinish={async (values) => {
        delete values.date;
        onSave(values);
      }}
      initialValues={
        info
          ? {
              ...info,
              term: String(info.term),
              status: String(info.status),
              date: [info.start_date, info.end_date],
            }
          : { term: '1', status: '0' }
      }
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText name="code" label="编号" readonly />
      <ProFormSelect
        name="year"
        label="学年"
        options={Array.from({ length: 8 }, (_, i) => {
          // 自动生成从5年前到2年后的年份
          const startYear = (new Date().getFullYear() - 5 + i).toString();
          const endYear = (new Date().getFullYear() - 4 + i).toString();
          return {
            label: `${startYear}-${endYear}`,
            value: `${startYear}-${endYear}`,
          };
        })}
        rules={[{ required: true, message: '请选择学年！' }]}
        // colProps={{ span: 16 }}
      />
      <ProFormSegmented
        name="term"
        label="学期"
        valueEnum={{
          1: '第一学期',
          2: '第二学期',
        }}
        rules={[{ required: true, message: '请选择学期！' }]}
      />
      <ProFormDateRangePicker
        name="date"
        label="起止日期"
        rules={[{ required: true, message: '请选择起止日期！' }]}
      />
      <ProFormDatePicker
        name="start_date"
        label="开始日期"
        colProps={{ span: 12 }}
        readonly
        hidden
      />
      <ProFormDatePicker
        name="end_date"
        label="结束日期"
        colProps={{ span: 12 }}
        readonly
        hidden
      />
      <ProFormSegmented
        name="status"
        label="状态"
        valueEnum={{
          0: '禁用',
          1: '启用',
        }}
      />
      <ProFormDependency name={['year', 'term', 'date']}>
        {({ year, term, date }, form) => {
          // 处理联动
          if (year && term) {
            // 根据年份生成编号
            form.setFieldsValue({
              code: `${year.replace(/-/g, '')}0${term}`,
            });
          }
          if (date) {
            // 根据起止日期生成对应字段
            form.setFieldsValue({
              start_date: date[0],
              end_date: date[1],
            });
          }
          return null;
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default EditModal;
