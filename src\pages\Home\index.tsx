import { COMMON_FEATURES, FEATURE_SECTIONS } from '@/constants/homePageConfig';
import { FeatureState } from '@/models/feature';
import { RoleState } from '@/models/role';
import { index as getDashboardOverview } from '@/services/dashboard';
import { logout } from '@/utils/auth';
import {
  getAccessibleRoute,
  getAvailableSchoolPath,
  getFirstAccessiblePage,
  hasFeatureSectionAccess,
} from '@/utils/routeUtils';
import {
  AppstoreOutlined,
  BankOutlined,
  BulbOutlined,
  DatabaseOutlined,
  LogoutOutlined,
  SafetyOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import {
  AnyAction,
  connect,
  Dispatch,
  history,
  useAccess,
  useModel,
} from '@umijs/max';
import {
  Avatar,
  Col,
  Dropdown,
  Layout,
  message,
  Row,
  Statistic,
  Typography,
} from 'antd';

import { useEffect, useState } from 'react';
import styles from './index.less';

const { Header, Content } = Layout;
const { Title } = Typography;

const HomePage: React.FC<{
  feature: FeatureState;
  role: RoleState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ feature, role, loading }) => {
  const { initialState } = useModel('@@initialState');
  const access = useAccess();

  // 状态管理
  const [enterpriseCount, setEnterpriseCount] = useState<number>(0);
  const [userCount, setUserCount] = useState<number>(0);
  const [dataLoading, setDataLoading] = useState<boolean>(true);

  // 获取统计数据
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setDataLoading(true);

        // 调用dashboard概览接口获取统计数据
        const response = await getDashboardOverview({});

        if (!response.errCode && response.data) {
          // 根据接口返回的数据结构设置状态
          if (response.data.enterprise_count !== undefined) {
            setEnterpriseCount(response.data.enterprise_count);
          }
          if (response.data.user_count !== undefined) {
            setUserCount(response.data.user_count);
          }
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setDataLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  // 获取用户有权限访问的第一个页面
  const getFirstPage = () => {
    const userFeatures = initialState?.features || [];
    return getFirstAccessiblePage(userFeatures);
  };

  return (
    <Layout className={styles.container}>
      <Header className={styles.top}>
        <div className={styles.logo}>
          <img src="/homework-design/logo.png" alt="logo" height="100%" />
          <span>题库设计系统</span>
        </div>
        <div className={styles.userInfo}>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'logout',
                  icon: <LogoutOutlined />,
                  label: '退出登录',
                  onClick: logout,
                },
              ],
            }}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
                cursor: 'pointer',
              }}
            >
              <Avatar
                src={
                  initialState?.avatar ||
                  'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg'
                }
                size="default"
              />
              <div>{initialState?.nickname || initialState?.username}</div>
            </div>
          </Dropdown>
        </div>
      </Header>
      <div className={styles.banner}>
        <div className={styles.bannerContent}>
          <div className={styles.bannerLeft}>
            <h2>
              <span className={styles.titleMain}>智慧作业设计系统</span>
              <span className={styles.titleSub}>
                Intelligent Homework Design System
              </span>
            </h2>
            <p>专业的教育管理平台，助力教学质量提升</p>
            <div className={styles.bannerFeatures}>
              <span>
                <DatabaseOutlined className={styles.featureIcon} />
                一站式题库管理
              </span>
              <span>
                <BulbOutlined className={styles.featureIcon} />
                智能作业设计
              </span>
              <span>
                <SafetyOutlined className={styles.featureIcon} />
                数据安全保障
              </span>
            </div>
          </div>
          <div className={styles.bannerRight}>
            <img
              src="https://images.unsplash.com/photo-*************-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=85"
              alt="在线教育"
              className={styles.bannerMainImage}
            />
          </div>
        </div>
      </div>
      <Content className={styles.content}>
        <Row gutter={16} className={styles.statsRow}>
          <Col span={6}>
            <ProCard loading={dataLoading}>
              <div className={styles.statisticCard}>
                <BankOutlined
                  className={styles.statisticIcon}
                  style={{ color: '#1890ff', fontSize: '42px' }}
                />
                <Statistic title="入驻单位" value={enterpriseCount} />
              </div>
            </ProCard>
          </Col>
          <Col span={6}>
            <ProCard loading={dataLoading}>
              <div className={styles.statisticCard}>
                <UserOutlined
                  className={styles.statisticIcon}
                  style={{ color: '#52c41a', fontSize: '42px' }}
                />
                <Statistic title="用户数量" value={userCount} />
              </div>
            </ProCard>
          </Col>
          <Col span={6}>
            <ProCard loading={loading.models.role}>
              <div className={styles.statisticCard}>
                <TeamOutlined
                  className={styles.statisticIcon}
                  style={{ color: '#fa8c16', fontSize: '42px' }}
                />
                <Statistic title="角色数量" value={role.list.length} />
              </div>
            </ProCard>
          </Col>
          <Col span={6}>
            <ProCard loading={loading.models.feature}>
              <div className={styles.statisticCard}>
                <AppstoreOutlined
                  className={styles.statisticIcon}
                  style={{ color: '#722ed1', fontSize: '42px' }}
                />
                <Statistic title="系统功能数量" value={feature.list.length} />
              </div>
            </ProCard>
          </Col>
        </Row>
        <Title level={2} className={styles.title}>
          常用功能
        </Title>
        {(() => {
          // 过滤出有权限的常用功能卡片
          const accessibleCommonFeatures = COMMON_FEATURES.filter(
            (cardConfig) => access[cardConfig.code],
          );

          // 根据是否有常用功能卡片来调整布局
          const hasCommonFeatures = accessibleCommonFeatures.length > 0;

          return (
            <Row
              gutter={[24, 24]}
              className={styles.featuresRow}
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                padding: '32px',
                borderRadius: '16px',
                boxShadow: '0 4px 20px rgba(24, 144, 255, 0.08)',
                border: '1px solid rgba(24, 144, 255, 0.1)',
              }}
            >
              {/* 动态渲染常用功能卡片 */}
              {accessibleCommonFeatures.map((cardConfig) => {
                const {
                  code,
                  name,
                  icon: IconComponent,
                  color,
                  title,
                  span = 4,
                } = cardConfig;

                const handleClick = () => {
                  let targetPath: string | null = null;

                  // 特殊处理需要动态路由的功能
                  if (['070000', '080000', '090000', '100000'].includes(code)) {
                    const moduleTypeMap: Record<string, any> = {
                      '070000': 'homeworkTemplate',
                      '080000': 'complianceTestTemplate',
                      '090000': 'homeworkDesign',
                      '100000': 'complianceTestDesign',
                    };

                    const moduleType = moduleTypeMap[code];
                    if (moduleType) {
                      targetPath = getAvailableSchoolPath(
                        moduleType,
                        initialState?.features || [],
                      );
                    }
                  } else {
                    // 使用通用的路由获取逻辑
                    targetPath = getAccessibleRoute(
                      code,
                      initialState?.features || [],
                    );
                  }

                  if (targetPath) {
                    history.push(targetPath);
                  }
                };

                return (
                  <Col key={code} span={span}>
                    <div
                      className={styles.nonCardFeatureItem}
                      onClick={handleClick}
                      title={title || name}
                    >
                      <IconComponent
                        className={styles.featureIcon}
                        style={{ color }}
                      />
                      <div className={styles.featureName}>{name}</div>
                    </div>
                  </Col>
                );
              })}

              {/* 进入系统按钮卡片 - 根据是否有常用功能调整样式 */}
              <Col span={hasCommonFeatures ? 4 : 24}>
                <div
                  className={
                    hasCommonFeatures
                      ? styles.nonCardFeatureItem
                      : styles.featureItem
                  }
                  onClick={() => {
                    const firstPage = getFirstPage();
                    if (firstPage) {
                      history.push(firstPage);
                    } else {
                      // 如果没有任何权限，显示友好的提示信息
                      message.warning(
                        '暂无可访问的功能权限，请联系管理员分配权限',
                      );
                    }
                  }}
                  title="进入系统使用所有功能"
                  style={
                    hasCommonFeatures
                      ? {}
                      : {
                          textAlign: 'center',
                          padding: '40px 20px',
                          fontSize: '16px',
                          fontWeight: 'bold',
                        }
                  }
                >
                  <AppstoreOutlined
                    className={styles.featureIcon}
                    style={{
                      color: '#52c41a',
                      fontSize: hasCommonFeatures ? '40px' : '48px',
                    }}
                  />
                  <div
                    className={
                      hasCommonFeatures
                        ? styles.enterSystemName
                        : styles.featureName
                    }
                  >
                    进入系统
                  </div>
                  {hasCommonFeatures && (
                    <div className={styles.enterSystemDesc}>所有功能</div>
                  )}
                  {!hasCommonFeatures && (
                    <div
                      style={{
                        marginTop: '8px',
                        fontSize: '14px',
                        color: '#666',
                      }}
                    >
                      点击进入系统使用所有功能
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          );
        })()}
        {/* 动态渲染功能区域 */}
        {FEATURE_SECTIONS.map((sectionConfig) => {
          const {
            title,
            parentCode,
            cards,
            cardStyle = 'normal',
            gutter = [16, 16],
          } = sectionConfig;

          // 检查是否有权限访问这个功能区域
          const hasAccess = parentCode
            ? hasFeatureSectionAccess(parentCode, initialState?.features || [])
            : true;

          if (!hasAccess) {
            return null;
          }

          // 过滤出有权限的卡片
          const accessibleCards = cards.filter(
            (cardConfig) => access[cardConfig.code],
          );

          // 如果没有可显示的卡片，隐藏整个区域
          if (accessibleCards.length === 0) {
            return null;
          }

          return (
            <div key={title}>
              <Title level={2} className={styles.title}>
                {title}
              </Title>
              <Row gutter={gutter} className={styles.featuresRow}>
                {accessibleCards.map((cardConfig) => {
                  const {
                    code,
                    name,
                    icon: IconComponent,
                    color,
                    title: cardTitle,
                    span = 6,
                  } = cardConfig;

                  const handleClick = () => {
                    const targetPath = getAccessibleRoute(
                      code,
                      initialState?.features || [],
                    );
                    if (targetPath) {
                      history.push(targetPath);
                    }
                  };

                  const cardClassName =
                    cardStyle === 'large'
                      ? styles.featureItem
                      : styles.nonCardFeatureItem;

                  return (
                    <Col key={code} span={span}>
                      <div
                        className={cardClassName}
                        onClick={handleClick}
                        title={cardTitle || name}
                      >
                        <IconComponent
                          className={styles.featureIcon}
                          style={{ color }}
                        />
                        <div className={styles.featureName}>{name}</div>
                      </div>
                    </Col>
                  );
                })}
              </Row>
            </div>
          );
        })}
      </Content>
    </Layout>
  );
};

export default connect(({ feature, role, loading }) => ({
  feature,
  role,
  loading,
}))(HomePage);
