import { styled } from '@umijs/max';
import { Row, Tree } from 'antd';

export const RowStyled = styled(Row)`
  width: 100%;
  margin-bottom: 16px;
  .ant-col {
    display: flex;
    align-items: center;
    > label {
      flex: 0 0 6em;
      text-align: right;
      &::after {
        content: ':';
        position: relative;
        margin-block: 0;
        margin-inline-start: 2px;
        margin-inline-end: 8px;
      }
    }
  }
`;

export const TreeStyled = styled(Tree)`
  .ant-tree-treenode {
    .ant-tree-node-content-wrapper {
      .ant-tree-title {
        .options {
          display: none;
        }
      }
      &.ant-tree-node-selected,
      &:hover {
        .ant-tree-title {
          .options {
            display: flex;
            justify-content: space-around;
            width: 2em;
          }
        }
      }
    }
  }
`;
