import { Descriptions, DescriptionsProps, Modal, Tag } from 'antd';
import React from 'react';
import styles from './common.less';
import TopicContent from './TopicContent';

interface LookQuestionModalProps {
  open: boolean;
  info: API.SystemQuestion;
  onCancel: () => void;
}

const LookQuestionModal: React.FC<LookQuestionModalProps> = ({
  open,
  info,
  onCancel,
}) => {
  const {
    type,
    difficulty,
    year,
    points,
    tier,
    cognitiveHierarchy,
    coreQuality,
    investigationAbility,
  } = info || {};

  const items: DescriptionsProps['items'] = [
    {
      key: '1',
      label: '题型',
      children: type?.name ?? '-',
    },
    {
      key: '2',
      label: '难度',
      children: difficulty?.name ?? '-',
    },
    {
      key: '3',
      label: '所属年份',
      children: year ?? '-',
    },
    {
      key: '4',
      label: '题目分层',
      children: tier?.name ?? '-',
    },
    {
      key: '5',
      label: '认知层级',
      children: cognitiveHierarchy?.name ?? '-',
    },
    {
      key: '6',
      label: '核心素养',
      children: coreQuality?.name ?? '-',
    },
    {
      key: '7',
      label: '考察能力',
      children: investigationAbility?.name ?? '-',
    },
    {
      key: '8',
      label: '知识点',
      children: (
        <div>
          {(points ?? []).map((item, index) => (
            <Tag
              key={index}
              style={{
                marginBottom: 10,
              }}
            >
              {item.name || '-'}
            </Tag>
          ))}
        </div>
      ),
    },
  ];

  return (
    <Modal
      width={800}
      open={open}
      onCancel={onCancel}
      footer={null}
      className={styles.lookQuestionModal}
    >
      <Descriptions title="试题详情" items={items} />
      <div className={styles.topicCardContent}>
        <TopicContent
          showDeleteIcon={false}
          className={styles.topicContent}
          info={info}
          allShowAnalysis
        />
      </div>
    </Modal>
  );
};
export default LookQuestionModal;
