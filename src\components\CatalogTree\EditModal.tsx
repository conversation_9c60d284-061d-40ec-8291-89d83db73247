import { ModalForm, ProFormText } from '@ant-design/pro-components';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.TextbookCatalog;
  onSave: (info: API.TextbookCatalog) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<API.TextbookCatalog>
      title={info ? '编辑目录' : '添加目录'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      isKeyPressSubmit
      open={open}
      layout="horizontal"
      grid
      width={400}
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText
        name="title"
        label="名称"
        initialValue={info?.title || ''}
        rules={[{ required: true, message: '请输入名称！' }]}
      />
      <ProFormText name="id" label="ID" hidden />
    </ModalForm>
  );
};

export default EditModal;
