import CommmonGradeSubject from '@/components/CommmonGradeSubject';
import {
  ModalForm,
  ProFormDigit,
  ProFormText,
} from '@ant-design/pro-components';
import { FormInstance } from 'antd/es/form';
import React, { useEffect } from 'react';

interface DurationModalProps {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
  form: FormInstance<any>;
}

const DurationModal: React.FC<DurationModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  form,
}) => {
  useEffect(() => {
    if (info && open) {
      form.setFieldsValue(info);
    }
  }, [info, open]);
  return (
    <>
      <ModalForm<any>
        width={560}
        title={info ? '编辑学科作业时长' : '新增学科作业时长'}
        autoFocusFirstInput
        form={form}
        modalProps={{
          destroyOnClose: true,
          onCancel: onClose,
          styles: {
            body: {
              marginTop: '20px',
            },
          },
        }}
        open={open}
        layout="horizontal"
        grid
        labelCol={{ flex: '6em' }}
        onFinish={onSave}
      >
        <ProFormText name="id" label="ID" hidden />
        <CommmonGradeSubject form={form} showGrade />
        <ProFormDigit
          label="最小时长"
          name="min_duration"
          min={0}
          fieldProps={{ precision: 0, suffix: '分钟' }}
          rules={[{ required: true, message: '请输入最小时长！' }]}
        />
        <ProFormDigit
          label="最大时长"
          name="max_duration"
          min={0}
          fieldProps={{ precision: 0, suffix: '分钟' }}
          rules={[{ required: true, message: '请输入最大时长！' }]}
        />
      </ModalForm>
    </>
  );
};
export default DurationModal;
