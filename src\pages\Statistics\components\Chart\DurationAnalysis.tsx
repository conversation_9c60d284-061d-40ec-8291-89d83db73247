import {
  getQuestionWork,
  getTeacherWorkStatistic,
} from '@/services/statistics';
import { useModel } from '@umijs/max';
import { Card, Col, message, Row } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import ColumnChart from '../BaseChart/Column';
import FilterSelect from '../BaseChart/FilterSelect';
import Ranking from '../BaseChart/Ranking';
import StatisticsContext from '../Context';
import styles from './styles/index.less';

interface DurationAnalysisProps {
  span: number;
  type: 'city' | 'county' | 'school';
  title: string;
}

const DurationAnalysis: React.FC<DurationAnalysisProps> = ({
  span,
  type,
  title,
}) => {
  const { initialState } = useModel('@@initialState');
  const [data, setData] = useState<any>([]);
  const [filterInfo, setFilterInfo] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const { info, options } = useContext(StatisticsContext);
  const { yearTerm, county, schoolType } = info || {};

  const buildParams = () => {
    const baseParams = {
      semester_code: yearTerm,
      grade_section_code: schoolType,
      grade_code: filterInfo?.grade,
      subject_id: filterInfo?.subject,
    };

    switch (type) {
      case 'city':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county,
        };
      case 'county':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county || initialState?.enterprise?.area,
        };
      case 'school':
        return {
          ...baseParams,
          enterprise_code: initialState?.enterprise?.code,
        };
      default:
        return baseParams;
    }
  };

  const getLabel = (type: 'city' | 'county' | 'school', item: any) => {
    switch (type) {
      case 'city':
        return item?.area_name?.includes('市辖区')
          ? item?.city_name + item?.area_name
          : item?.area_name;
      case 'county':
        return item?.enterprise_name;
      case 'school':
        return item?.teacher_name;
    }
  };

  const getInitData = async () => {
    setLoading(true);
    if (!yearTerm) return;
    const params = buildParams();
    const res = await (type === 'school'
      ? getTeacherWorkStatistic
      : getQuestionWork)(params);
    if (res?.errCode) {
      setLoading(false);
      message.warning(`获取数据失败，请稍后重试 ${res?.msg}`);
      return;
    }
    setLoading(false);

    const newDatta = res?.data?.map(
      (
        item: {
          city_name: string;
          area_name: string;
          total_average_time: string;
        },
        index: number,
      ) => {
        return {
          ...item,
          index: index + 1,
          label: getLabel(type, item),
          value: parseFloat(item.total_average_time),
        };
      },
    );

    setData(newDatta || []);
  };

  useEffect(() => {
    if (type) getInitData();
  }, [type, yearTerm, county, schoolType, filterInfo]);

  return (
    <Col span={span}>
      <Card
        className={styles.card}
        title={title}
        extra={
          <FilterSelect
            mode="grade"
            gradeSection={schoolType}
            gradeOption={options}
            onChange={(value) => {
              setFilterInfo(value);
            }}
          />
        }
      >
        <Row gutter={[0, 12]}>
          <Col span={15}>
            <ColumnChart loading={loading} data={data} />
          </Col>
          <Col span={9}>
            <Ranking
              type={type}
              loading={loading}
              data={data}
              shouSuggest={!!filterInfo?.grade}
            />
          </Col>
        </Row>
      </Card>
    </Col>
  );
};
export default DurationAnalysis;
