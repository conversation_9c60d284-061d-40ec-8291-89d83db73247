export default (
  initialState: API.User | undefined,
): {
  isLogin: boolean;
  featuresList: Record<string, Array<{ name: string; code: string }>>;
  [key: string]:
    | boolean
    | Record<string, Array<{ name: string; code: string }>>;
} => {
  const isLogin = !!initialState;
  const features = initialState?.features || [];
  // 设置路由可见权限
  const featureMap = features.reduce<Record<string, boolean>>(
    (acc, feature) => ({ ...acc, [feature.code]: true }),
    {},
  );

  // 封装一个可用于页面内业务判断的权限列表
  const featuresList = features.reduce<
    Record<string, Array<{ name: string; code: string }>>
  >(
    (acc, item) => ({
      ...acc,
      ...(item?.permissions && {
        [item.code]: item.permissions.map(({ name, featureCode: code }) => ({
          name,
          code,
        })),
      }),
    }),
    {},
  );

  return {
    isLogin,
    featuresList,
    ...featureMap,
  };
};
