/*
 * @Description: 教材版本管理
 * @Date: 2025-02-22 15:51:06
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-12 14:39:23
 */
import CommonSelect from '@/components/CommonSelect';
import CustomCollapse from '@/components/CustomCollapse';
import { SECTION } from '@/constants';
import { DictionarieState } from '@/models/dictionarie';
import { create, index, remove, update } from '@/services/textbooks';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import EditModal from './EditModal';

const Textbook: React.FC<{
  dictionarie: DictionarieState;
}> = ({ dictionarie }) => {
  const actionRef = useRef<ActionType>();

  // 编辑弹窗
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Textbook | undefined>(undefined);

  // 当前选中的科目
  const [currentSubject, setCurrentSubject] = useState<API.Subject>();
  const [title, setTitle] = useState<string>('查询条件');

  const handleSave = async (values: API.Textbook) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create({
        ...values,
        subject_id: currentSubject?.id || 0,
      });
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Textbook) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.Textbook, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '学段',
      dataIndex: ['subject', 'grade_section'],
      key: 'grade_section',
      valueEnum: {
        [SECTION.小学]: '小学',
        [SECTION.初中]: '初中',
        [SECTION.高中]: '高中',
      },
      search: false,
      hidden: true,
    },
    {
      title: '学科',
      dataIndex: ['subject', 'subject'],
      key: 'subject',
    },
    {
      title: '教材版本',
      dataIndex: 'textbook_version',
      key: 'textbook_version',
    },
    {
      title: '别名',
      dataIndex: 'alias',
      key: 'alias',
    },
    {
      title: '出版单位',
      dataIndex: 'publisher_id',
      key: 'publisher_id',
      render: (_, record) => {
        return record.publisher?.name;
      },
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (currentSubject) {
      const section = dictionarie.list.find(
        (item) => item.code === currentSubject.grade_section,
      )?.name;
      setTitle(`${section} / ${currentSubject.subject}`);
    } else {
      setTitle('查询条件');
    }
  }, [currentSubject]);

  return (
    <>
      <CustomCollapse
        items={[
          {
            key: '1',
            label: title,
            children: (
              <CommonSelect
                level="subject"
                onChange={({ subject }) => {
                  setCurrentSubject(subject);
                }}
              />
            ),
          },
        ]}
        defaultActiveKey={['1']}
        style={{ marginBottom: 16 }}
      />
      <ProTable<API.Textbook>
        options={false}
        actionRef={actionRef}
        rowKey="id"
        headerTitle={title || '教材版本管理'}
        columns={columns}
        params={{
          subject_id: currentSubject?.id,
        }}
        request={async (params, sort, filter) => {
          if (!params.subject_id) {
            return {
              data: [],
              total: 0,
            };
          }
          const { errCode, msg, data } = await index({
            ...params,
            sort,
            filter,
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data.list,
            total: data.total,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
        search={false}
      />
      <EditModal
        open={modalVisible}
        info={current}
        currentSubject={currentSubject}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(Textbook);
