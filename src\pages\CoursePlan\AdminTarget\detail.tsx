import { Typography } from 'antd';
import React from 'react';

const { Title, Paragraph } = Typography;
const AdminTarget: React.FC = () => {
  return (
    <div className="commonWapper">
      <Title level={2}>培养目标管理</Title>
      <Title level={4}>1.有理想</Title>
      <Paragraph>
        热爱祖国，热爱人民，热爱中国共产党，学习伟大建党精神。努
        力学习和弘扬社会主义先进文化、革命文化和中华优秀传统文化，理
        解和践行社会主义核心价值观，逐步领会改革创新的时代精神。懂得
        坚持走中国特色社会主义道路的道理，初步树立共产主义远大理想和
        中国特色社会主义共同理想。明确人生发展方向，追求美好生活，能
        够将个人追求融入国家富强、民族复兴、人民幸福的伟大梦想之中。
      </Paragraph>
      <Title level={4}>2. 有本领</Title>
      <Paragraph>
        乐学善学，勤于思考，保持好奇心与求知欲，形成良好的学习习
        惯，初步掌握适应现代化社会所需要的知识与技能，具有学会学习的
        能力。乐于提问，敢于质疑，学会在真实情境中发现问题、解决问
        题，具有探究能力和创新精神。自理自立，热爱劳动，掌握基本的生
        活技能，具有良好的生活习惯。强身健体，健全人格，养成体育运动
        的习惯，掌握基本的健康知识和适合自身的运动技能，树立生命安全
        一、培养目标I 与健康意识，形成积极的心理品质，具有抗挫折能力与自我保护能
        力。向善尚美，富于想象，具有健康的审美情趣和初步的艺术鉴赏、
        表现能力。学会交往，善于沟通，具有基本的合作能力、团队精神。
      </Paragraph>
    </div>
  );
};

export default AdminTarget;
