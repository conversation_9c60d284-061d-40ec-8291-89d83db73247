import { But<PERSON>, Drawer, Form, Space, Spin } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
import QuestionFormWrapper from './QuestionFormWrapper';

export interface QuestionFormRef {
  /** 显示录题 */
  show: () => void;
  /** 清空录题表单 */
  clearContents: () => void;
  /** 初始化表单数据 */
  initData: (data?: API.SystemQuestion) => void;
  /** 加载表单 默认状态：false，默认提示：加载中... */
  loading: (status: boolean, text?: string) => void;
  /** 关闭录题 */
  close: () => void;
  /** answerAnalysis 模式时，仅答案和解析可编辑 */
  mode?: 'answerAnalysis' | undefined;
}

interface QuestionFormProps {
  refresh?: () => Promise<void>;
  subjectId?: number | string | null;
  mode?: 'answerAnalysis';
  handleQuestionSubmit?: (values: Partial<API.SystemQuestion>) => void;
}

const QuestionForm = forwardRef<QuestionFormRef, QuestionFormProps>(
  (
    { handleQuestionSubmit, refresh, mode },
    ref: React.Ref<QuestionFormRef>,
  ) => {
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);
    const [initValues, setInitValues] = useState<Partial<API.SystemQuestion>>();
    const [loading, setLoading] = useState(false);
    const [tip, setTip] = useState('加载中...');
    const [key, setKey] = useState(0);

    const onClose = () => {
      setOpen(false);
      setTimeout(() => {
        form.resetFields();
        setInitValues(undefined);
        refresh?.();
      }, 300);
    };

    useImperativeHandle(ref, () => ({
      // 暴露给父组件的方法
      show: () => {
        setOpen(true);
      },
      close: () => {
        setOpen(false);
        onClose();
      },
      clearContents: () => {
        setInitValues(undefined);
        setKey((prev) => prev + 1);
      },
      initData: (data?: API.SystemQuestion) => {
        setInitValues(data);
      },
      loading: (status: boolean, text?: string) => {
        setLoading(status);
        if (status) {
          setTip(text || '加载中...');
        }
      },
    }));
    return (
      <Drawer
        title={initValues ? '试题编辑' : '试题录入'}
        width={'65vw'}
        open={open}
        onClose={onClose}
        maskClosable={false}
        destroyOnClose
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button
              onClick={() => {
                form.submit();
              }}
              type="primary"
            >
              提交
            </Button>
          </Space>
        }
        styles={{
          footer: {
            textAlign: 'right',
          },
          body: {
            padding: '16px',
          },
        }}
      >
        <Spin tip={tip} spinning={loading}>
          {open && (
            <QuestionFormWrapper
              mode={mode}
              key={key}
              form={form}
              initValues={initValues}
              onClose={onClose}
              setLoading={setLoading}
              handleQuestionSubmit={handleQuestionSubmit}
            />
          )}
        </Spin>
      </Drawer>
    );
  },
);
export default QuestionForm;
