/*
 * @Description: 义务教育学科管理
 * @Date: 2025-02-22 10:38:03
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-21 17:38:26
 */
import CommonSelect from '@/components/CommonSelect';
import CustomCollapse from '@/components/CustomCollapse';
import { SECTION } from '@/constants';
import { create, index, remove, update } from '@/services/subjects';
import { ExclamationCircleFilled, PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Popconfirm, Space, Table } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';
import ImpotModal from './ImpotModal';
import PreviewModal from './PreviewModal';

const Subject: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [currentSection, setCurrentSection] = useState<API.Dictionarie>();
  const [title, setTitle] = useState('查询条件');

  const [modalVisible, setModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Subject | undefined>(undefined);

  const handleSave = async (values: API.Subject, callback?: () => void) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      callback?.();
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Subject) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.Subject, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '学段',
      dataIndex: 'grade_section',
      key: 'grade_section',
      valueEnum: {
        [SECTION.小学]: '小学',
        [SECTION.初中]: '初中',
        [SECTION.高中]: '高中',
      },
      search: false,
      hidden: true,
    },
    {
      title: '学科',
      dataIndex: 'subject',
      key: 'subject',
    },
    {
      title: '封面',
      dataIndex: 'cover',
      key: 'cover',
      valueType: 'image',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (_, record) => {
        return record.description ? (
          <Button
            type="link"
            onClick={() => {
              Modal.info({
                title: '描述',
                content: (
                  <div
                    dangerouslySetInnerHTML={{ __html: record.description! }}
                  ></div>
                ),
                okText: '关闭',
              });
            }}
          >
            描述信息
          </Button>
        ) : (
          ''
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setPreviewModalVisible(true);
            }}
          >
            查看
          </Button>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            description="删除学科后，对应的其他信息也会被清除，包括并不限于教材和试题"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <CustomCollapse
        items={[
          {
            key: '1',
            label: title,
            children: (
              <CommonSelect
                onChange={({ section }) => {
                  setCurrentSection(section);
                  setTitle(`${section?.name || ''}`);
                }}
              />
            ),
          },
        ]}
        defaultActiveKey={['1']}
        style={{ marginBottom: 16 }}
      />
      <ProTable<API.Subject>
        actionRef={actionRef}
        rowKey="id"
        headerTitle={title}
        columns={columns}
        params={{
          grade_section: currentSection?.code,
        }}
        request={async (params, sort, filter) => {
          if (!params.grade_section) {
            return {
              data: [],
            };
          }
          const { errCode, msg, data } = await index({
            ...params,
            sort,
            filter,
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data.list,
            total: data.total,
          };
        }}
        rowSelection={{
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          alwaysShowAlert: true,
        }}
        tableAlertOptionRender={({ selectedRows }) => {
          return (
            <Space size={16}>
              <a
                onClick={() => {
                  Modal.confirm({
                    title: '确定要删除这些学科吗？',
                    content:
                      '删除学科后，对应的其他信息也会被清除，包括并不限于教材和试题',
                    icon: <ExclamationCircleFilled />,
                    onOk: () => {
                      const promises = selectedRows.map((r) => remove(r.id));
                      Promise.all(promises).then((res) => {
                        const err = res.find((item) => item.errCode);
                        if (err) {
                          message.error(err.msg || '删除失败');
                        } else {
                          message.success('删除成功');
                          actionRef?.current?.reload();
                        }
                      });
                    },
                  });
                }}
                style={{ display: selectedRows.length > 0 ? 'inline' : 'none' }}
              >
                批量删除
              </a>
            </Space>
          );
        }}
        toolBarRender={() => [
          <ImpotModal
            key="import"
            onFinish={() => {
              actionRef?.current?.reload();
            }}
          />,
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
            icon={<PlusOutlined />}
          >
            新增
          </Button>,
        ]}
        search={false}
        options={false}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
      <PreviewModal
        open={previewModalVisible}
        value={current}
        onClose={() => {
          setPreviewModalVisible(false);
        }}
      />
    </>
  );
};

export default Subject;
