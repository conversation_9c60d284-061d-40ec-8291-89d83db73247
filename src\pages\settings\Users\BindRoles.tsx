import { RoleState } from '@/models/role';
import { addUsers, removeUsers } from '@/services/roles';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Drawer, message } from 'antd';
import React, { useRef, useState } from 'react';
import styles from './BindRoles.less';

const BindRoles: React.FC<{
  open: boolean;
  current?: API.User;
  saveHandl: () => void;
  cancleHandl: () => void;
  role: RoleState;
}> = ({ open, current, saveHandl, cancleHandl, role }) => {
  const actionRef1 = useRef<ActionType>();
  const actionRef2 = useRef<ActionType>();
  const [selectedRowKeys1, setSelectedRowKeys1] = useState<React.Key[]>([]);
  const [selectedRowKeys2, setSelectedRowKeys2] = useState<React.Key[]>([]);

  const columns: ProColumns<API.Role, 'text'>[] = [
    {
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      dataIndex: 'name',
      key: 'name',
      title: '角色名称',
    },
    {
      dataIndex: 'description',
      key: 'description',
      title: '描述',
    },
  ];

  const handleAddUsers = async () => {
    if (current && selectedRowKeys1.length) {
      const methods = selectedRowKeys1.map((key) =>
        addUsers(key as number, { userIds: [current.id] }),
      );
      Promise.all(methods).then((res) => {
        const err = res.find((item) => item.errCode);
        if (err) {
          message.error(err.msg || '添加失败');
        } else {
          message.success('添加成功');
          setSelectedRowKeys1([]);
          actionRef1.current?.reload();
          actionRef2.current?.reload();
          saveHandl();
        }
      });
    }
  };

  const handleDelUsers = async () => {
    if (current && selectedRowKeys2.length) {
      const methods = selectedRowKeys2.map((key) =>
        removeUsers(key as number, { userIds: [current.id] }),
      );
      Promise.all(methods).then((res) => {
        const err = res.find((item) => item.errCode);
        if (err) {
          message.error(err.msg || '删除失败');
        } else {
          message.success('删除成功');
          setSelectedRowKeys2([]);
          actionRef1.current?.reload();
          actionRef2.current?.reload();
          saveHandl();
        }
      });
    }
  };

  return (
    <Drawer
      title={
        current
          ? `设置角色（${current.nickname} - ${current.username}）`
          : '未选择用户'
      }
      width={800}
      open={open}
      onClose={cancleHandl}
      destroyOnClose
      styles={{ body: { display: 'flex', flexDirection: 'column' } }}
    >
      <div>
        <ProTable<API.Role>
          headerTitle="选择角色"
          rowKey="id"
          actionRef={actionRef1}
          size="small"
          search={{ filterType: 'light' }}
          columns={columns}
          pagination={{
            pageSize: 5,
          }}
          rowSelection={{
            alwaysShowAlert: true,
            selectedRowKeys: selectedRowKeys1,
            onChange: (_, selectedRows) => {
              setSelectedRowKeys1(selectedRows.map((item) => item.id));
            },
            getCheckboxProps: (record) => {
              const isBound = Boolean(
                current?.roles?.some((r) => r.id === record.id),
              );
              return {
                disabled: isBound,
              };
            },
          }}
          dataSource={role.list}
          options={false}
        />
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '16%',
          borderBlock: '1px solid #aaa',
          paddingBlock: '16px',
        }}
      >
        <CaretDownOutlined
          className={styles.btn}
          disabled={selectedRowKeys1.length === 0}
          onClick={selectedRowKeys1.length === 0 ? undefined : handleAddUsers}
        />
        <CaretUpOutlined
          className={styles.btn}
          disabled={selectedRowKeys2.length === 0}
          onClick={selectedRowKeys2.length === 0 ? undefined : handleDelUsers}
        />
      </div>
      <div>
        <ProTable<API.Role, { userId?: number }>
          headerTitle="已选角色"
          rowKey="id"
          actionRef={actionRef2}
          size="small"
          search={{ filterType: 'light' }}
          columns={columns}
          pagination={{
            pageSize: 6,
          }}
          rowSelection={{
            alwaysShowAlert: true,
            selectedRowKeys: selectedRowKeys2,
            onChange: (_, selectedRows) => {
              setSelectedRowKeys2(selectedRows.map((item) => item.id));
            },
            getCheckboxProps: (record) => ({
              disabled:
                current?.username === 'admin' && record.name === '管理员',
            }),
          }}
          dataSource={current ? current.roles : []}
          options={false}
        />
      </div>
    </Drawer>
  );
};

export default connect(({ role }) => ({ role }))(BindRoles);
