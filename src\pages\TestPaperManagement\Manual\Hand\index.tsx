import { useCurrentAccess } from '@/common/useCurrentAccess';
import CatalogTree from '@/components/CatalogTree';
import CommonCard from '@/components/CommonCard';
import TopicList from '@/components/QuestionBank/TopicList';
import SearchTopicBox, {
  SearchTopicBoxResult,
} from '@/components/SearchTopicBox';
import { getCommonAllQuestions } from '@/services/common_question';
import { createQuestionBasketBulkCount } from '@/services/composer_question_box';
import { useLocation, useModel, useParams } from '@umijs/max';
import { Button, Checkbox, Col, message, Row, Spin } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.less';

interface HeadQuestionProps {
  /** 批量添加试题成功的回调 */
  onSuccess?: () => void;
  /**是否存在已选试题，供试题添加时使用 */
  choosedQuestion?: API.SystemQuestion[];
  choosedEvent?: (value: API.SystemQuestion, status: boolean) => void;
}

export interface RouteState {
  id: number;
  gradeSection: string;
  grade: string;
  gradeId: number;
  gradeSectionId: number;
  version: string;
  textbookId: number;
  volume: string;
  volumeId: number;
  name: string;
  subjectId: number;
  subjectName: string;
  creatorId: number;
  creatorName: string;
  enterpriseId: number;
  description: string;
  paperCount: number;
  process: boolean;
  versionId: number;
  createdAt: string;
  updatedAt: string;
  textbookChecklistId: number;
}

type unitAndPeriod = {
  unit?: number;
  period?: number;
};

const HeadQuestion: React.FC<HeadQuestionProps> = ({
  onSuccess,
  choosedQuestion,
  choosedEvent,
}) => {
  const { initialState } = useModel('@@initialState');
  const { id } = useParams();
  const location = useLocation();
  const { textbookChecklistId, ...rest } =
    (location?.state as RouteState) || {};

  const [allSelect, setAllSelect] = useState<string[]>([]);
  const [data, setData] = useState<any>([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [searchTopicInfo, setSearchTopicInfo] = useState<SearchTopicBoxResult>(
    {},
  );
  const [loading, setLoading] = useState(false);
  const [showAnalysis, setShowAnalysis] = useState<Record<string, boolean>>({});
  const [unitAndPeriod, setUnitAndPeriod] = useState<unitAndPeriod>({
    unit: undefined,
    period: undefined,
  });
  const { isSysAdmin } = useCurrentAccess();
  const [currentCatalog, setCurrentCatalog] = useState<number>();
  /** 缓存 versionId 防止重复请求 */
  const checklist = useMemo(
    () => ({
      id: textbookChecklistId,
    }),
    [textbookChecklistId],
  );

  /** 搜索 */
  const handleSearch = async (data: SearchTopicBoxResult) => {
    const isDataChanged =
      JSON.stringify(data) !== JSON.stringify(searchTopicInfo);
    if (isDataChanged) {
      setAllSelect([]);
      setShowAnalysis({});
      setSearchTopicInfo(data);
      setPagination({ current: 1, pageSize: 10 });
    }
  };

  /** 获取试题列表 */
  const getAllQuestion = useCallback(async () => {
    setLoading(true);
    setShowAnalysis({});
    const {
      difficulty,
      questionTypes: type,
      knowledgePoint,
      searchTopic,
      searchTag,
      platType = 'system',
    } = searchTopicInfo ?? {};
    const { gradeId, subjectId, textbookId, volumeId, gradeSectionId } =
      rest ?? {};
    const { errCode, data, msg } = await getCommonAllQuestions(platType, {
      offset:
        Number((pagination?.current || 1) - 1) *
        Number(pagination?.pageSize || 10),
      limit: Number(pagination?.pageSize || 10),
      difficulty: difficulty?.code,
      userId: initialState?.id,
      type: type?.code,
      grade: gradeId,
      subject: subjectId,
      textbookVersion: textbookId,
      volume: volumeId,
      gradeSectionCode: gradeSectionId,
      catalog: currentCatalog,
      points: knowledgePoint?.join(',') || undefined,
      stem: searchTopic || undefined,
      userTag: searchTag || undefined,
    });
    if (errCode) {
      setLoading(false);
      message.warning(`获取试题列表失败，请稍后重试 ${msg}`);
    } else {
      setData(data ?? []);
      setLoading(false);
    }
  }, [
    pagination?.current,
    pagination?.pageSize,
    searchTopicInfo,
    unitAndPeriod,
    rest,
  ]);

  /** 计算子题总数 */
  const countAllQuestions = (questions: any[]) => {
    let count = 0;
    questions?.forEach((question) => {
      count++;
      if (question.children?.length) {
        count += countAllQuestions(question.children);
      }
    });
    return count;
  };

  /** 计算子题解析数 */
  const countShownAnalysis = (
    questions: any[],
    showAnalysis: Record<string, boolean>,
  ) => {
    let count = 0;
    questions?.forEach((question) => {
      if (showAnalysis[question._id]) count++;
      if (question.children?.length) {
        count += countShownAnalysis(question.children, showAnalysis);
      }
    });
    return count;
  };

  /** 是否仅选择部分题目解析 */
  const isPartiallyShown = () => {
    return (
      countShownAnalysis(data?.list, showAnalysis) > 0 &&
      countShownAnalysis(data?.list, showAnalysis) <
        countAllQuestions(data?.list)
    );
  };

  /** 是否已选择全部解析 */
  const isAllShown = () => {
    return (
      countShownAnalysis(data?.list, showAnalysis) ===
        countAllQuestions(data?.list) && countAllQuestions(data?.list) > 0
    );
  };

  /** 启用/停用 显示全部解析 */
  const toggleAllShown = (e: { target: { checked: boolean } }) => {
    const newShowAnalysis: Record<string, boolean> = {};
    const setAllAnalysis = (questions: any[], checked: boolean) => {
      questions?.forEach((question) => {
        newShowAnalysis[question._id] = checked;
        if (question.children?.length) {
          setAllAnalysis(question.children, checked);
        }
      });
    };
    setAllAnalysis(data?.list, e.target.checked);
    setShowAnalysis(newShowAnalysis);
  };

  /** 批量增加试题 */
  const addQuestionBasket = async () => {
    if (!data?.list?.length) {
      return message.warning('请选择试题');
    }
    const questions = data.list.map((item: any) => {
      return {
        questionBankId: item._id,
        type: item.type.name,
        sourceTable: item.tableName,
      };
    });

    const { errCode, msg } = await createQuestionBasketBulkCount({
      composerPaperId: id,
      questions,
    });

    if (errCode) {
      return message.warning(`添加试题篮失败，请稍后重试 ${msg}`);
    }
    message.success(`添加试题篮成功`);
    onSuccess?.();
  };

  useEffect(() => {
    if (pagination.current && pagination.pageSize) {
      getAllQuestion();
    }
  }, [
    pagination.current,
    pagination.pageSize,
    searchTopicInfo,
    location?.state,
    unitAndPeriod,
  ]);

  return (
    <>
      <div className={styles.handWapper}>
        <Row gutter={16}>
          <Col span={5} className={styles.catalogTree}>
            <CatalogTree
              readonly
              currentTextbookChecklist={checklist}
              onSelect={(info) => {
                setCurrentCatalog(info?.id);
                setUnitAndPeriod({
                  unit: info?.id,
                  period: info?.parent_id,
                });
              }}
            />
          </Col>
          <Col span={19} className={styles.catalogContent}>
            <SearchTopicBox
              onChange={handleSearch}
              subjectId={rest.subjectId}
              showPlat={!isSysAdmin}
              showDifficulty
              showTypes
              showTag
              showKeyword
              showSmart
            />
            <CommonCard
              style={{
                marginTop: '16px',
              }}
              title={`题目总数（${data?.total ?? 0} 道）`}
              hiden={false}
            >
              <Checkbox
                indeterminate={isPartiallyShown()}
                checked={isAllShown()}
                onChange={toggleAllShown}
                disabled={!data?.list?.length}
              >
                全部解析
              </Checkbox>
              <Button
                disabled={!!!data?.list?.length}
                onClick={addQuestionBasket}
              >
                本页全部添加
              </Button>
            </CommonCard>
            <Spin tip="数据加载中..." spinning={loading}>
              <div className={styles.questionListWrapper}>
                <TopicList
                  isTag={false}
                  isGroupModel
                  data={data}
                  choosedQuestion={choosedQuestion}
                  choosedEvent={choosedEvent}
                  onSelect={(value: string, status: boolean) => {
                    if (status) {
                      setAllSelect([...allSelect, value]);
                    } else {
                      setAllSelect(
                        allSelect.filter((item: string) => item !== value),
                      );
                    }
                  }}
                  showAnalysis={showAnalysis}
                  onShowAnalysis={(id: string) => {
                    setShowAnalysis({
                      ...showAnalysis,
                      [id]: !showAnalysis[id],
                    });
                  }}
                  allSelect={allSelect}
                  pagination={{
                    total: data?.total,
                    current: pagination?.current,
                    pageSize: pagination?.pageSize,
                    onChange: (page: any, pageSize: any) => {
                      setPagination({
                        current: page,
                        pageSize: pageSize,
                      });
                    },
                  }}
                />
              </div>
            </Spin>
          </Col>
        </Row>
      </div>
    </>
  );
};
export default HeadQuestion;
