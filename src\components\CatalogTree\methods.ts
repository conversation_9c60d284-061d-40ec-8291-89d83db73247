import {
  complianceDesignCatalogCreate,
  complianceDesignCatalogIndex,
  complianceDesignCatalogInsert,
  complianceDesignCatalogRemove,
  complianceDesignCatalogUpdate,
} from '@/services/compliance_detection_design_catalog';
import {
  lessonWorkCatalogCreate,
  lessonWorkCatalogIndex,
  lessonWorkCatalogInsert,
  lessonWorkCatalogRemove,
  lessonWorkCatalogUpdate,
} from '@/services/lesson_work_catalog';
import {
  create,
  index,
  insert,
  remove,
  update,
} from '@/services/textbook_catalog';
import { convertListToTree } from '@/utils/calc';
import { message } from 'antd';
import { CatalogTreeNode } from './typings';

/**
 * 获取目录树数据
 *
 * @param {number} [textbookChecklist_id] 教材名录ID
 * @return {*}  {Promise<CatalogTreeNode[]>}
 */
export const getTreeData = async (
  textbookChecklist_id?: number,
  proxyType?: string,
  designId?: any,
  maxLevel?: number,
) => {
  if (!textbookChecklist_id) return [];
  let result: any;
  switch (proxyType) {
    case 'lesson_work_catalog':
      result = await lessonWorkCatalogIndex({
        lessonWorkDesign_id: designId,
        textbookChecklist_id,
      });
      break;
    case 'compliance_detection_design_catalog':
      result = await complianceDesignCatalogIndex({
        design_id: designId,
        textbookChecklist_id,
      });
      break;
    default:
      result = await index({ textbookChecklist_id });
      break;
  }
  if (result?.errCode) {
    message.error(result?.msg || '教材章节目录查询失败');
  } else {
    const td = convertListToTree({
      list: (result?.data?.list || []).map((item: any) => ({
        ...item,
        key: item.id,
      })),
      fieldName: {
        parent: 'id',
        child: 'parent_id',
      },
      maxLevel,
    });

    return td;
  }
};

/**
 * 保存目录
 *
 * @param {API.TextbookCatalog} values 目录信息
 * @param {() => void} callback
 */
export const handleSave = async (
  values: API.TextbookCatalog,
  callback: () => void,
  proxyType?: string,
  designId?: any,
) => {
  const { id, ...info } = values;
  let response;
  if (id) {
    switch (proxyType) {
      case 'lesson_work_catalog':
        response = await lessonWorkCatalogUpdate(id, {
          lessonWorkDesign_id: designId,
          ...info,
        });
        break;
      case 'compliance_detection_design_catalog':
        response = await complianceDesignCatalogUpdate(id, {
          design_id: designId,
          ...info,
        });
        break;
      default:
        response = await update(id, info);
        break;
    }
  } else {
    switch (proxyType) {
      case 'lesson_work_catalog':
        response = await lessonWorkCatalogCreate({
          ...values,
          lessonWorkDesign_id: designId,
        });
        break;
      case 'compliance_detection_design_catalog':
        response = await complianceDesignCatalogCreate({
          ...values,
          design_id: designId,
        });
        break;
      default:
        response = await create(values);
        break;
    }
  }

  if (response.errCode) {
    message.error(response.msg);
  } else {
    message.success('操作成功');
    callback();
  }
};

/**
 * 批量生成目录
 *
 * @param {Object}  textbook 教材信息
 * @param {number}  textbook.textbookChecklist_id 所属册次ID
 * @param {number}  [textbook.parent_id] 父ID
 * @param {Object[]}  textbook.list 目录列表
 * @param {string} [textbook.list.title] 标题
 * @param {number} [textbook.list.sort_order] 排序
 * @param {() => void} callback 回调函数
 * @return {*} void
 */
export const batchGeneration = async (
  {
    textbookChecklist_id,
    parent_id,
    list,
    proxyType,
    designId,
  }: {
    textbookChecklist_id?: number;
    parent_id?: number;
    list: {
      title: string;
      sort_order: number;
    }[];
    proxyType?: string;
    designId?: any;
  },
  callback: () => void,
) => {
  if (!textbookChecklist_id) {
    message.error('请先选择教材册次');
    return;
  }
  const promises = list.map(async (item) => {
    switch (proxyType) {
      case 'lesson_work_catalog':
        return lessonWorkCatalogCreate({
          lessonWorkDesign_id: designId,
          textbookChecklist_id,
          parent_id,
          old_id: parent_id,
          ...item,
        });
      case 'compliance_detection_design_catalog':
        return complianceDesignCatalogCreate({
          design_id: designId,
          textbookChecklist_id,
          parent_id,
          old_id: parent_id,
          ...item,
        });
      default:
        return create({
          textbookChecklist_id,
          parent_id,
          ...item,
        });
    }
  });
  const results = await Promise.all(promises);
  const err = results.find((res) => res.errCode);
  if (err) {
    message.error(err.msg || '部分目录创建失败');
  } else {
    message.success('批量创建成功');
    callback();
  }
};

/**
 * 删除单个目录
 *
 * @param {number} id 目录id
 * @param {() => void} callback 删除成功的回调
 */
export const handleDel = async (
  id: number,
  callback: () => void,
  proxyType?: string,
) => {
  let response: any;
  switch (proxyType) {
    case 'lesson_work_catalog':
      response = await lessonWorkCatalogRemove(id);
      break;
    case 'compliance_detection_design_catalog':
      response = await complianceDesignCatalogRemove(id);
      break;
    default:
      response = await remove(id);
      break;
  }

  if (response.errCode) {
    message.error(response.msg);
  } else {
    callback();
  }
};

/**
 * 删除指定目录所有子节点
 *
 * @param {CatalogTreeNode} node 目录节点
 * @param {() => void} callback 删除成功的回调
 * @return {*}
 */
export const cleanChildren = async (
  node: CatalogTreeNode,
  callback: () => void,
  proxyType?: string,
) => {
  const { children } = node;
  if (!children?.length) return;
  const promises = children.map((child) => {
    switch (proxyType) {
      case 'lesson_work_catalog':
        return lessonWorkCatalogRemove(child.id);
      case 'compliance_detection_design_catalog':
        return complianceDesignCatalogRemove(child.id);
      default:
        return remove(child.id);
    }
  });
  const results = await Promise.all(promises);
  const err = results.find((result) => result.errCode);
  if (err) {
    message.error(err.msg || '清除失败');
  } else {
    message.success('清除成功');
    callback();
  }
};

/**
 *
 * @param textbookChecklist_id 参照目录ID
 * @param title 标题
 * @param action 插入方式，insert：插入到前面，append：追加后面
 * @returns 插入结果
 */
export const insertCatalog = async (
  textbookChecklist_id: number,
  info: { title: string; old_id?: number; action: 'insert' | 'append' },
  proxyType?: string,
) => {
  const { title, old_id, action } = info;
  if (!textbookChecklist_id || !title || !action) {
    return { errCode: 400, msg: '操作失败，请重试。' };
  }
  let response: API.ResType<any>;
  switch (proxyType) {
    case 'lesson_work_catalog':
      response = await lessonWorkCatalogInsert(textbookChecklist_id, {
        title,
        old_id: old_id || null,
        action,
      });
      break;
    case 'compliance_detection_design_catalog':
      response = await complianceDesignCatalogInsert(textbookChecklist_id, {
        title,
        old_id: old_id || null,
        action,
      });
      break;
    default:
      response = await insert(textbookChecklist_id, title, action);
      break;
  }
  return response;
};
