/*
 * @Description: 公用Header
 */
import { Space } from 'antd';
import classNames from 'classnames';
import React from 'react';
import ConditionalRender from '../ConditionalRender';
import styles from './index.less';

interface CommonCardProps {
  /** 标题 */
  title?: React.ReactNode;
  /** 居中展示内容，必须title与children同时存在 */
  centerDom?: React.ReactNode;
  centerStyle?: { flex: number };
  /** 子元素 */
  children?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否隐藏 */
  hiden?: boolean;
  /** 是否激活 ( 仅 title 里的元素颜色变为蓝色 ) */
  activeTitle?: boolean;
  /**高度 */
  height?: number | string;
  /** 宽度 */
  width?: number | string;
  /** 背景色 */
  background?: string;
  /** 字体颜色 */
  color?: string;
}

const CommonCard: React.FC<CommonCardProps> = ({
  title,
  centerDom,
  centerStyle,
  children,
  className,
  style,
  activeTitle,
  hiden = false,
  height,
  width,
  background,
  color,
}) => {
  return (
    <ConditionalRender
      hasAccess={!hiden}
      accessComponent={
        <div
          className={classNames(styles.commonCard, className)}
          style={{
            height,
            width,
            ...style,
            background,
            color,
          }}
        >
          <div
            className={classNames(styles.title, {
              [styles.active]: activeTitle,
            })}
          >
            {title}
          </div>
          {centerDom && <div style={centerStyle ?? {}}>{centerDom}</div>}
          <Space>{children}</Space>
        </div>
      }
    />
  );
};
export default CommonCard;
