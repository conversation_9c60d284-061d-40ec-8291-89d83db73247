import { useCurrentAccess } from '@/common/useCurrentAccess';
import ConditionalRender from '@/components/ConditionalRender';
import { create, index, remove, update } from '@/services/question_tier';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const HierarchyQuestionBank: React.FC = () => {
  const { isAdmin } = useCurrentAccess();
  const { initialState } = useModel('@@initialState');
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<any | undefined>(undefined);

  const handleSave = async (values: any) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create({
        ...values,
        enterpriseCode: initialState?.enterprise?.code,
        enterpriseName: initialState?.enterprise?.name,
      });
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: any) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<any, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center',
      key: 'name',
    },
    {
      title: '描述',
      align: 'center',
      dataIndex: 'description',
      key: 'description',
      search: false,
    },
    {
      title: '操作',
      width: 120,
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <ConditionalRender
          hasAccess={isAdmin}
          accessComponent={
            <Space>
              <Button
                type="link"
                onClick={() => {
                  setCurrent(record);
                  setModalVisible(true);
                }}
              >
                编辑
              </Button>
              <Popconfirm
                title="确认删除？"
                onConfirm={() => {
                  handleDel(record);
                }}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </Space>
          }
        />
      ),
    },
  ];

  return (
    <>
      <ProTable<any>
        options={false}
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index({
            ...params,
            name: params.name || undefined,
            enterpriseCode: initialState?.enterprise?.code,
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data.list,
            total: data.total,
          };
        }}
        toolBarRender={() => [
          <ConditionalRender
            key="add"
            hasAccess={isAdmin}
            accessComponent={
              <Button
                type="primary"
                onClick={() => {
                  setCurrent(undefined);
                  setModalVisible(true);
                }}
                icon={<PlusOutlined />}
              >
                新增
              </Button>
            }
          />,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default HierarchyQuestionBank;
