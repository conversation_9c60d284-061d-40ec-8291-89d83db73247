import { index as indexSubject } from '@/services/subjects';
import { Col, ColProps, message, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { RadioGroupStyled } from './styled';

const Subject: React.FC<{
  initalValue?: number;
  onChange?: (value?: API.Subject) => void;
  /** 学段编号 */
  sectionCode?: string;
  /** 标题列属性 */
  labelCol?: ColProps;
  /** 自定义行样式 */
  rowStyles?: React.CSSProperties;
}> = ({ initalValue, onChange, sectionCode, labelCol, rowStyles }) => {
  const [currentSubjectId, setCurrentSubjectId] = useState<number | undefined>(
    initalValue,
  );
  const [subjectList, setSubjectList] = useState<API.Subject[]>([]);

  useEffect(() => {
    if (sectionCode) {
      (async () => {
        const { errCode, data, msg } = await indexSubject({
          grade_section: sectionCode,
        });
        if (errCode) {
          message.error(msg || '科目列表获取失败');
          return;
        }
        setSubjectList(data.list || []);
      })();
    }
  }, [sectionCode]);

  // 学科列表切换后，检查currentSubject是否在列表中
  useEffect(() => {
    if (currentSubjectId) {
      if (!subjectList.find((d) => d.id === currentSubjectId)) {
        setCurrentSubjectId(undefined);
      }
    } else {
      setCurrentSubjectId(subjectList?.[0]?.id);
      onChange?.(subjectList?.[0]);
    }
  }, [subjectList, currentSubjectId]);

  // 监听initalValue变化
  useEffect(() => {
    if (initalValue !== undefined && initalValue !== currentSubjectId) {
      setCurrentSubjectId(initalValue);
      const item = subjectList.find((d) => d.id === initalValue);
      onChange?.(item);
    }
  }, [initalValue, subjectList]);

  return (
    <Row className={styles.row} style={rowStyles}>
      <Col className={styles.label} {...labelCol}>
        <label>学科</label>
      </Col>
      <Col>
        <RadioGroupStyled
          size="small"
          options={subjectList.map((d) => ({
            label: d.subject,
            value: d.id,
            title: d.subject,
          }))}
          onChange={(e) => {
            setCurrentSubjectId(e.target.value);
            const item = subjectList.find((d) => d.id === e.target.value);
            onChange?.(item);
          }}
          value={currentSubjectId}
          optionType="button"
          buttonStyle="solid"
        />
      </Col>
    </Row>
  );
};

export default Subject;
