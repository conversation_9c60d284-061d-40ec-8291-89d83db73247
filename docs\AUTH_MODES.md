# 认证模式配置说明

本项目支持三种认证流程，可以通过配置文件进行切换。

## 认证流程说明

### 1. 第三方Token参数认证（始终有效）
- **触发条件**: URL中携带`token`参数
- **流程**: 
  1. 系统检测到URL中的`token`参数
  2. 调用`loginWithSsoToken`接口进行快速认证
  3. 生成本地认证token完成登录
- **适用场景**: 与第三方系统集成时的快速认证

### 2. SSO OAuth2认证
- **配置**: `AUTH_MODE: 'sso'`（默认）
- **流程**:
  1. 用户访问系统未登录时跳转到SSO认证页面
  2. 用户在SSO页面完成登录
  3. 携带`code`返回到`oauth2/callback`页面
  4. 系统使用`code`换取token完成认证
- **适用场景**: 与统一身份认证系统集成

### 3. 本地JWT认证
- **配置**: `AUTH_MODE: 'local'`
- **流程**:
  1. 用户访问系统未登录时跳转到本地登录页面
  2. 用户输入用户名和密码
  3. 系统调用本地登录接口验证用户身份
  4. 返回JWT token完成认证
- **适用场景**: 独立部署，不依赖外部认证系统

## 配置方法

### 1. 修改配置文件
在 `config/config.ts` 中修改 `AUTH_MODE` 配置：

```typescript
define: {
  // ... 其他配置
  /** 认证模式配置: 'sso' | 'local' */
  AUTH_MODE: 'local', // 或 'sso'
}
```

### 2. 后端接口要求

#### SSO模式接口
- `GET /sso/callback` - OAuth2回调接口
- `GET /sso/userinfo` - 获取用户信息
- `POST /sso/login/ssoToken` - 第三方token登录
- `GET /sso/logout` - 退出登录

#### 本地认证模式接口
- `POST /auth/login` - 本地登录接口
- `GET /auth/userinfo` - 获取当前用户信息

### 3. 接口数据格式

#### 登录接口请求格式
```typescript
// 本地登录
POST /auth/login
{
  "username": "用户名",
  "password": "密码"
}
```

#### 登录接口响应格式
```typescript
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "token": "JWT_TOKEN",
    "expiresIn": 3600 // 过期时间（秒）
  }
}
```

#### 用户信息接口响应格式
```typescript
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "username": "用户名",
    "nickname": "昵称",
    "avatar": "头像URL",
    "email": "邮箱",
    "enterprise": {}, // 企业信息
    "roles": [], // 角色信息
    "features": [] // 功能权限
  }
}
```

## 切换认证模式的影响

### 从SSO切换到本地认证
1. 用户将无法通过SSO页面登录
2. 未登录用户会跳转到本地登录页面
3. 第三方token参数认证仍然有效
4. 需要确保后端提供本地认证接口

### 从本地认证切换到SSO
1. 用户将无法通过本地登录页面登录
2. 未登录用户会跳转到SSO认证页面
3. 第三方token参数认证仍然有效
4. 需要确保SSO配置正确

## 注意事项

1. **第三方Token认证优先级最高**: 无论配置什么认证模式，URL中的`token`参数都会被优先处理
2. **Token存储**: 所有认证模式生成的token都存储在localStorage中
3. **权限控制**: 不同认证模式下的权限控制逻辑相同，都基于用户的features字段
4. **退出登录**: 本地认证模式下退出登录不会调用SSO的退出接口
5. **开发调试**: 可以通过修改配置文件快速切换认证模式进行测试

## 开发建议

1. **开发环境**: 建议使用本地认证模式，方便调试
2. **生产环境**: 根据实际需求选择SSO或本地认证
3. **测试**: 切换认证模式后需要清除localStorage中的认证信息重新测试
4. **监控**: 建议在生产环境中监控认证失败的情况，及时发现配置问题
