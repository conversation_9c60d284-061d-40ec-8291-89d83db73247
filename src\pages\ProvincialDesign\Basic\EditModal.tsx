import CommmonGradeSubject from '@/components/CommmonGradeSubject';
import {
  FormInstance,
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
  form: FormInstance<any>;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  form,
}) => {
  useEffect(() => {
    if (info && open) {
      form.setFieldsValue(info);
    }
  }, [info, open]);
  return (
    <ModalForm<any>
      width={560}
      form={form}
      title={info ? '编辑基础型作业' : '新增基础型作业'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '4em' }}
      onFinish={onSave}
    >
      <ProFormText name="id" label="ID" hidden />
      <CommmonGradeSubject form={form} />
      <ProFormTextArea
        name="description"
        label="描述"
        fieldProps={{
          autoSize: { minRows: 2, maxRows: 12 },
        }}
        rules={[{ required: true, message: '请输入描述！' }]}
      />
    </ModalForm>
  );
};

export default EditModal;
