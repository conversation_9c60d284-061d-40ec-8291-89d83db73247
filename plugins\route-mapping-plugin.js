const fs = require('fs');
const path = require('path');

/**
 * UMI 路由映射自动生成插件
 */
function routeMappingPlugin(api) {
  let isWatching = false;
  let hasGenerated = false;

  // 提取路由映射
  const extractRouteMapping = (routes, mapping = {}) => {
    routes.forEach((route) => {
      // 只处理有 access 和 path 的路由，且不是隐藏菜单项
      if (
        route.access &&
        route.path &&
        !route.hideInMenu &&
        route.access !== 'isLogin'
      ) {
        // 排除参数路由（包含 :id 等）
        if (!route.path.includes(':')) {
          mapping[route.access] = route.path;
        }
      }

      // 递归处理子路由
      if (route.routes && route.routes.length > 0) {
        extractRouteMapping(route.routes, mapping);
      }
    });

    return mapping;
  };

  // 生成映射文件内容
  const generateMappingFileContent = (mapping) => {
    const timestamp = new Date().toLocaleString('zh-CN');

    // 按功能编号排序
    const sortedEntries = Object.entries(mapping).sort(([a], [b]) =>
      a.localeCompare(b),
    );

    const mappingContent = sortedEntries
      .map(([code, path]) => `  '${code}': '${path}',`)
      .join('\n');

    return `/**
 * 功能编号到路由路径的映射
 * 此文件由 UMI 插件自动生成，请勿手动修改
 * 生成时间: ${timestamp}
 */

export const FEATURE_ROUTE_MAPPING: Record<string, string> = {
${mappingContent}
};

/**
 * 根据功能编号获取对应的路由路径
 * @param featureCode 功能编号
 * @returns 路由路径，如果未找到则返回 null
 */
export const getRouteByFeatureCode = (featureCode: string): string | null => {
  return FEATURE_ROUTE_MAPPING[featureCode] || null;
};

/**
 * 获取所有有效的功能编号列表（按编号排序）
 * @returns 排序后的功能编号数组
 */
export const getAllFeatureCodes = (): string[] => {
  return Object.keys(FEATURE_ROUTE_MAPPING).sort((a, b) => a.localeCompare(b));
};

/**
 * 检查功能编号是否存在对应的路由
 * @param featureCode 功能编号
 * @returns 是否存在对应路由
 */
export const hasRouteForFeature = (featureCode: string): boolean => {
  return featureCode in FEATURE_ROUTE_MAPPING;
};`;
  };

  // 更新映射文件
  const updateMappingFile = () => {
    try {
      // 获取路由配置
      const routes = api.config.routes || [];

      // 提取映射
      const mapping = extractRouteMapping(routes);

      // 生成文件内容
      const fileContent = generateMappingFileContent(mapping);

      // 写入文件
      const outputPath = path.join(api.cwd, 'src/constants/routeMapping.ts');
      fs.writeFileSync(outputPath, fileContent, 'utf-8');

      console.log(`✅ 路由映射已更新: ${Object.keys(mapping).length} 个路由`);
    } catch (error) {
      console.error('❌ 生成路由映射失败:', error.message);
    }
  };

  // 监听路由文件变化
  const startWatching = () => {
    if (isWatching) return;

    const routeFilePath = path.join(api.cwd, 'config/route.ts');

    if (fs.existsSync(routeFilePath)) {
      console.log('👀 开始监听路由文件变化...');

      fs.watchFile(routeFilePath, { interval: 2000 }, (curr, prev) => {
        if (curr.mtime !== prev.mtime) {
          console.log('🔄 路由文件变化，重新生成映射...');
          // 延迟一点时间，确保文件写入完成
          setTimeout(updateMappingFile, 500);
        }
      });

      isWatching = true;
    }
  };

  // 在开发模式首次编译完成时生成映射并开始监听
  api.onDevCompileDone(() => {
    if (!hasGenerated) {
      updateMappingFile();
      hasGenerated = true;

      if (api.env === 'development') {
        startWatching();
      }
    }
  });

  // 在构建开始时生成映射
  api.onBuildComplete(() => {
    updateMappingFile();
  });

  // 添加命令行命令
  api.registerCommand({
    name: 'generate-route-mapping',
    description: '生成功能编号到路由路径的映射文件',
    fn: () => {
      updateMappingFile();
    },
  });
}

module.exports = routeMappingPlugin;
