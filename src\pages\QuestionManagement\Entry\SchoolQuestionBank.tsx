import CatalogTreeWithHead from '@/components/CatalogTreeWithHead';
import CommonCard from '@/components/CommonCard';
import ConditionalRender from '@/components/ConditionalRender';
import LookQuestionModal from '@/components/QuestionBank/LookQuestionModal';
import ReviseClassHoursModal from '@/components/QuestionBank/ReviseClassHoursModal';
import TopicList from '@/components/QuestionBank/TopicList';
import QuestionForm, { QuestionFormRef } from '@/components/QuestionForm';
import SearchTopicBox, {
  SearchTopicBoxResult,
} from '@/components/SearchTopicBox';
import { QuestionContext } from '@/context/QuestionContext';
import {
  batchSchoolQuestion,
  createAssociateSchoolQuestion,
  createSchoolQuestion,
  getAllSchoolQuestion,
  removeAssociateSchoolQuestion,
  removeSchoolQuestion,
  updateAssociateSchoolQuestion,
  updateSchoolQuestion,
} from '@/services/school_question_bank';
import {
  DeleteOutlined,
  FormOutlined,
  PlusOutlined,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Button,
  Checkbox,
  Col,
  message,
  Popconfirm,
  Row,
  Space,
  Spin,
} from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { PartialWithRequired } from 'typings';
import { MONGO_MODEL_KEY } from '.';
import styles from './index.less';
import ModalUpload from './ModalUpload';

interface SubjectInfoType {
  section?: API.Dictionarie;
  grade?: API.Dictionarie;
  subject?: API.Subject;
  textbook?: API.Textbook;
  catalog?: API.TextbookCatalog;
  volume?: API.TextbookChecklist;
}

interface SchoolQuestionBankProps {
  /** 是否只读 */
  readonly?: boolean;
}

const SchoolQuestionBank: React.FC<SchoolQuestionBankProps> = ({
  readonly,
}) => {
  const { initialState } = useModel('@@initialState');
  const childRef = useRef<QuestionFormRef>(null);
  const [allSelect, setAllSelect] = useState<string[]>([]);
  const [data, setData] = useState<any>([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [searchTopicInfo, setSearchTopicInfo] = useState<SearchTopicBoxResult>(
    {},
  );
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [isReadonly, setIsReadonly] = useState(false);
  const [showAnalysis, setShowAnalysis] = useState<Record<string, boolean>>({});
  const [lookQuestioOpen, setLookQuestioOpen] = useState<{
    open: boolean;
    data?: API.SystemQuestion;
  }>({
    open: false,
    data: undefined,
  });
  /** 教材信息 */
  const [subjectInfo, setSubjectInfo] = useState<SubjectInfoType>({});
  const [selectedUnitHours, setSelectedUnitHours] = useState<boolean>(false);
  /** 编辑试题的科目id */
  const [editSubjectId, setEditSubjectId] = useState<number>();
  const [currentCatalog, setCurrentCatalog] = useState<{
    id?: number;
    name?: string;
  }>();
  const [uploadModalOpen, setUploadModalOpen] = useState<boolean>(false);

  /** 搜索 */
  const handleSearch = async (data: SearchTopicBoxResult) => {
    const isDataChanged =
      JSON.stringify(data) !== JSON.stringify(searchTopicInfo);
    if (isDataChanged) {
      setAllSelect([]);
      setShowAnalysis({});
      setSearchTopicInfo(data);
      setPagination({ current: 1, pageSize: 10 });
    }
  };

  /** 获取试题列表 */
  const getAllQuestion = useCallback(async () => {
    const {
      difficulty,
      questionTypes: type,
      searchTopic,
      knowledgePoint,
      searchTag,
    } = searchTopicInfo ?? {};
    const { grade, textbook, section, subject, volume } = subjectInfo ?? {};
    if (!subject || !textbook || !volume || !section || !grade) return;
    setLoading(true);
    setShowAnalysis({});
    const { errCode, data, msg } = await getAllSchoolQuestion({
      offset:
        Number((pagination?.current || 1) - 1) *
        Number(pagination?.pageSize || 10),
      limit: Number(pagination?.pageSize || 10),
      difficulty: difficulty?.code,
      type: type?.code,
      grade: grade?.code,
      subject: subject?.id,
      textbookVersion: textbook?.id,
      volume: volume?.id,
      gradeSection: section?.code,
      catalog: currentCatalog?.id,
      enterpriseCode: initialState?.enterprise?.code,
      userId: initialState?.id,
      points: knowledgePoint?.join(',') || undefined,
      stem: searchTopic || undefined,
      userTag: searchTag || undefined,
    });
    if (errCode) {
      setLoading(false);
      setData([]);
      message.warning(`获取试题列表失败，请稍后重试 ${msg}`);
    } else {
      setData(data ?? []);
      setLoading(false);
    }
  }, [pagination?.current, pagination?.pageSize, searchTopicInfo, subjectInfo]);

  /** 新增试题 */
  const addSystemQuestion = async (data: Partial<API.SystemQuestion>) => {
    const {} = subjectInfo;
    const { errCode, msg } = await createSchoolQuestion(data);
    if (errCode) {
      return message.warning(`创建试题失败，请稍后重试 ${msg}`);
    }
    message.success('创建试题成功');
    childRef?.current?.loading(false);
    childRef?.current?.clearContents();
  };

  /** 编辑试题 */
  const editQuestion = async (
    data: PartialWithRequired<API.SystemQuestion, '_id'>,
  ) => {
    childRef?.current?.loading(true, '修改试题中，请耐心等候...');
    const { errCode, msg } = await updateSchoolQuestion(data._id, data);
    if (errCode) {
      childRef?.current?.loading(false);
      return message.warning(`修改试题失败，请稍后重试 ${msg}`);
    }
    message.success('修改试题成功');
    childRef?.current?.loading(false);
    childRef?.current?.close();
  };

  /** 新增父子题 */
  const addAssociateQuestion = async (data: Partial<API.SystemQuestion>) => {
    childRef?.current?.loading(true, '创建试题中，请耐心等候...');
    const { errCode, msg } = await createAssociateSchoolQuestion(data as any);
    if (errCode) {
      childRef?.current?.loading(false);
      return message.warning(`创建组题类试题失败，请稍后重试 ${msg}`);
    }
    message.success('创建组题类试题成功');
    childRef?.current?.loading(false);
    childRef?.current?.clearContents();
  };

  /** 编辑父子题 */
  const editAssociateQuestion = async (data: Partial<API.SystemQuestion>) => {
    const { _id: id } = data;
    if (!id) {
      return message.warning('组题类试题ID为空');
    }
    childRef?.current?.loading?.(true, '修改试题中，请耐心等候...');
    const { errCode, msg } = await updateAssociateSchoolQuestion(
      id,
      data as any,
    );
    if (errCode) {
      return message.warning(`修改组题类试题失败，请稍后重试 ${msg}`);
    } else {
      message.success('修改组题类试题成功');
    }
    childRef?.current?.loading(false);
    childRef?.current?.close();
  };

  /** 提交试题 */
  const submitQuestion = async (data: Partial<API.SystemQuestion>) => {
    const { grade, textbook, section, subject, volume } = subjectInfo;
    /** 增加部分参数 */
    const newData: any = {
      ...data,
      author: {
        id: initialState?.id,
        name: initialState?.nickname,
      },
      enterpriseCode: initialState?.enterprise?.code,
      grade: data?.grade || {
        name: grade?.name,
        code: grade?.code,
      },
      subject: data?.subject || {
        name: subject?.subject,
        id: subject?.id,
      },
      textbookVersion: data?.textbookVersion || {
        name: textbook?.textbook_version,
        id: textbook?.id,
      },
      volume: data?.volume || {
        name: volume?.volume,
        id: volume?.id,
      },
      gradeSection: data?.gradeSection || {
        name: section?.name,
        code: section?.code,
      },
      catalog: data?.catalog || {
        name: currentCatalog?.name,
        id: currentCatalog?.id,
      },
    };
    try {
      if (newData.isCompose) {
        // 父子题
        if (newData?._id) {
          return await editAssociateQuestion(newData);
        }
        return await addAssociateQuestion(newData);
      } else {
        // 普通题
        if (newData?._id) {
          return await editQuestion({ ...newData, _id: newData._id });
        }
        return await addSystemQuestion(newData);
      }
    } catch (error) {
      childRef?.current?.loading?.(false);
      return message.warning(`提交试题失败，请稍后重试 ${error}`);
    }
  };

  /** 删除试题 */
  const deleteQuestion = async (id: string, isFather = false) => {
    /** 是否为父级ID */
    if (isFather) {
      const { errCode, msg } = await removeAssociateSchoolQuestion(id);
      if (errCode) {
        return message.warning(`删除组题类试题失败，请稍后重试 ${msg}`);
      }
      message.success('删除组题类试题成功');
    } else {
      const { errCode, msg } = await removeSchoolQuestion(id);
      if (errCode) {
        return message.warning(`删除试题失败，请稍后重试 ${msg}`);
      }
      message.success('删除试题成功');
    }
    getAllQuestion();
  };

  /** 批量删除试题 */
  const batchDeleteQuestion = async () => {
    const { errCode, msg } = await batchSchoolQuestion({
      ids: allSelect,
    });
    if (errCode) {
      return message.warning(`批量删除试题失败，请稍后重试 ${msg}`);
    }
    message.success('批量删除试题成功');
    setAllSelect([]);
    getAllQuestion();
  };

  /** 查看试题 */
  const lookQuestion = (data: API.SystemQuestion) => {
    setLookQuestioOpen({
      open: true,
      data,
    });
  };

  /** 编辑回填试题 */
  const onEditInfo = (data: API.SystemQuestion) => {
    setCurrentCatalog(data?.catalog);
    setEditSubjectId(data?.subject?.id);
    childRef.current?.show?.();
    if (data?.isCompose) {
      data.questions = data?.children.map(
        (item: { type: { code: string } }) => {
          return {
            ...item,
            type_code: item?.type?.code,
          };
        },
      );
    }
    const backfillData = {
      ...data,
      type_code: data?.type?.code,
      answer:
        data?.baseType?.name === '多选题'
          ? JSON.parse(data?.answer || '[]')
          : data?.answer,
      difficulty: data?.difficulty?.code,
      tier: data?.tier?.id,
      cognitiveHierarchy: data?.cognitiveHierarchy?.id,
      coreQuality: data?.coreQuality?.id,
      investigationAbility: data?.investigationAbility?.id,
      classification: data?.classification?.id,
    };
    setIsReadonly(true);
    childRef?.current?.initData?.(backfillData as any);
  };

  /** 计算子题总数 */
  const countAllQuestions = (questions: any[]) => {
    let count = 0;
    questions?.forEach((question) => {
      count++;
      if (question.children?.length) {
        count += countAllQuestions(question.children);
      }
    });
    return count;
  };

  /** 计算子题解析数 */
  const countShownAnalysis = (
    questions: any[],
    showAnalysis: Record<string, boolean>,
  ) => {
    let count = 0;
    questions?.forEach((question) => {
      if (showAnalysis[question._id]) count++;
      if (question.children?.length) {
        count += countShownAnalysis(question.children, showAnalysis);
      }
    });
    return count;
  };

  /** 是否仅选择部分题目解析 */
  const isPartiallyShown = () => {
    return (
      countShownAnalysis(data?.list, showAnalysis) > 0 &&
      countShownAnalysis(data?.list, showAnalysis) <
        countAllQuestions(data?.list)
    );
  };

  /** 是否已选择全部解析 */
  const isAllShown = () => {
    return (
      countShownAnalysis(data?.list, showAnalysis) ===
        countAllQuestions(data?.list) && countAllQuestions(data?.list) > 0
    );
  };

  /** 启用/停用 显示全部解析 */
  const toggleAllShown = (e: { target: { checked: boolean } }) => {
    const newShowAnalysis: Record<string, boolean> = {};
    const setAllAnalysis = (questions: any[], checked: boolean) => {
      questions?.forEach((question) => {
        newShowAnalysis[question._id] = checked;
        if (question.children?.length) {
          setAllAnalysis(question.children, checked);
        }
      });
    };
    setAllAnalysis(data?.list, e.target.checked);
    setShowAnalysis(newShowAnalysis);
  };

  /** 试题录入 */
  const handleAddQuestion = () => {
    if (selectedUnitHours) {
      childRef?.current?.initData?.(undefined);
      childRef?.current?.show?.();
      setIsReadonly(false);
    } else {
      message.warning('请先选择单元课时');
    }
  };
  /** 试题导入 */
  const handleUploadQuestion = () => {
    setUploadModalOpen(true);
  };

  useEffect(() => {
    if (pagination.current && pagination.pageSize) {
      getAllQuestion();
    }
  }, [
    pagination.current,
    pagination.pageSize,
    searchTopicInfo,
    subjectInfo,
    selectedUnitHours,
    currentCatalog,
  ]);

  useEffect(() => {
    if (subjectInfo && subjectInfo.catalog?.parent_id) {
      setSelectedUnitHours(true);
    } else {
      setSelectedUnitHours(false);
    }
  }, [subjectInfo?.catalog]);

  return (
    <>
      <QuestionContext.Provider
        value={{
          readonly: isReadonly,
          subjectId: subjectInfo?.subject?.id,
          catalogId: currentCatalog?.id,
        }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <CatalogTreeWithHead
              level="volume"
              onSelect={(info: any) => {
                setPagination({ current: 1, pageSize: 10 });
                setCurrentCatalog({
                  id: info?.catalog?.id,
                  name: info?.catalog?.title,
                });
                setSubjectInfo(info);
              }}
            />
          </Col>
          <Col span={18}>
            <div className={styles.questionBox}>
              <CommonCard
                title="作业题库管理"
                className={styles.addQuestionBtn}
              >
                <ConditionalRender
                  hasAccess={selectedUnitHours && !readonly}
                  accessComponent={
                    <Space>
                      <Button
                        onClick={handleUploadQuestion}
                        icon={<VerticalAlignBottomOutlined />}
                      >
                        批量导入
                      </Button>
                      <Button
                        type="primary"
                        onClick={handleAddQuestion}
                        icon={<PlusOutlined />}
                      >
                        试题录入
                      </Button>
                    </Space>
                  }
                />
              </CommonCard>
              <div className={styles.searchTopicBoxWrapper}>
                <SearchTopicBox
                  subjectId={subjectInfo?.subject?.id}
                  className={styles.searchTopicBox}
                  onChange={handleSearch}
                  showAll={INDEPENDENT_QUESTION_BANK}
                  showDifficulty
                  showTypes
                  showSmart
                  showKeyword
                  showTag
                />
                <CommonCard title={`题目总数（${data?.total ?? 0} 道）`}>
                  <ConditionalRender
                    hasAccess={INDEPENDENT_QUESTION_BANK}
                    accessComponent={
                      <Button color="default" variant="link">
                        批量标注
                      </Button>
                    }
                  />
                  <ConditionalRender
                    hasAccess={INDEPENDENT_QUESTION_BANK}
                    accessComponent={
                      <Button color="default" variant="link">
                        更新相似题数
                      </Button>
                    }
                  />
                  <Checkbox
                    indeterminate={isPartiallyShown()}
                    checked={isAllShown()}
                    onChange={toggleAllShown}
                    disabled={!data?.list?.length}
                  >
                    全部解析
                  </Checkbox>
                  <ConditionalRender
                    hasAccess={!readonly}
                    accessComponent={
                      <Checkbox
                        indeterminate={
                          allSelect.length > 0 &&
                          allSelect.length < data?.list?.length
                        }
                        checked={
                          allSelect.length === data?.list?.length &&
                          data?.list?.length !== 0
                        }
                        onChange={(e) => {
                          const ids = data?.list.map((item: any) => item._id);
                          setAllSelect(e.target.checked ? ids : []);
                        }}
                        disabled={!!!data?.list?.length}
                      >
                        全选本页试题
                      </Checkbox>
                    }
                  />
                  <ConditionalRender
                    hasAccess={!(allSelect.length === 0)}
                    accessComponent={
                      <>
                        <ConditionalRender
                          hasAccess={INDEPENDENT_QUESTION_BANK}
                          accessComponent={
                            <Button
                              type="link"
                              icon={<FormOutlined />}
                              size="small"
                              onClick={() => {
                                setOpen(true);
                              }}
                            >
                              批量修改课时
                            </Button>
                          }
                        />
                        <Popconfirm
                          title="您确定要删除选中的试题吗？"
                          description="删除后无法恢复，请谨慎操作！"
                          onConfirm={batchDeleteQuestion}
                        >
                          <Button
                            type="link"
                            icon={<DeleteOutlined />}
                            size="small"
                            danger
                          >
                            批量删除
                          </Button>
                        </Popconfirm>
                      </>
                    }
                  />
                </CommonCard>

                <Spin tip="数据加载中..." spinning={loading}>
                  <div className={styles.questionListWrapper}>
                    <TopicList
                      data={data}
                      onreload={getAllQuestion}
                      onSelect={(value: string, status: boolean) => {
                        if (status) {
                          setAllSelect([...allSelect, value]);
                        } else {
                          setAllSelect(
                            allSelect.filter((item) => item !== value),
                          );
                        }
                      }}
                      readonly={readonly}
                      onLookInfo={lookQuestion}
                      onDelete={deleteQuestion}
                      onEditInfo={onEditInfo}
                      showAnalysis={showAnalysis}
                      onShowAnalysis={(id: string) => {
                        setShowAnalysis({
                          ...showAnalysis,
                          [id]: !showAnalysis[id],
                        });
                      }}
                      allSelect={allSelect}
                      pagination={{
                        total: data?.total,
                        current: pagination?.current,
                        pageSize: pagination?.pageSize,
                        onChange: (page: any, pageSize: any) => {
                          setPagination({
                            current: page,
                            pageSize: pageSize,
                          });
                        },
                      }}
                    />
                  </div>
                </Spin>
              </div>
            </div>
          </Col>
        </Row>
        {/* 存放不在当前页面直接展示的组件 */}
        <>
          <ReviseClassHoursModal
            title="批量修改课时"
            onOk={async () => {
              return false;
            }}
            onCancel={() => {
              setOpen(false);
            }}
            open={open}
          />
          <LookQuestionModal
            open={lookQuestioOpen.open}
            info={lookQuestioOpen.data!}
            onCancel={() => {
              setLookQuestioOpen({
                open: false,
                data: undefined,
              });
            }}
          />
          <QuestionForm
            subjectId={editSubjectId ?? subjectInfo?.subject?.id}
            handleQuestionSubmit={submitQuestion}
            ref={childRef}
            refresh={async () => {
              await getAllQuestion();
            }}
          />
          <ModalUpload
            info={subjectInfo}
            open={uploadModalOpen}
            onClose={() => {
              setUploadModalOpen(false);
            }}
            onSuccess={async () => {
              setUploadModalOpen(false);
              await getAllQuestion();
            }}
            tableName={MONGO_MODEL_KEY.SCHOOL}
          />
        </>
      </QuestionContext.Provider>
    </>
  );
};
export default SchoolQuestionBank;
