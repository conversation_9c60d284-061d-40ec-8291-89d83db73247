/**
 * @Description: 依据权限判断是否展示组件
 */
import React from 'react';

interface ConditionalRenderProps {
  /** 权限 */
  hasAccess: boolean | undefined;
  /** 无权限时展示的组件，默认值：null */
  noAccessComponent?: React.ReactNode;
  /** 有权限时展示的组件 */
  accessComponent: React.ReactNode;
}

const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  hasAccess,
  accessComponent,
  noAccessComponent = null,
}) => {
  return hasAccess ? accessComponent : noAccessComponent;
};

export default ConditionalRender;
