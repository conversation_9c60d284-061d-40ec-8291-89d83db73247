import { Modal } from 'antd';
import { BraftItem, ChoiceItem, JudgeItem } from '../QuestionItem';
import styles from './index.less';

const Analysis = ({
  open,
  hideModal,
  data,
}: {
  open: boolean;
  hideModal: () => void;
  data?: any;
}) => {
  return (
    <Modal
      title="题目详情"
      open={open}
      onOk={hideModal}
      onCancel={hideModal}
      okText="确认"
      cancelText="取消"
      width={800}
      getContainer={false}
      classNames={{
        body: styles.analysisModal,
      }}
      footer={null}
    >
      <div className={styles.questionItem}>
        {data?.type === '单选题' || data?.type === '多选题' ? (
          <ChoiceItem data={{ type: data?.type, info: { question: data } }} />
        ) : data?.type === '判断题' ? (
          <JudgeItem data={{ type: data?.type, info: { question: data } }} />
        ) : (
          <BraftItem data={{ type: data?.type, info: { question: data } }} />
        )}
        <div className={`${styles.questionAnswerArea}`}>
          <div className={styles.questionAnswerItem}>
            <div className="questionPoint">【指标体系】</div>
            <div className="questionTitle">
              {data?.indicators?.map((item: any) => {
                return (
                  <span key={item.id} className={styles.questionItemZsd}>
                    {item.name}
                  </span>
                );
              })}
            </div>
          </div>
          <div className={styles.questionAnswerItem}>
            <div className="questionPoint">【适用对象】</div>
            <div className="questionTitle">
              {/* <Space size={'middle'}>
                {data?.identitys?.map((item: any) => {
                  const identity = dictionary?.档案身份?.find((v) => v.code === item?.identity);
                  return (
                    <span key={item.id} className={styles.questionItemZsd}>
                      {identity?.name}
                    </span>
                  );
                })}
              </Space> */}
            </div>
          </div>
          <div
            className={styles.questionAnswerItem}
            dangerouslySetInnerHTML={{
              __html: `<div class="questionPoint">【答案】</div><div class="questionTitle" style="display: ${
                data?.type === '填空题' ? 'flex' : 'block'
              };align-items:center,flex-wrap: wrap;">${
                data?.practive
                  ? data?.type === '填空题'
                    ? JSON.parse(data?.practive)?.map(
                        (v: { value: any }) => v.value,
                      )
                    : data?.practive
                  : ''
              }</div>`,
            }}
          />
          <div
            className={styles.questionAnswerItem}
            dangerouslySetInnerHTML={{
              __html:
                '<div class="questionPoint">【解析】</div><div class="questionTitle">' +
                (data?.analysis ? data?.analysis : '') +
                '</div>',
            }}
          />
        </div>
      </div>
    </Modal>
  );
};

export default Analysis;
