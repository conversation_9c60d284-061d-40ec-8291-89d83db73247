.customMenu {
  border-right: 1px solid #f0f0f0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>,
    'Helvetica Neue', Arial;
  font-size: 14px;
}

.menuItem {
  padding: 0 16px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &.selected {
    background: #f0f9ff;
    color: #1677ff;
    font-weight: 500;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background: #1677ff;
    }
  }
}

.arrowIcon {
  font-size: 10px;
  transition: transform 0.3s;

  &.open {
    transform: rotate(0);
  }
}

.subMenu {
  background: #fff;
  border-left: 1px solid #f0f0f0;
  margin-left: 16px;
}

.subMenuItem {
  padding: 0 16px 0 32px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  position: relative;

  &.selected {
    background: #f0f9ff;
    color: #1677ff;
    font-weight: 500;
    border-radius: 4px;

    &::before {
      content: '';
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: #1677ff;
      border-radius: 2px;
    }
  }
}
