const routes: IBestAFSRoute[] = [
  {
    path: '/',
    redirect: '/home',
    keepQuery: true,
  },
  {
    name: 'OAuth2登录回调',
    path: '/oauth2/callback',
    component: './OAuth2/Callback',
    layout: false,
  },
  {
    name: '本地登录',
    path: '/login',
    component: './Login',
    layout: false,
  },
  {
    name: '首页',
    path: '/home',
    // icon: 'home',
    component: './Home',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    layout: false,
    access: '010000',
  },
  {
    name: '课程方案管理',
    path: '/coursePlan',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent'],
    access: '020000',
    routes: [
      {
        name: '培养目标管理',
        path: '/coursePlan/adminTarget',
        component: './CoursePlan/AdminTarget',
        access: '020100',
      },
      {
        name: '方案原则管理',
        path: '/coursePlan/designPrinciple',
        component: './CoursePlan/DesignPrinciple',
        access: '020200',
      },
      {
        name: '课程学制管理',
        path: '/coursePlan/designCourseSystem',
        component: './CoursePlan/DesignCourseSystem',
        access: '020300',
      },
      {
        name: '课程科目管理',
        path: '/coursePlan/designCourseSubject',
        component: './CoursePlan/DesignCourseSubject',
        access: '020400',
      },
      {
        name: '课程设置管理',
        path: '/coursePlan/designCourseSetting',
        component: './CoursePlan/DesignCourseSetting',
        access: '020500',
      },
      {
        name: '教学时间管理',
        path: '/coursePlan/designCourseTime',
        component: './CoursePlan/DesignCourseTime',
        access: '020600',
      },
      {
        name: '实施机制管理',
        path: '/coursePlan/designCourseCarry',
        component: './CoursePlan/DesignCourseCarry',
        access: '020700',
      },
    ],
  },
  {
    name: '课程标准管理',
    path: '/courseStandard',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent'],
    access: '030000',
    routes: [
      {
        name: '课程性质管理',
        path: '/courseStandard/character',
        component: './CourseStandard/Character',
        access: '030100',
      },
      {
        name: '课程理念管理',
        path: '/courseStandard/idea',
        component: './CourseStandard/Idea',
        access: '030200',
      },
      {
        name: '课程目标管理',
        path: '/courseStandard/objective',
        component: './CourseStandard/Objective',
        access: '030300',
      },
      {
        name: '课程内容管理',
        path: '/courseStandard/content',
        component: './CourseStandard/Content',
        access: '030400',
      },
      {
        name: '学业质量管理',
        path: '/courseStandard/quality',
        component: './CourseStandard/Quality',
        access: '030500',
      },
      {
        name: '课程实施管理',
        path: '/courseStandard/carry',
        component: './CourseStandard/Carry',
        access: '030600',
      },
      {
        name: '课标附录管理',
        path: '/courseStandard/appendix',
        component: './CourseStandard/Appendix',
        access: '030700',
      },
    ],
  },
  {
    name: '学科教材管理',
    path: '/subjectTextbook',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    // access: '040000',
    routes: [
      {
        name: '义务教育学科管理',
        path: '/subjectTextbook/subject',
        component: './subjectTextbook/Subject',
        access: '040100',
      },
      {
        name: '出版单位管理',
        path: '/subjectTextbook/publisher',
        component: './subjectTextbook/Publisher',
        access: '040200',
      },
      {
        name: '教材版本管理',
        path: '/subjectTextbook/textbook',
        component: './subjectTextbook/Textbook',
        access: '040300',
      },
      {
        name: '教材名录库管理',
        path: '/subjectTextbook/textbookChecklist',
        component: './subjectTextbook/TextbookChecklist',
        access: '040400',
      },
      {
        name: '教材章节目录管理',
        path: '/subjectTextbook/catalog',
        component: './subjectTextbook/Catalog',
        access: '040500',
      },
      {
        name: '教材选用管理',
        path: '/subjectTextbook/choose',
        component: './subjectTextbook/Choose',
        access: '040600',
      },
    ],
  },
  {
    name: '省级作业设计实施管理',
    path: '/provincialDesign',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent'],
    access: '050000',
    routes: [
      {
        name: '作业目标管理',
        path: '/provincialDesign/objective',
        component: './ProvincialDesign/Objective',
        access: '050100',
      },
      {
        name: '设计理念管理',
        path: '/provincialDesign/idea',
        component: './ProvincialDesign/Idea',
        access: '050200',
      },
      {
        name: '基础型作业管理',
        path: '/provincialDesign/basic',
        component: './ProvincialDesign/Basic',
        access: '050300',
      },
      {
        name: '发展型作业管理',
        path: '/provincialDesign/develop',
        component: './ProvincialDesign/Develop',
        access: '050400',
      },
      {
        name: '拓展型作业管理',
        path: '/provincialDesign/extend',
        component: './ProvincialDesign/Extend',
        access: '050500',
      },
      {
        name: '课程实施要点管理',
        path: '/provincialDesign/implement',
        component: './ProvincialDesign/Implement',
        access: '050600',
      },
    ],
  },
  {
    name: '市级作业设计指南管理',
    path: '/municipalDesign',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent'],
    access: '060000',
    routes: [
      {
        name: '学科设计理念管理',
        path: '/municipalDesign/idea',
        component: './MunicipalDesign/Idea',
        access: '060100',
      },
      {
        name: '学科设计要求管理',
        path: '/municipalDesign/requirement',
        component: './MunicipalDesign/Requirement',
        access: '060200',
      },
      {
        name: '学科设计原则管理',
        path: '/municipalDesign/principle',
        component: './MunicipalDesign/Principle',
        access: '060300',
      },
      {
        name: '设计类型信息管理',
        path: '/municipalDesign/type',
        component: './MunicipalDesign/Type',
        access: '060400',
      },
      {
        name: '设计作业形式管理',
        path: '/municipalDesign/form',
        component: './MunicipalDesign/FormContent',
        access: '060500',
      },
      {
        name: '作业设计建议管理',
        path: '/municipalDesign/suggest',
        component: './MunicipalDesign/Suggest',
        access: '060600',
      },
      {
        name: '典型案例集管理',
        path: '/municipalDesign/case',
        component: './MunicipalDesign/Case',
        access: '060700',
      },
    ],
  },
  {
    name: '课时作业范本管理',
    path: '/homeworkTemplate',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: '070000',
    routes: [
      {
        name: '小学课时作业范本管理',
        path: '/homeworkTemplate/primarySchool',
        component: './TemplateManagement/PrimarySchool',
        access: '070100',
      },
      {
        name: '初中课时作业范本管理',
        path: '/homeworkTemplate/juniorSchool',
        component: './TemplateManagement/JuniorSchool',
        access: '070200',
      },
      {
        name: '高中课时作业范本管理',
        path: '/homeworkTemplate/highSchool',
        component: './TemplateManagement/HighSchool',
        access: '070300',
      },
      {
        name: '课时作业范本试题列表',
        path: '/homeworkTemplate/:schoolType/:id',
        component: './TemplateManagement/TemplateInfo',
        access: '070400',
        hideInMenu: true,
      },
      {
        name: '课时作业范本试题详情',
        path: '/homeworkTemplate/:schoolType/:id/detail',
        component: './TemplateManagement/TemplateDetail',
        hideInMenu: true,
      },
      {
        path: '/homeworkTemplate/',
        redirect: '/homeworkTemplate/primarySchool',
      },
    ],
  },
  {
    name: '课时作业范本编辑',
    path: '/homeworkTemplate/:schoolType/:id/edit*', // 使用*匹配剩余路径
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    component: './TemplateManagement/TemplateEdit',
    hideInMenu: true,
    layout: false,
  },
  {
    name: '课时作业范本详情编辑',
    path: '/homeworkTemplate/:schoolType/:id/:detail/edit*', // 使用*匹配剩余路径
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    component: './TemplateManagement/TemplateEdit',
    hideInMenu: true,
    layout: false,
  },
  {
    name: '达标检测范本管理',
    path: '/complianceTestTemplate',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: '080000',
    routes: [
      {
        name: '小学单元达标检测范本管理',
        path: '/complianceTestTemplate/primarySchool/unit',
        component: './ComplianceTestManagement/PrimarySchool/Unit',
        access: '080100',
      },
      {
        name: '小学期中期末达标检测范本管理',
        path: '/complianceTestTemplate/primarySchool/midsemester',
        component: './ComplianceTestManagement/PrimarySchool/Midsemester',
        access: '080200',
      },
      {
        name: '初中单元达标检测范本管理',
        path: '/complianceTestTemplate/juniorSchool/unit',
        component: './ComplianceTestManagement/JuniorSchool/Unit',
        access: '080300',
      },
      {
        name: '初中期中期末达标检测范本管理',
        path: '/complianceTestTemplate/juniorSchool/midsemester',
        component: './ComplianceTestManagement/JuniorSchool/Midsemester',
        access: '080400',
      },
      {
        name: '高中单元达标检测范本管理',
        path: '/complianceTestTemplate/highSchool/unit',
        component: './ComplianceTestManagement/HighSchool/Unit',
        access: '080500',
      },
      {
        name: '高中期中期末达标检测范本管理',
        path: '/complianceTestTemplate/highSchool/midsemester',
        component: './ComplianceTestManagement/HighSchool/Midsemester',
        access: '080600',
      },
      {
        name: '达标检测范本列表',
        path: '/complianceTestTemplate/:schoolType/:testType/:id',
        component: './ComplianceTestManagement/ComplianceTestInfo',
        access: '080700',
        hideInMenu: true,
      },
      {
        name: '达标检测范本详情',
        path: '/complianceTestTemplate/:schoolType/:testType/:id/detail',
        component: './ComplianceTestManagement/ComplianceTestDetail',
        hideInMenu: true,
      },
    ],
  },
  {
    name: '达标检测范本编辑',
    path: '/complianceTestTemplate/:schoolType/:testType/:id/edit*', // 使用*匹配剩余路径
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    component: './ComplianceTestManagement/ComplianceTestEdit',
    hideInMenu: true,
    layout: false,
  },
  {
    name: '达标检测范本详情编辑',
    path: '/complianceTestTemplate/:schoolType/:testType/:id/:detail/edit*', // 使用*匹配剩余路径
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    component: './ComplianceTestManagement/ComplianceTestEdit',
    hideInMenu: true,
    layout: false,
  },
  {
    name: '课时作业设计管理',
    path: '/homeworkDesign',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: '090000',
    routes: [
      {
        name: '小学课时作业设计管理',
        path: '/homeworkDesign/primarySchool',
        component: './HomeworkDesign/PrimarySchool',
        access: '090100',
      },
      {
        name: '初中课时作业设计管理',
        path: '/homeworkDesign/juniorSchool',
        component: './HomeworkDesign/JuniorSchool',
        access: '090200',
      },
      {
        name: '高中课时作业设计管理',
        path: '/homeworkDesign/highSchool',
        component: './HomeworkDesign/HighSchool',
        access: '090300',
      },
      {
        name: '课时作业设计试题列表',
        path: '/homeworkDesign/:schoolType/:id',
        component: './HomeworkDesign/HomeworkDesignInfo',
        hideInMenu: true,
      },
      {
        name: '课时作业设计试题详情',
        path: '/homeworkDesign/:schoolType/:id/detail',
        component: './HomeworkDesign/HomeworkDesignDetail',
        hideInMenu: true,
      },
    ],
  },
  {
    name: '课时作业设计编辑',
    path: '/homeworkDesign/:schoolType/:id/edit*', // 使用*匹配剩余路径
    component: './HomeworkDesign/HomeworkDesignEdit',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    hideInMenu: true,
    layout: false,
  },
  {
    name: '课时作业设计详情编辑',
    path: '/homeworkDesign/:schoolType/:id/:detail/edit*', // 使用*匹配剩余路径
    component: './HomeworkDesign/HomeworkDesignEdit',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    hideInMenu: true,
    layout: false,
  },
  {
    name: '达标检测设计管理',
    path: '/complianceTestDesign',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: '100000',
    routes: [
      {
        name: '小学单元达标检测设计管理',
        path: '/complianceTestDesign/primarySchoolUnit',
        component: './ComplianceTestDesign/PrimarySchoolUnit',
        access: '100100',
      },
      {
        name: '小学期中期末达标检测设计管理',
        path: '/complianceTestDesign/primarySchoolMidsemester',
        component: './ComplianceTestDesign/PrimarySchoolMidsemester',
        access: '100200',
      },
      {
        name: '初中单元达标检测设计管理',
        path: '/complianceTestDesign/juniorSchoolUnit',
        component: './ComplianceTestDesign/JuniorSchoolUnit',
        access: '100300',
      },
      {
        name: '初中期中期末达标检测设计管理',
        path: '/complianceTestDesign/juniorSchoolMidsemester',
        component: './ComplianceTestDesign/JuniorSchoolMidsemester',
        access: '100400',
      },
      {
        name: '高中单元达标检测设计管理',
        path: '/complianceTestDesign/highSchoolUnit',
        component: './ComplianceTestDesign/HighSchoolUnit',
        access: '100500',
      },
      {
        name: '高中期中期末达标检测设计管理',
        path: '/complianceTestDesign/highSchoolMidsemester',
        component: './ComplianceTestDesign/HighSchoolMidsemester',
        access: '100600',
      },
      {
        name: '达标检测设计列表',
        path: '/complianceTestDesign/:schoolType/:id',
        component: './ComplianceTestDesign/ComplianceTestInfo',
        hideInMenu: true,
      },
      {
        name: '达标检测设计详情',
        path: '/complianceTestDesign/:schoolType/:id/detail',
        component: './ComplianceTestDesign/ComplianceTestDetail',
        hideInMenu: true,
      },
    ],
  },
  {
    name: '达标检测设计编辑',
    path: '/complianceTestDesign/:schoolType/:id/edit*', // 使用*匹配剩余路径
    component: './ComplianceTestDesign/ComplianceTestEdit',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    hideInMenu: true,
    layout: false,
  },
  {
    name: '达标检测设计详情编辑',
    path: '/complianceTestDesign/:schoolType/:id/:detail/edit*', // 使用*匹配剩余路径
    component: './ComplianceTestDesign/ComplianceTestEdit',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    hideInMenu: true,
    layout: false,
  },
  {
    name: '作业组卷管理',
    path: '/testPaperManagement',
    // icon: 'home',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: '110000',
    routes: [
      {
        name: '手工组卷',
        path: '/testPaperManagement/manual',
        component: './TestPaperManagement/Manual',
        access: '110100',
      },
      {
        name: '手工组卷组卷详情',
        path: '/testPaperManagement/manual/detail/:id',
        component: './TestPaperManagement/Manual/Detail',
        access: '110200',
        hideInMenu: true,
      },
      {
        name: '自动组卷',
        path: '/testPaperManagement/auto',
        component: './TestPaperManagement/Auto',
        access: '110300',
      },
      {
        name: '自动组卷组卷详情',
        path: '/testPaperManagement/auto/detail/:id',
        component: './TestPaperManagement/Auto/Detail',
        access: '110400',
        hideInMenu: true,
      },
      {
        name: '组卷记录',
        path: '/testPaperManagement/:type/record/:id',
        component: './TestPaperManagement/Record',
        access: '110500',
        hideInMenu: true,
      },
    ],
  },
  {
    name: '作业题库管理',
    path: '/questionManagement',
    // icon: 'home',
    // component: './QuestionBank',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: '120000',
    routes: [
      {
        name: '题库录入管理',
        path: '/questionManagement/entry',
        component: './QuestionManagement/Entry',
        access: '120100',
      },
      {
        name: '题库知识点管理',
        path: '/questionManagement/knowledge',
        component: './QuestionManagement/Knowledge',
        access: '120200',
      },
      {
        name: '答案解析管理',
        path: '/questionManagement/analysis',
        component: './QuestionManagement/Analysis',
        access: '120300',
      },
      {
        name: '题库分类管理',
        path: '/questionManagement/classification',
        component: './QuestionManagement/Classification',
        access: '120400',
      },
      {
        name: '题库审核管理',
        path: '/questionManagement/audit',
        component: './QuestionManagement/Audit',
        access: '120500',
      },
      {
        name: '题库分层管理',
        path: '/questionManagement/hierarchy',
        component: './QuestionManagement/Hierarchy',
        access: '120600',
      },
    ],
  },
  {
    name: '作业统计分析',
    path: '/question/statistics',
    // icon: 'home',
    component: './Statistics',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: '130000',
  },
  {
    name: '基础数据管理',
    path: '/basicData',
    // icon: 'setting',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: '980000',
    routes: [
      {
        name: '学年学期',
        path: '/basicData/semester',
        component: './basicData/Semester',
        access: '980100',
      },
      {
        name: '行政区划',
        path: '/basicData/area',
        component: './basicData/Area',
        access: '980200',
      },
      {
        name: '数据字典',
        path: '/basicData/dictionarie',
        component: './basicData/Dictionarie',
        access: '980300',
      },
      {
        name: '题型维护',
        path: '/basicData/questionType',
        component: './basicData/QuestionType',
        access: '980400',
      },
      {
        name: '核心素养',
        path: '/basicData/knowledge',
        component: './basicData/Knowledge',
        access: '980500',
      },
      {
        name: '考察能力',
        path: '/basicData/ability',
        component: './basicData/Ability',
        access: '980600',
      },
      {
        name: '认知层次',
        path: '/basicData/level',
        component: './basicData/Level',
        access: '980700',
      },
      {
        name: '题目分类',
        path: '/basicData/classification',
        component: './basicData/Classification',
        access: '980800',
      },
      {
        name: '题目分层',
        path: '/basicData/hierarchy',
        component: './basicData/Hierarchy',
        access: '980900',
      },
    ],
  },
  {
    name: '系统管理',
    path: '/settings',
    // icon: 'setting',
    wrappers: ['@/wrappers/loginWithParent', '@/wrappers/globalData'],
    access: 'isLogin',
    // access: '990000',
    routes: [
      {
        name: '单位管理',
        path: '/settings/enterprises',
        // icon: 'user',
        component: './settings/Enterprises',
        access: '990100',
      },
      {
        name: '用户管理',
        path: '/settings/users',
        // icon: 'user',
        component: './settings/Users',
        access: '990200',
      },
      {
        name: '角色管理',
        path: '/settings/roles',
        // icon: 'team',
        component: './settings/Roles',
        access: '990300',
      },
      {
        name: '权限点维护',
        path: '/settings/permissions',
        component: './settings/Permissions',
        access: '990400',
      },
      {
        name: '系统功能维护',
        path: '/settings/features',
        component: './settings/Features',
        access: '990500',
      },
    ],
  },
  {
    path: '/*',
    component: './404',
  },
];

export default routes;
