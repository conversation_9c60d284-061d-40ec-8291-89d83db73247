import TitleBar from '@/components/TitleBar';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Pie } from '@ant-design/plots';
import { Table } from 'antd';

const columns = [
  {
    title: '难度',
    dataIndex: 'name',
    key: 'name',
    width: '160px',
    ellipsis: true,
  },
  {
    title: '题目数量',
    dataIndex: 'count',
    key: 'count',
    render: (_: any, record: any) => {
      return (
        <span>
          {record.count}（{record.rate}%）
        </span>
      );
    },
  },
];
const config = {
  data: [],
  angleField: 'count',
  colorField: 'name',
  label: {
    text: 'count',
    style: {
      fontWeight: 'bold',
    },
  },
  legend: {
    color: {
      title: false,
      position: 'right',
      rowPadding: 5,
    },
  },
  interaction: {
    tooltip: {
      render: (_: any, { title, items }: any) => {
        return (
          <div key={title}>
            <h4>{title}</h4>
            {items.map((item: any) => {
              const { value, color } = item;
              return (
                <div key={color}>
                  <div
                    style={{
                      margin: 0,
                      display: 'flex',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div>
                      <span
                        style={{
                          display: 'inline-block',
                          width: 6,
                          height: 6,
                          borderRadius: '50%',
                          backgroundColor: color,
                          marginRight: 6,
                        }}
                      ></span>
                      <span>题目数量</span>
                    </div>
                    <b>{value}</b>
                  </div>
                </div>
              );
            })}
          </div>
        );
      },
    },
  },
};
const DifficultyAnalysis = ({ data }: { data: any }) => {
  return (
    <div>
      <TitleBar
        prefix={false}
        title={
          <>
            <QuestionCircleOutlined />
            难度分析
          </>
        }
        actionDom={
          <div>
            <span>
              {data?.difficultyCoverage ? data?.difficultyCoverage + '%' : ''}
            </span>
          </div>
        }
      />
      <Table
        bordered
        size="small"
        dataSource={data?.difficulty || []}
        columns={columns}
        pagination={false}
      />
      {data?.difficulty?.length > 0 && (
        <div
          style={{
            width: 320,
            height: 300,
          }}
        >
          <Pie {...config} data={data?.difficulty || []} />
        </div>
      )}
    </div>
  );
};

export default DifficultyAnalysis;
