import { addUsers, getUsers, removeUsers } from '@/services/roles';
import { index } from '@/services/users';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Drawer, message } from 'antd';
import React, { useRef, useState } from 'react';
import styles from './BindUsers.less';

const BindUsers: React.FC<{
  open: boolean;
  current?: API.Role;
  saveHandl: () => void;
  cancleHandl: () => void;
}> = ({ open, current, saveHandl, cancleHandl }) => {
  const actionRef1 = useRef<ActionType>();
  const actionRef2 = useRef<ActionType>();
  const [selectedRowKeys1, setSelectedRowKeys1] = useState<React.Key[]>([]);
  const [selectedRowKeys2, setSelectedRowKeys2] = useState<React.Key[]>([]);

  const columns: ProColumns<API.User, 'text'>[] = [
    {
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      dataIndex: 'nickname',
      key: 'nickname',
      title: '姓名',
    },
    {
      dataIndex: 'username',
      key: 'username',
      title: '用户名',
    },
    {
      dataIndex: 'email',
      key: 'email',
      title: '邮箱',
    },
  ];

  const handleAddUsers = async () => {
    if (current && selectedRowKeys1.length) {
      const { errCode, msg } = await addUsers(current.id, {
        userIds: selectedRowKeys1 as number[],
      });
      if (errCode) {
        message.error(msg || '添加失败');
      } else {
        message.success('添加成功');
        setSelectedRowKeys1([]);
        actionRef1.current?.reload();
        actionRef2.current?.reload();
        saveHandl();
      }
    }
  };

  const handleDelUsers = async () => {
    if (current && selectedRowKeys2.length) {
      const { errCode, msg } = await removeUsers(current.id, {
        userIds: selectedRowKeys2 as number[],
      });
      if (errCode) {
        message.error(msg || '删除失败');
      } else {
        message.success('删除成功');
        setSelectedRowKeys2([]);
        actionRef1.current?.reload();
        actionRef2.current?.reload();
        saveHandl();
      }
    }
  };

  return (
    <Drawer
      title={current?.name}
      width={800}
      open={open}
      onClose={cancleHandl}
      destroyOnClose
      styles={{ body: { display: 'flex', flexDirection: 'column' } }}
    >
      <div>
        <ProTable<API.User>
          headerTitle="选择用户"
          rowKey="id"
          actionRef={actionRef1}
          size="small"
          search={{ filterType: 'light' }}
          columns={columns}
          pagination={{
            pageSize: 5,
          }}
          rowSelection={{
            alwaysShowAlert: true,
            selectedRowKeys: selectedRowKeys1,
            onChange: (_, selectedRows) => {
              setSelectedRowKeys1(selectedRows.map((item) => item.id));
            },
            getCheckboxProps: (record) => ({
              disabled: record.roles?.some((item) => item.id === current?.id),
            }),
          }}
          request={async (params) => {
            const { errCode, msg, data } = await index(params);
            if (errCode) {
              message.error(msg || '列表查询失败');
              return {
                success: true,
                data: [],
              };
            }
            return {
              success: true,
              data: data.list,
              total: data.total,
            };
          }}
        />
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '16%',
          borderBlock: '1px solid #aaa',
          paddingBlock: '16px',
        }}
      >
        <CaretDownOutlined
          className={styles.btn}
          disabled={selectedRowKeys1.length === 0}
          onClick={selectedRowKeys1.length === 0 ? undefined : handleAddUsers}
        />
        <CaretUpOutlined
          className={styles.btn}
          disabled={selectedRowKeys2.length === 0}
          onClick={selectedRowKeys2.length === 0 ? undefined : handleDelUsers}
        />
      </div>
      <div>
        <ProTable<API.User, { roleId?: number }>
          headerTitle="已选用户"
          rowKey="id"
          actionRef={actionRef2}
          size="small"
          search={{ filterType: 'light' }}
          columns={columns}
          pagination={{
            pageSize: 6,
          }}
          rowSelection={{
            alwaysShowAlert: true,
            selectedRowKeys: selectedRowKeys2,
            onChange: (_, selectedRows) => {
              setSelectedRowKeys2(selectedRows.map((item) => item.id));
            },
            getCheckboxProps: (record) => ({
              disabled:
                record.username === 'admin' && current?.name === '管理员',
            }),
          }}
          params={{ roleId: current?.id }}
          request={async (params) => {
            const { roleId, ...query } = params;
            if (!roleId) {
              return {
                success: true,
                data: [],
                total: 0,
              };
            }
            const { errCode, msg, data } = await getUsers(roleId, query);
            if (errCode) {
              message.error(msg || '用户列表查询失败');
              return {
                success: false,
                data: [],
                total: 0,
              };
            }
            return {
              success: true,
              data: data.list,
              total: data.total,
            };
          }}
        />
      </div>
    </Drawer>
  );
};

export default BindUsers;
