import CustomCollapse from '@/components/CustomCollapse';
import { convertNumber } from '@/utils/calc';
import {
  FormListActionType,
  ModalForm,
  ProFormList,
  ProFormText,
} from '@ant-design/pro-components';
import React, { useRef } from 'react';
import BatchGeneration from './BatchGeneration';

const CreateModal: React.FC<{
  parent?: API.TextbookCatalog;
  open: boolean;
  okHandler: (list: { title: string; sort_order: number }[]) => Promise<void>;
  cancelHandler: () => void;
}> = ({ parent, open, okHandler, cancelHandler }) => {
  const actionRef = useRef<FormListActionType<API.TextbookCatalog>>();

  const createHandler = ({
    num,
    autoCreateTitle,
    titleRule,
    titleName,
  }: {
    num: number;
    autoCreateTitle: boolean;
    titleRule: 'upperCase' | 'lowerCase';
    titleName: string;
  }) => {
    // 清空列表
    while (actionRef.current?.get(0)) {
      actionRef.current?.remove(0);
    }
    // 添加列表
    for (let i = 0; i < num; i++) {
      actionRef.current?.add({
        title: autoCreateTitle
          ? `第${
              titleRule === 'upperCase' ? convertNumber(i + 1) : i + 1
            }${titleName} `
          : '',
      });
    }
  };

  return (
    <ModalForm<{ list: { title: string }[] }>
      title={`创建目录${parent ? `(${parent.title})` : ''}`}
      open={open}
      grid
      layout="horizontal"
      labelCol={{ flex: '6em' }}
      modalProps={{
        destroyOnClose: true,
        onClose: cancelHandler,
        onCancel: cancelHandler,
        styles: {
          body: {
            padding: '0 10px',
            maxHeight: '70vh',
            overflowY: 'auto',
          },
        },
      }}
      onFinish={async (formData) => {
        okHandler(
          formData.list.map((item, index) => ({
            title: item.title,
            sort_order: index + 1,
          })),
        );
      }}
    >
      <CustomCollapse
        items={[
          {
            key: '1',
            label: '批量生成',
            children: <BatchGeneration createHandler={createHandler} />,
          },
        ]}
        style={{ width: '100%', marginBottom: '24px' }}
      />
      <ProFormList<API.TextbookCatalog>
        label="目录列表"
        name="list"
        max={20}
        actionRef={actionRef}
        rules={[
          {
            required: true,
            validator: (_rule, value, callback) => {
              if (!value || value.length === 0) {
                callback('目录列表不能为空');
              } else {
                callback();
              }
            },
          },
        ]}
      >
        <ProFormText
          key="title"
          name="title"
          rules={[{ required: true, message: '请输入目录名称！' }]}
        />
      </ProFormList>
    </ModalForm>
  );
};

export default CreateModal;
