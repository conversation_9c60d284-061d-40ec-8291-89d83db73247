/*
 * @Description: 单位管理
 * @Date: 2025-02-12 17:49:16
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-26 11:24:36
 */
import { AreaState } from '@/models/area';
import { DictionarieState } from '@/models/dictionarie';
import { EnterpriseState } from '@/models/enterprise';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { AnyAction, connect, Dispatch } from '@umijs/max';
import { Button, Image, Popconfirm, Space, TreeSelect } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import EditModal from './EditModal';

const Enterprise: React.FC<{
  area: AreaState;
  dictionarie: DictionarieState;
  enterprise: EnterpriseState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ area, dictionarie, enterprise, dispatch }) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Enterprise | undefined>(undefined);

  const handleSave = async (values: API.Enterprise) => {
    if (current) {
      const { id, ...info } = values;
      dispatch({
        type: 'enterprise/update',
        payload: {
          id,
          info,
        },
      });
    } else {
      dispatch({
        type: 'enterprise/add',
        payload: values,
      });
    }
  };

  const handleDel = async (record: API.Enterprise) => {
    const { id } = record;
    dispatch({
      type: 'enterprise/remove',
      payload: {
        id,
      },
    });
  };

  const columns: ProColumns<API.Enterprise, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'LOGO',
      dataIndex: 'logo',
      valueType: 'image',
      key: 'logo',
      align: 'center',
      search: false,
      render(_dom, entity) {
        return (
          <Image
            src={entity?.logo}
            alt={entity.name}
            style={{ width: '60px' }}
          />
        );
      },
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      filters: dictionarie.list
        .filter((item) => item.type === 'enterprise_type')
        .map((item) => ({
          text: item.name,
          value: item.code,
        })),
      render: (text) => {
        return dictionarie.list
          .filter((item) => item.type === 'enterprise_type')
          .find((item) => item.code === text)?.name;
      },
      search: false,
    },
    {
      title: '学制',
      dataIndex: 'school_system',
      key: 'school_system',
      filters: dictionarie.list
        .filter((item) => item.type === 'school_system')
        .map((item) => ({
          text: item.name,
          value: item.code,
        })),
      render: (text) => {
        return dictionarie.list
          .filter((item) => item.type === 'school_system')
          .find((item) => item.code === text)?.name;
      },
      search: false,
    },
    {
      title: '所属省',
      dataIndex: 'province_name',
      key: 'province_name',
      search: false,
    },
    {
      title: '所属省',
      dataIndex: 'province',
      key: 'province',
      hidden: true,
      renderFormItem: () => (
        <TreeSelect placeholder="请选择" treeData={area.treeData} allowClear />
      ),
    },
    {
      title: '所属市',
      dataIndex: 'city_name',
      key: 'city_name',
      search: false,
    },
    {
      title: '所属市',
      dataIndex: 'city',
      key: 'city',
      hidden: true,
      renderFormItem: () => (
        <TreeSelect placeholder="请选择" treeData={area.treeData} allowClear />
      ),
    },
    {
      title: '所属区/县',
      dataIndex: 'area_name',
      key: 'area_name',
      search: false,
    },
    {
      title: '所属区/县',
      dataIndex: 'area',
      key: 'area',
      hidden: true,
      renderFormItem: () => (
        <TreeSelect placeholder="请选择" treeData={area.treeData} allowClear />
      ),
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setModalVisible(false);
  }, [enterprise]);

  return (
    <>
      <ProTable<API.Enterprise>
        actionRef={actionRef}
        rowKey="id"
        headerTitle="入驻学校/单位"
        columns={columns}
        dataSource={enterprise.currentList}
        onChange={(_pagination, filters, sorter) => {
          dispatch({
            type: 'enterprise/onChange',
            payload: {
              filters,
              sorter,
            },
          });
        }}
        beforeSearchSubmit={(params) => {
          dispatch({
            type: 'enterprise/onSearch',
            payload: params,
          });
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
        options={false}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ area, dictionarie, enterprise, loading }) => ({
  area,
  dictionarie,
  enterprise,
  loading,
}))(Enterprise);
