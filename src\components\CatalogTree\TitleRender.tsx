import { EllipsisOutlined } from '@ant-design/icons';
import { Dropdown, message, Popconfirm, Typography } from 'antd';
import { ItemType } from 'antd/es/menu/interface';
import { cleanChildren, handleDel } from './methods';
import { CatalogTreeNode } from './typings';

type TitleRenderProps = {
  proxyType?: string;
  /** 当前目录节点 */
  node: CatalogTreeNode;
  actived?: boolean;
  /** 新增目录方法 */
  addHandler: (node: CatalogTreeNode, action: 'insert' | 'append') => void;
  /** 编辑目录方法 */
  editHandler: (node: CatalogTreeNode) => void;
  /** 批量创建方法 */
  batchGenerationHandler: (node: CatalogTreeNode) => void;
  /** 数据发生变化的回调，方便调用着刷新渲染 */
  onChange: () => void;
  /** 最大层级 */
  maxLevel?: number;
};
const TitleRender: React.FC<TitleRenderProps> = ({
  proxyType,
  node,
  actived,
  addHandler,
  editHandler,
  batchGenerationHandler,
  onChange,
  maxLevel,
}) => {
  const { id, title, level, children } = node;
  const canOptionChildren = !maxLevel || level < maxLevel;
  const menus: ItemType[] = [
    {
      key: 'edit',
      label: (
        <a
          onClick={() => {
            editHandler(node);
          }}
        >
          编辑
        </a>
      ),
    },
    {
      key: 'insert',
      label: (
        <a
          onClick={() => {
            addHandler(node, 'insert');
          }}
        >
          向前插入同级
        </a>
      ),
    },
    {
      key: 'add',
      label: (
        <a
          onClick={() => {
            addHandler(node, 'append');
          }}
        >
          向后插入同级
        </a>
      ),
    },
  ];
  if (canOptionChildren) {
    if (children?.length) {
      menus.push({
        key: 'clearChildren',
        label: (
          <Popconfirm
            title="确认清空"
            description={
              <Typography.Paragraph>
                确认清空<Typography.Text mark>{title}</Typography.Text>
                下的所有目录吗？
              </Typography.Paragraph>
            }
            onConfirm={() => {
              cleanChildren(
                node,
                () => {
                  message.success('清空成功');
                  onChange();
                },
                proxyType,
              );
            }}
          >
            <a style={{ color: '#ff4d4f' }}>清空下一级</a>
          </Popconfirm>
        ),
      });
    } else {
      menus.push({
        key: 'batchGeneration',
        label: (
          <a
            onClick={() => {
              batchGenerationHandler(node);
            }}
          >
            批量创建下一级
          </a>
        ),
      });
    }
  }
  menus.push({
    key: 'del',
    label: (
      <Popconfirm
        title="确认删除"
        description={
          <Typography.Paragraph>
            确认删除<Typography.Text mark>{title}</Typography.Text>吗？
          </Typography.Paragraph>
        }
        onConfirm={() => {
          handleDel(
            id!,
            () => {
              message.success('删除成功');
              onChange();
            },
            proxyType,
          );
        }}
      >
        <a style={{ color: '#ff4d4f' }}>删除</a>
      </Popconfirm>
    ),
  });

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <div>{title}</div>
      <div
        className="options"
        onClick={(e) => {
          // 禁止冒泡
          if (actived) {
            e.stopPropagation();
          }
        }}
      >
        <Dropdown
          menu={{
            items: menus,
          }}
          trigger={['click']}
        >
          <a>
            <EllipsisOutlined />
          </a>
        </Dropdown>
      </div>
    </div>
  );
};

export default TitleRender;
