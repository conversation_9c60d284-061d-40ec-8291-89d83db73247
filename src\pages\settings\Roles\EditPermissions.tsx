import { FeatureState } from '@/models/feature';
import { RoleState } from '@/models/role';
import { index as permissionsIndex } from '@/services/permissions';
import { addPermissions, delPermissions } from '@/services/roles';
import { ProCard } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Divider, Drawer, message, Table, Tag, Tree } from 'antd';
import React, { useEffect, useState } from 'react';

const EditPermissions: React.FC<{
  open: boolean;
  current?: API.Role;
  saveHandl: () => void;
  cancleHandl: () => void;
  feature: FeatureState;
  role: RoleState;
}> = ({ open, current, saveHandl, cancleHandl, feature, role }) => {
  // 当前选中的功能点
  const [currentFeature, setCurrentFeature] = useState<API.Feature>();
  // 当前功能点的权限列表
  const [permissionList, setPermissionList] = useState<API.Permission[]>([]);

  // 当前角色的权限
  const permissions =
    role.list
      .find((r) => r.id === current?.id)
      ?.permissions?.sort((a, b) =>
        a.featureCode.localeCompare(b.featureCode),
      ) || [];

  useEffect(() => {
    if (currentFeature) {
      (async () => {
        const { errCode, msg, data } = await permissionsIndex({
          featureCode: currentFeature.code,
        });
        if (errCode) {
          message.error(msg || '权限列表查询失败');
        }
        setPermissionList(data?.list || []);
      })();
    }
    setPermissionList([]);
  }, [currentFeature]);

  return (
    <Drawer
      title={current?.name}
      width={1000}
      open={open}
      onClose={cancleHandl}
      destroyOnClose
      styles={{ body: { display: 'flex', flexDirection: 'column' } }}
    >
      <div style={{ minHeight: '100px', overflow: 'auto' }}>
        {permissions.map((per) => (
          <Tag
            key={per.id}
            style={{ margin: '5px 8px' }}
          >{`${per.feature?.name}-${per.name}`}</Tag>
        ))}
      </div>
      <Divider />
      <ProCard split="vertical">
        <ProCard colSpan="30%">
          <Tree
            treeData={feature.treeData}
            onSelect={(keys) => {
              if (keys.length) {
                setCurrentFeature(
                  feature.list.find((item) => item.code === keys[0]),
                );
              } else {
                setCurrentFeature(undefined);
              }
            }}
          />
        </ProCard>
        <ProCard title={currentFeature?.name}>
          <Table<API.Permission>
            rowKey="id"
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: permissions.map((p) => p.id),
              onSelect: async (record, selected) => {
                if (!current) return;
                let errCode, msg;
                if (selected) {
                  const res = await addPermissions(current.id, {
                    permissionIds: [record.id],
                  });
                  ({ errCode, msg } = res);
                } else {
                  const res = await delPermissions(current.id, {
                    permissionIds: [record.id],
                  });
                  ({ errCode, msg } = res);
                }
                if (errCode) {
                  message.error(msg || '权限点删除失败');
                } else {
                  message.success('操作成功');
                  saveHandl();
                }
              },
              onSelectAll: async (selected, _records, changeRows) => {
                if (!current) return;
                let errCode, msg;
                if (selected) {
                  const res = await addPermissions(current.id, {
                    permissionIds: changeRows
                      .filter((p) => !!p)
                      .map((p) => p.id),
                  });
                  ({ errCode, msg } = res);
                } else {
                  const res = await delPermissions(current.id, {
                    permissionIds: changeRows
                      .filter((p) => !!p)
                      .map((p) => p.id),
                  });
                  ({ errCode, msg } = res);
                }
                if (errCode) {
                  message.error(msg || '权限点删除失败');
                } else {
                  message.success('操作成功');
                  saveHandl();
                }
              },
              getCheckboxProps: (record) => ({
                // 管理员必须保留角色管理的管理权限，否则无法调整权限，相当于自杀
                disabled:
                  current?.name === '管理员' &&
                  currentFeature?.name === '角色管理' &&
                  record.name === '管理',
              }),
            }}
            columns={[
              {
                title: '权限点',
                dataIndex: 'name',
                align: 'center',
              },
              {
                title: '说明',
                dataIndex: 'description',
              },
            ]}
            dataSource={permissionList}
          />
        </ProCard>
      </ProCard>
    </Drawer>
  );
};

export default connect(({ feature, role }) => ({ feature, role }))(
  EditPermissions,
);
