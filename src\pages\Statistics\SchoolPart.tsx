import { useModel } from '@umijs/max';
import {
  AssignmentsTotal,
  DifficultyAnalysis,
  DurationAnalysis,
  QuantitativeAnalysis,
  QuestionsTotalTrend,
  TrendHomeworkQuantity,
} from './components/Chart';
import TeacherAssignmentsTotal from './components/Chart/TeacherAssignmentsTotal';
import StatisticsContext from './components/Context';

const SchoolPart = ({ params }: { params: any }) => {
  const { initialState } = useModel('@@initialState');
  return (
    <StatisticsContext.Provider
      value={{
        info: {
          ...params.info,
          schoolType: initialState?.grade_section_code,
        },
        options: params.options,
      }}
    >
      <AssignmentsTotal
        span={12}
        type="school"
        title={'全校分科目作业次数分析'}
      />
      <TeacherAssignmentsTotal span={12} title={'全校各科目教师作业次数分析'} />
      <TrendHomeworkQuantity
        span={24}
        type="school"
        title="全校月度作业数量趋势"
      />
      <DurationAnalysis
        span={24}
        type="school"
        title="各年级科目教师作业时长分析"
      />
      <QuantitativeAnalysis
        span={12}
        type="school"
        title="全校各年级分科目作业时长分析"
      />
      <DifficultyAnalysis
        span={12}
        type="school"
        title="全校各年级分科目试题数量分析"
      />
      <QuestionsTotalTrend
        span={24}
        type="school"
        title="全校月度试题数量趋势"
      />
    </StatisticsContext.Provider>
  );
};
export default SchoolPart;
