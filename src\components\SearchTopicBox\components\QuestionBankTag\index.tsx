import { Input } from 'antd';
import React from 'react';
import styles from './index.less';

const { Search } = Input;

interface QuestionBankTagProps {
  onChange?: (value: string) => void;
}

const QuestionBankTag: React.FC<QuestionBankTagProps> = ({ onChange }) => {
  const [value, setValue] = React.useState<string | null>(null);
  React.useEffect(() => {
    if (onChange && typeof value === 'string' && value.trim().length > 0) {
      onChange(value.trim());
    }
  }, [value]);
  return (
    <div className={styles.wapper}>
      <label>类题标签：</label>
      <Search
        placeholder="请输入类题标签搜索"
        allowClear
        onSearch={(newValue) => {
          setValue(newValue.trim());
        }}
      />
    </div>
  );
};
export default QuestionBankTag;
