import selectImg from '@/assets/noData.png';
import { useCurrentAccess } from '@/common/useCurrentAccess';
import CatalogTree from '@/components/CatalogTree';
import CommonCard from '@/components/CommonCard';
import CommonDesc from '@/components/CommonDesc';
import ConditionalRender from '@/components/ConditionalRender';
import QuestionList from '@/components/HomeworkDesign/QuestionList';
import { MONGO_MODEL_KEY } from '@/pages/QuestionManagement/Entry';
import ModalUpload from '@/pages/QuestionManagement/Entry/ModalUpload';
import { templateHomeworkIndex } from '@/services/class_work_detail';
import { getSSOToken } from '@/utils/auth';
import {
  PlusOutlined,
  UnorderedListOutlined,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import { Link, history, useLocation, useParams } from '@umijs/max';
import { Breadcrumb, Button, Col, Row, Space, Spin, message } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import HomeworkTemplateEdit from '../TemplateDetail';
import styles from './index.less';

const HomeworkTemplateInfo: React.FC = () => {
  const { id } = useParams();
  const { isSchoolAdmin, isSysAdmin } = useCurrentAccess();
  const [uploadModalOpen, setUploadModalOpen] = useState<boolean>(false);

  const { state: info, pathname } = useLocation() as {
    state: API.TemplateWork;
    pathname: string;
  };
  const [ksInfo, setKsInfo] = useState<any>();
  const [ksId, setKsId] = useState<number>();
  const [loading, setLoading] = useState<boolean>(false);
  const [isCheck, setIsCheck] = useState<boolean>(false);
  const [homework, setHomework] = useState<any>([]);
  const [curHomework, setCurHomework] = useState<any>();
  const isHidden =
    (info?.type === '内置' && !isSysAdmin) ||
    (info?.type === '自定义' && !isSchoolAdmin);
  /** 获取数据 */
  const getTemplateInfo = async () => {
    setLoading(true);
    const { errCode, data, msg } = await templateHomeworkIndex({
      class_work_id: id,
      textbookCatalog_id: ksId,
      status: isHidden ? '发布' : undefined,
    });
    setLoading(false);
    if (errCode) {
      message.warning('数据获取失败,请稍后重试' + msg);
    }
    if (data?.list?.length) {
      setCurHomework(data?.list?.[data?.list?.length - 1]);
    } else {
      setCurHomework(null);
    }
    setHomework(data?.list ?? []);
  };
  const onSelect = (value: Partial<API.TextbookCatalog>) => {
    if (value?.level === 1) {
      setKsId(undefined);
      setKsInfo(undefined);
      return;
    }
    setKsInfo(value);
    setKsId(value.id);
  };

  /** 试题导入 */
  const handleUploadQuestion = () => {
    setUploadModalOpen(true);
  };
  useEffect(() => {
    if (id && ksId) {
      getTemplateInfo();
    }
  }, [id, ksId]);
  useEffect(() => {
    const handleFocus = () => {
      if (id && ksId) {
        // 添加页面可见性检查
        if (document.visibilityState === 'visible') {
          getTemplateInfo();
        }
      }
    };

    // 同时监听 visibilitychange 和 focus 事件
    document.addEventListener('visibilitychange', handleFocus);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleFocus);
      window.removeEventListener('focus', handleFocus);
    };
  }, [id, ksId]); // 添加依赖项
  return (
    <ConditionalRender
      hasAccess={isCheck}
      accessComponent={
        <HomeworkTemplateEdit
          curHomework={curHomework}
          setIsCheck={setIsCheck}
        />
      }
      noAccessComponent={
        <div className={styles.homeworkTemplateInfo}>
          <Breadcrumb
            items={[
              {
                title: (
                  <a
                    onClick={() => {
                      history.back();
                    }}
                  >
                    课时作业范本列表
                  </a>
                ),
              },
              {
                title: '课时作业范本详情',
              },
            ]}
          />
          <Spin spinning={loading} tip="数据加载中...">
            <div className={styles.container}>
              <Row gutter={16}>
                <Col span={6}>
                  <CommonCard
                    height={45}
                    background="#2979ff"
                    color="#fff"
                    style={{
                      marginBottom: 0,
                      borderRadius: '8px 8px 0 0',
                    }}
                    title={
                      <>
                        <span>
                          {info?.grade_section_name}
                          {info?.grade_name}
                        </span>
                      </>
                    }
                  />
                  <CommonCard
                    title={
                      <Space size="small">
                        <UnorderedListOutlined /> {info?.subject_name} ·{' '}
                        {info?.textbook_version} （{info?.volume}）
                      </Space>
                    }
                    height={45}
                    style={{
                      marginBottom: 0,
                      borderRadius: 0,
                    }}
                  />
                  <CatalogTree
                    readonly={true}
                    cardClassName={styles.catalogTree}
                    currentTextbookChecklist={{
                      id: info?.textbookChecklist_id,
                    }}
                    onSelect={onSelect}
                    // defaultSelectedNode={ksInfo}
                  />
                </Col>
                <Col span={18}>
                  {ksId ? (
                    <div>
                      <ConditionalRender
                        hasAccess={
                          info?.status === '发布' && homework?.length === 0
                        }
                        accessComponent={<></>}
                        noAccessComponent={
                          <CommonCard
                            activeTitle
                            title={
                              <ul className={styles.homeworkBread}>
                                {homework?.map((item: any, index: number) => (
                                  <li
                                    key={item.id}
                                    className={
                                      curHomework?.id === item.id
                                        ? styles.activeTitle
                                        : ''
                                    }
                                    onClick={() => {
                                      setCurHomework(item);
                                    }}
                                  >
                                    <span title={item.name}>
                                      作业{index + 1}
                                    </span>
                                  </li>
                                ))}
                                {info?.status === '草稿' && (
                                  <Link
                                    to={`${pathname}/edit?ddtab=true&subjectId=${
                                      info?.subject_id
                                    }&volumeId=${
                                      info?.textbookChecklist_id
                                    }&ksId=${ksId}&isSysAdmin=${isSysAdmin}&type=add&token=${getSSOToken()}`}
                                    target="_blank"
                                    type="text"
                                  >
                                    <Button type="link" icon={<PlusOutlined />}>
                                      新建作业
                                    </Button>
                                  </Link>
                                )}
                              </ul>
                            }
                          >
                            {info?.status === '草稿' && (
                              <Button
                                onClick={handleUploadQuestion}
                                icon={<VerticalAlignBottomOutlined />}
                              >
                                导入文档
                              </Button>
                            )}
                          </CommonCard>
                        }
                      />

                      <ConditionalRender
                        hasAccess={!!curHomework?.id}
                        accessComponent={
                          <QuestionList
                            homework={{ ...curHomework, isHidden }}
                            setIsCheck={setIsCheck}
                            homeworkKsid={curHomework?.textbookCatalog_id}
                            subjectId={info?.subject_id}
                            reload={getTemplateInfo}
                          />
                        }
                        noAccessComponent={
                          <div
                            className="commonWapper"
                            style={{
                              minHeight: 'calc(100vh - 210px)',
                              display: 'flex',
                              justifyContent: 'center',
                              paddingTop: '100px',
                            }}
                          >
                            <ConditionalRender
                              hasAccess={info?.status === '草稿'}
                              accessComponent={
                                <CommonDesc
                                  style={{
                                    margin: '0 auto',
                                  }}
                                  title="暂无作业"
                                  desc="请先添加作业"
                                  extra={
                                    <Link
                                      to={`${pathname}/edit?ddtab=true&subjectId=${
                                        info?.subject_id
                                      }&volumeId=${
                                        info?.textbookChecklist_id
                                      }&ksId=${ksId}&isSysAdmin=${isSysAdmin}&type=add&token=${getSSOToken()}`}
                                      target="_blank"
                                      type="text"
                                    >
                                      <Button type="primary">新增作业</Button>
                                    </Link>
                                  }
                                />
                              }
                              noAccessComponent={
                                <CommonDesc
                                  style={{
                                    margin: '0 auto',
                                  }}
                                  title="暂无作业"
                                />
                              }
                            />
                          </div>
                        }
                      />
                    </div>
                  ) : (
                    <div
                      className="commonWapper"
                      style={{
                        minHeight: 'calc(100vh - 142px)',
                        display: 'flex',
                        justifyContent: 'center',
                        paddingTop: '100px',
                      }}
                    >
                      <CommonDesc
                        style={{
                          margin: '0 auto',
                        }}
                        icon={selectImg}
                        title="请先选择章节课时"
                        desc="只能在章节课时中进行范本设计"
                      />
                    </div>
                  )}
                </Col>
              </Row>
            </div>
          </Spin>

          <ModalUpload
            info={{
              outterId: info?.id,
              fbname:
                ksInfo?.title + moment().format('YYYY年MM月DD日') + '作业',
              section: {
                name: info?.grade_section_name,
                code: info?.grade_section_code,
              },
              grade: {
                name: info?.grade_name,
                code: info?.grade_code,
              },
              subject: {
                subject: info?.subject_name,
                id: info?.subject_id,
              },
              textbook: {
                textbook_version: info?.textbook_version,
                id: info?.textbook_id,
              },
              volume: {
                volume: info?.volume,
                id: info?.textbookChecklist_id,
              },
              catalog: ksInfo,
            }}
            type="课时作业范本"
            open={uploadModalOpen}
            onClose={() => {
              setUploadModalOpen(false);
            }}
            onSuccess={async () => {
              setUploadModalOpen(false);
              getTemplateInfo();
            }}
            tableName={
              isSysAdmin
                ? MONGO_MODEL_KEY.SYSTEM
                : isSchoolAdmin
                ? MONGO_MODEL_KEY.SCHOOL
                : undefined
            }
          />
        </div>
      }
    />
  );
};
export default HomeworkTemplateInfo;
