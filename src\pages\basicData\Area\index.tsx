/*
 * @Description: 行政区划管理
 * @Date: 2025-02-17 12:50:42
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-19 16:05:30
 */
import { AreaState, TreeNode } from '@/models/area';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { AnyAction, connect, Dispatch } from '@umijs/max';
import { Button, Popconfirm, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import EditModal from './EditModal';
import { OptionsTreeStyled } from './index.styled-components';

const Area: React.FC<{ area: AreaState; dispatch: Dispatch<AnyAction> }> = ({
  area,
  dispatch,
}) => {
  const [currentSheng, setCurrentSheng] = useState<TreeNode | undefined>(
    area.treeData?.[0],
  );
  const [currentShi, setCurrentShi] = useState<TreeNode | undefined>(
    currentSheng?.children?.[0],
  );
  const [currentXian, setCurrentXian] = useState<TreeNode>();

  const [current, setCurrent] = useState<API.Area>();
  const [showEditModal, setShowEditModal] = useState<boolean>(false);

  const handleSave = async (values: API.Area) => {
    if (current) {
      const { code, ...info } = values;
      dispatch({
        type: 'area/update',
        payload: {
          code,
          info,
        },
      });
    } else {
      dispatch({
        type: 'area/add',
        payload: values,
      });
    }
  };

  const handleDel = async (code: string) => {
    dispatch({
      type: 'area/remove',
      payload: {
        code,
      },
    });
  };

  const titleRender = ({ title, key }: { title: string; key: string }) => (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <div>
        <div>{title}</div>
        <div style={{ fontSize: '10px', color: '#999' }}>({key})</div>
      </div>
      <div className="options">
        <EditOutlined
          onClick={() => {
            setCurrent(area.list.find((item) => item.code === key));
            setShowEditModal(true);
          }}
        />
        <Popconfirm
          title="确认删除"
          description={
            <Typography.Paragraph>
              确认删除<Typography.Text mark>{title}</Typography.Text>吗？
            </Typography.Paragraph>
          }
          onConfirm={() => {
            handleDel(key);
          }}
        >
          <DeleteOutlined style={{ color: '#ff4d4f' }} />
        </Popconfirm>
      </div>
    </div>
  );

  // 刷新页面显示的数据
  useEffect(() => {
    const sheng = area.treeData.find(
      (item) => item.code === currentSheng?.code,
    );
    setCurrentSheng(sheng);
    setCurrentShi(
      sheng?.children?.find((item) => item.code === currentShi?.code),
    );
    setShowEditModal(false);
  }, [area]);

  return (
    <ProCard
      title="行政区划"
      extra={
        <Button
          type="primary"
          onClick={() => {
            setCurrent(undefined);
            setShowEditModal(true);
          }}
        >
          新增
        </Button>
      }
      split="vertical"
      bordered
      headerBordered
      bodyStyle={{
        height: 'calc(100vh - 10.7rem)',
      }}
    >
      <ProCard title="省级">
        <div style={{ height: 'calc(100vh - 15.5rem)', overflowY: 'auto' }}>
          <OptionsTreeStyled
            blockNode
            treeData={area.treeData.map((d) => ({
              title: d.title,
              key: d.key,
            }))}
            titleRender={titleRender}
            selectedKeys={currentSheng ? [currentSheng.key] : []}
            onSelect={(keys) => {
              if (keys.length) {
                setCurrentSheng(
                  area.treeData.find((item) => item.key === keys[0]),
                );
              } else {
                setCurrentSheng(undefined);
                setCurrentShi(undefined);
                setCurrentXian(undefined);
              }
            }}
            multiple={false}
          />
        </div>
      </ProCard>
      <ProCard title="市级">
        <div style={{ height: 'calc(100vh - 15.5rem)', overflowY: 'auto' }}>
          <OptionsTreeStyled
            blockNode
            treeData={(currentSheng?.children || []).map((d) => ({
              title: d.title,
              key: d.key,
            }))}
            titleRender={titleRender}
            selectedKeys={currentShi ? [currentShi.key] : []}
            onSelect={(keys) => {
              if (keys.length) {
                setCurrentShi(
                  (currentSheng?.children || []).find(
                    (item) => item.key === keys[0],
                  ),
                );
              } else {
                setCurrentShi(undefined);
                setCurrentXian(undefined);
              }
            }}
            multiple={false}
          />
        </div>
      </ProCard>
      <ProCard title="区县级">
        <div style={{ height: 'calc(100vh - 15.5rem)', overflowY: 'auto' }}>
          <OptionsTreeStyled
            blockNode
            treeData={(currentShi?.children || []).map((d) => ({
              title: d.title,
              key: d.key,
            }))}
            titleRender={titleRender}
            selectedKeys={currentXian ? [currentXian.key] : []}
            onSelect={(keys) => {
              if (keys.length) {
                setCurrentXian(
                  (currentShi?.children || []).find(
                    (item) => item.key === keys[0],
                  ),
                );
              } else {
                setCurrentXian(undefined);
              }
            }}
            multiple={false}
          />
        </div>
      </ProCard>
      <EditModal
        open={showEditModal}
        info={current}
        onClose={() => setShowEditModal(false)}
        onSave={handleSave}
      />
    </ProCard>
  );
};

export default connect(({ area }) => ({ area }))(Area);
