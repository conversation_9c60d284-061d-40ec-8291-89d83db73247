.homeworkTemplateInfo {
  .container {
    margin-top: 16px;

    .catalogTree {
      border-radius: 0 0 8px 8px;
    }

    .homeworkBread {
      list-style: none;
      display: flex;
      margin: 0;
      align-items: center;
      justify-content: flex-start;
      padding: 0;

      li {
        margin-right: 16px;
        font-size: 14px;
        color: #333;
        line-height: 32px;
        cursor: pointer;
        width: 80px;
        text-align: center;
        background-color: #eee;
        border-radius: 4px;
        transform: skew(-20deg); /* 倾斜变换 */
        &.activeTitle {
          background-color: var(--primary-color);
          color: #fff;
        }
      }
    }
  }
}
