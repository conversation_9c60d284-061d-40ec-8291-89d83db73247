import { request } from '@umijs/max';
export async function downHtmlToWord(body: any) {
  return request<API.ResType<any>>('/export/htmlToWord', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

export async function downPaperToWord(body: any) {
  return request<API.ResType<any>>('/export/paper-to-word', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}
