import { SECTION_GRADE } from '@/constants';
import { DictionarieState } from '@/models/dictionarie';
import { convertSystemToGrade } from '@/utils/calc';
import { connect, useModel } from '@umijs/max';
import { Col, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import CityPart from './CityPart';
import FilterSelect from './components/BaseChart/FilterSelect';
import { HeaderChart } from './components/Chart';
import CountyPart from './CountyPart';
import styles from './index.less';
import SchoolPart from './SchoolPart';
interface StatisticsProps {
  dictionarie?: DictionarieState;
}
const Statistics: React.FC<StatisticsProps> = ({ dictionarie }) => {
  const { initialState } = useModel('@@initialState');
  const [selectedFilterData, setSelectedFilterData] = useState<any>();
  const [gradeOptions, setGradeOptions] = useState<any[]>([]);
  const [typeInfo, setTypeInfo] = useState<'city' | 'county' | 'school'>();
  const [oriTypeInfo, setOriTypeInfo] = useState<
    'city' | 'county' | 'school'
  >();
  const checkRole = (roleNames: string | string[]) => {
    const rolesToCheck = Array.isArray(roleNames) ? roleNames : [roleNames];
    return initialState?.roles?.some((item: { name: string }) =>
      rolesToCheck.includes(item.name),
    );
  };
  const rolePermissions = {
    isCityAdmin: checkRole('市管理员'),
    isCountyAdmin: checkRole('区管理员'),
    isSchoolAdmin: checkRole(['小学管理员', '初中管理员', '高中管理员']),
  };
  /** 选中学段后返回学段对应的年级 */
  useEffect(() => {
    const { county, schoolType } = selectedFilterData || {};
    if (county) {
      setTypeInfo('county');
    } else if (rolePermissions?.isCityAdmin) {
      setTypeInfo('city');
    }
    const baseOptions = dictionarie?.list?.filter(
      (item) => item?.type === 'grade',
    );
    const key = (
      rolePermissions.isSchoolAdmin
        ? schoolType || initialState?.enterprise?.school_system
        : schoolType
    ) as keyof typeof SECTION_GRADE;
    if (key) {
      // 过滤出对应学段的年级
      const gradeData = convertSystemToGrade('gradeCode', key, baseOptions);
      setGradeOptions(gradeData);
    } else {
      const newOptions = baseOptions?.map((v) => ({
        label: v.name,
        value: v.code,
      }));
      setGradeOptions([...(newOptions || [])]);
    }
  }, [selectedFilterData, dictionarie]);
  useEffect(() => {
    if (rolePermissions.isCityAdmin) {
      setTypeInfo('city');
      setOriTypeInfo('city');
    }
    if (rolePermissions.isCountyAdmin) {
      setTypeInfo('county');
      setOriTypeInfo('county');
    }
    if (rolePermissions.isSchoolAdmin) {
      setTypeInfo('school');
      setOriTypeInfo('school');
    }
  }, [initialState]);
  return (
    <div className={styles.statistics}>
      <Row gutter={[12, 12]}>
        <Col span={24}>
          <div className={styles.filter}>
            <span className={styles.filterTitle}>数据筛选</span>
            <FilterSelect
              mode="global"
              type={typeInfo}
              oriType={oriTypeInfo}
              onChange={(value) => {
                setSelectedFilterData(value);
              }}
            />
          </div>
        </Col>
        <HeaderChart type={typeInfo} searchCondition={selectedFilterData} />
        {typeInfo === 'city' && (
          <CityPart
            params={{
              info: selectedFilterData,
              options: gradeOptions,
            }}
          />
        )}
        {typeInfo === 'county' && (
          <CountyPart
            params={{
              info: selectedFilterData,
              options: gradeOptions,
            }}
          />
        )}
        {typeInfo === 'school' && (
          <SchoolPart
            params={{
              info: selectedFilterData,
              options: gradeOptions,
            }}
          />
        )}
      </Row>
    </div>
  );
};
export default connect(({ dictionarie }) => ({ dictionarie }))(Statistics);
