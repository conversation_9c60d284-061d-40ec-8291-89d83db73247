import { useCurrentAccess } from '@/common/useCurrentAccess';
import ConditionalRender from '@/components/ConditionalRender';
import { DictionarieState } from '@/models/dictionarie';
import { QuestionTypesState } from '@/models/questionTypes';
import {
  createQuestionExtendType,
  getQuestionExtendType,
  removeQuestionExtendType,
  updateQuestionExtendType,
} from '@/services/question_extend_type';
import { index } from '@/services/subjects';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  ProColumns,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { AnyAction, connect } from '@umijs/max';
import { Button, Form, message, Popconfirm, Space } from 'antd';
import React, { Dispatch, useEffect, useRef, useState } from 'react';

type QuestionTypeProps = {
  dictionarie: DictionarieState;
  questionTypes: QuestionTypesState;
  dispatch: Dispatch<AnyAction>;
};

type OptionType = { label: string; value: string | number; title?: string };

const QuestionType: React.FC<QuestionTypeProps> = ({
  dictionarie,
  dispatch,
}) => {
  const { isAdmin } = useCurrentAccess();
  const actionRef = useRef<ActionType>();
  const [options, setOptions] = useState<OptionType[]>([]);
  const [pagination, setPagination] = useState<{
    current: number;
    pageSize: number;
  }>({
    current: 1,
    pageSize: 10,
  });
  const [modal, setModal] = useState<{
    open: boolean;
    data: any;
  }>({
    open: false,
    data: undefined,
  });
  const [gradeList, setGradeList] = useState<OptionType[]>([]);
  /** 选中的学段 */
  const [selectedSection, setSelectedSection] = useState<string>('');

  const [form] = Form.useForm();

  useEffect(() => {
    const options = dictionarie.list
      .filter((item) => {
        return item.type === 'question_type';
      })
      .map((item) => {
        return { label: item.name, value: item.code };
      });
    setOptions(options ?? []);
  }, [dictionarie]);
  /** 编辑扩展题型 */
  const onEdit = async (values: any) => {
    const { errCode, msg } = await updateQuestionExtendType(values.id, values);
    if (errCode) {
      return message.warning(`编辑扩展题型失败 ${msg}`);
    }
    message.success('编辑扩展题型成功');
    actionRef.current?.reload();
    setModal({
      open: false,
      data: undefined,
    });
    dispatch({
      type: 'questionTypes/fetch',
      payload: { refresh: true },
    });
  };
  /** 提交扩展题型 */
  const onSubmit = async (values: any) => {
    if (modal.data) {
      return onEdit(values);
    }
    const { errCode, msg } = await createQuestionExtendType(values);
    if (errCode) {
      return message.warning(`创建扩展题型失败 ${msg}`);
    }
    message.success('创建扩展题型成功');
    setModal({
      open: false,
      data: undefined,
    });
    actionRef.current?.reload();
    dispatch({
      type: 'questionTypes/fetch',
      payload: { refresh: true },
    });
  };
  /** 删除扩展题型 */
  const onDelete = async (id: number) => {
    const { errCode, msg } = await removeQuestionExtendType(id);
    if (errCode) {
      return message.warning(`删除扩展题型失败 ${msg}`);
    }
    message.success('删除扩展题型成功');

    const shouldGoBack =
      actionRef.current?.pageInfo?.total === 1 ||
      (actionRef.current?.pageInfo?.current === pagination.current &&
        actionRef.current?.pageInfo?.pageSize === pagination.pageSize &&
        actionRef.current?.pageInfo?.total ===
          (pagination.current - 1) * pagination.pageSize + 1);

    if (shouldGoBack && pagination.current > 1) {
      setPagination((prev) => ({
        ...prev,
        current: prev.current - 1,
      }));
    }

    actionRef.current?.reload();
    dispatch({
      type: 'questionTypes/fetch',
      payload: { refresh: true },
    });
  };

  const gradeSectionMap = React.useMemo(() => {
    const map = new Map();
    dictionarie?.list
      ?.filter((item) => item.type === 'grade_section')
      .forEach((item) => {
        map.set(item.code, item.name);
      });
    return map;
  }, [dictionarie]);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      title: '序号',
      valueType: 'index',
      width: 48,
    },
    {
      title: '学段',
      align: 'center',
      dataIndex: 'section',
      search: false,
      render: (_, record) => {
        return gradeSectionMap.get(record?.gradeSection);
      },
    },
    {
      title: '扩展题型名称',
      align: 'center',
      dataIndex: 'name',
    },
    {
      title: '扩展题型编码',
      align: 'center',
      dataIndex: 'code',
      search: false,
    },
    {
      title: '源题型名称',
      align: 'center',
      dataIndex: 'sourceName',
    },
    {
      title: '源题型编码',
      align: 'center',
      dataIndex: 'sourceCode',
      search: false,
    },
    {
      title: '是否组合题型',
      align: 'center',
      dataIndex: 'isCompose',
      valueEnum: {
        true: '是',
        false: '否',
      },
    },
    {
      title: '操作',
      align: 'center',
      valueType: 'option',
      key: 'option',
      render: (_text, record) => {
        return (
          <ConditionalRender
            hasAccess={isAdmin}
            accessComponent={
              <Space>
                <Popconfirm
                  key="deletes"
                  title="修改扩展题型提示"
                  description={`修改后，已使用该题型的题目不会自动更新，请确认是否继续？`}
                  onConfirm={() => {
                    setModal({
                      open: true,
                      data: record,
                    });
                  }}
                >
                  <Button type="link" key="editable">
                    编辑
                  </Button>
                </Popconfirm>

                <Popconfirm
                  key="deletes"
                  title="删除扩展题型提示"
                  description="删除后将无法恢复，请确认该扩展题型未被任何题目使用！"
                  onConfirm={() => {
                    onDelete(record?.id);
                  }}
                >
                  <Button type="link" danger>
                    删除
                  </Button>
                </Popconfirm>
              </Space>
            }
          />
        );
      },
    },
  ];

  useEffect(() => {
    if (modal.data && modal.open) {
      // 获取学科列表数据
      const fetchSubjects = async () => {
        const { errCode, data } = await index({
          grade_section: modal.data.gradeSection,
        });
        if (!errCode && data?.list) {
          const subjectOptions = data.list.map((item) => ({
            label: item.subject,
            value: item.id,
          }));

          // 设置表单值，包括多选的 label
          form.setFieldsValue({
            ...modal.data,
            subjectId: modal.data.subjectId?.map((id: number) => {
              const found = subjectOptions.find((opt) => opt.value === id);
              return found ? found.value : id;
            }),
          });
        }
      };

      form.setFieldsValue(modal.data);
      if (modal.data.gradeSection) {
        setSelectedSection(modal.data.gradeSection);
        fetchSubjects();
      }
    }
  }, [modal.data, modal.open]);

  useEffect(() => {
    if (modal.open) {
      const res = dictionarie.list.filter(
        (item) => item.type === 'grade_section',
      );
      setGradeList(
        res.map((item) => {
          return { label: item.name, value: item.code };
        }),
      );
    }
  }, [modal.open]);

  return (
    <>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        request={async (params) => {
          const { current, pageSize, isCompose, sourceName, name } = params;
          const { errCode, msg, data } = await getQuestionExtendType({
            offset: Number((current || 1) - 1) * Number(pageSize || 10),
            limit: Number(pageSize || 10),
            isCompose: isCompose || undefined,
            sourceName: sourceName || undefined,
            name: name || undefined,
          });
          if (errCode) {
            message.warning(`获取扩展题型列表失败 ${msg}`);
            return { data: [], success: false, total: 0 };
          }
          return {
            data: data?.list ?? [],
            success: true,
            total: data.total,
          };
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          span: 6,
        }}
        options={false}
        form={{
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          showSizeChanger: true,
          current: pagination?.current,
          pageSize: pagination?.pageSize,
          onChange: (page, pageSize) => {
            setPagination({
              current: page,
              pageSize,
            });
          },
        }}
        dateFormatter="string"
        headerTitle="扩展题型"
        toolBarRender={() => [
          <ConditionalRender
            key="add"
            hasAccess={isAdmin}
            accessComponent={
              <Button
                icon={<PlusOutlined />}
                onClick={() => {
                  setModal({
                    open: true,
                    data: undefined,
                  });
                }}
                type="primary"
              >
                新增扩展题型
              </Button>
            }
          />,
        ]}
      />
      <ModalForm
        width={600}
        layout="horizontal"
        title={!!modal.data ? '编辑扩展题型' : '新增扩展题型'}
        form={form}
        labelCol={{ span: 6 }}
        labelAlign="left"
        open={modal.open}
        autoFocusFirstInput
        onFinish={onSubmit}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setModal({
              open: false,
              data: undefined,
            });
            setSelectedSection('');
          },
          styles: {
            body: {
              marginTop: 20,
            },
          },
        }}
        submitTimeout={2000}
      >
        <ProFormText name="id" hidden />
        <ProFormText name="sourceName" hidden />
        <ProFormSelect
          name="gradeSection"
          label="所属学段"
          showSearch
          options={gradeList}
          placeholder="请选择源题型"
          fieldProps={{
            onChange: (value: string) => {
              setSelectedSection(value);
              form.setFieldsValue({
                subjectId: [],
              });
            },
          }}
          rules={[{ required: true, message: '此项为必填项' }]}
        />

        <ProFormSelect
          disabled={!selectedSection}
          name="subjectId"
          mode="multiple"
          label="所属学科"
          showSearch
          params={{ grade_section: selectedSection }}
          request={async (params) => {
            const { errCode, msg, data } = await index(params);
            if (errCode) {
              message.warning(`获取学科列表失败 ${msg}`);
              return [];
            }
            return (
              data?.list?.map((item) => ({
                label: item.subject,
                value: item.id,
                key: item.id, // 添加 key 确保选项唯一性
              })) ?? []
            );
          }}
          placeholder="请选择学科"
          rules={[{ required: true, message: '此项为必填项' }]}
        />

        <ProFormSelect
          name="sourceCode"
          label="源题型"
          showSearch
          options={options}
          onChange={(_value, option: any) => {
            form.setFieldsValue({
              sourceName: option?.title,
            });
          }}
          placeholder="请选择源题型"
          rules={[{ required: true, message: '此项为必填项' }]}
        />
        <ProFormText
          name="name"
          label="扩展题型名称"
          tooltip="最长为 24 位，用于唯一标识"
          placeholder="请输入名称"
          rules={[{ required: true, message: '此项为必填项' }]}
        />

        <ProFormText
          readonly={!!modal.data}
          name="code"
          label="扩展题型编码"
          tooltip="最长为 24 位，用于唯一标识"
          placeholder="新增后不可编辑，请谨慎操作！"
          rules={[{ required: true, message: '此项为必填项' }]}
        />
        <ProFormRadio.Group
          name="isCompose"
          label="组合题型"
          initialValue={false}
          options={[
            { label: '是', value: true },
            { label: '否', value: false },
          ]}
          rules={[{ required: true, message: '此项为必填项' }]}
        />
      </ModalForm>
    </>
  );
};
export default connect(({ dictionarie, dispatch }) => ({
  dictionarie,
  dispatch,
}))(QuestionType);
