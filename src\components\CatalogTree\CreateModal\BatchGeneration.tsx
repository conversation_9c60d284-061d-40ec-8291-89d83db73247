import { Button, Col, Input, InputNumber, Segmented } from 'antd';
import { useState } from 'react';
import { RowStyled } from '../../../pages/subjectTextbook/Catalog/Styled';

const BatchGeneration: React.FC<{
  createHandler: (info: {
    num: number;
    autoCreateTitle: boolean;
    titleRule: 'upperCase' | 'lowerCase';
    titleName: string;
  }) => void;
}> = ({ createHandler }) => {
  const [num, setNum] = useState<number>(5);
  const [autoCreateTitle, setAutoCreateTitle] = useState<boolean>(true);
  const [titleRule, setTitleRule] = useState<'upperCase' | 'lowerCase'>(
    'upperCase',
  );
  const [titleName, setTitleName] = useState<string>('章');

  return (
    <>
      <RowStyled gutter={[16, 16]}>
        <Col>
          <label>创建个数</label>
          <InputNumber
            value={num}
            min={1}
            max={30}
            onChange={(v) => {
              setNum(Number(v));
            }}
          />
        </Col>
      </RowStyled>
      <RowStyled gutter={[16, 16]}>
        <Col>
          <label>生成标题</label>
          <Segmented<boolean>
            options={[
              {
                label: '生成',
                value: true,
              },
              {
                label: '不生成',
                value: false,
              },
            ]}
            value={autoCreateTitle}
            onChange={(v) => {
              setAutoCreateTitle(v);
            }}
          />
        </Col>
        {autoCreateTitle && (
          <>
            <Col>
              <label>序号规则</label>
              <Segmented<'upperCase' | 'lowerCase'>
                options={[
                  {
                    label: '大写',
                    value: 'upperCase',
                  },
                  {
                    label: '小写',
                    value: 'lowerCase',
                  },
                ]}
                value={titleRule}
                onChange={(v) => {
                  setTitleRule(v);
                }}
              />
            </Col>
            <Col>
              <label>标题名</label>
              <Input
                placeholder={`请输入，例如'章'、'节'等`}
                style={{ width: '16em' }}
                defaultValue={titleName}
                onChange={(e) => {
                  setTitleName(e.target.value);
                }}
              />
            </Col>
          </>
        )}
      </RowStyled>
      {autoCreateTitle && (
        <RowStyled>
          <Col span={24}>
            <label>预览</label>
            {autoCreateTitle
              ? `第${titleRule === 'upperCase' ? '一' : '1'}${titleName}`
              : ''}
          </Col>
        </RowStyled>
      )}
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <Button
          type="primary"
          onClick={() => {
            createHandler({
              num,
              autoCreateTitle,
              titleRule,
              titleName,
            });
          }}
        >
          开始生成
        </Button>
      </div>
    </>
  );
};

export default BatchGeneration;
