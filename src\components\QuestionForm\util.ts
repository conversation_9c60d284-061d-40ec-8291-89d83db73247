/** 将字典中数据转换为表单中select使用的数据格式 */
export const convertDictSelect = (data: any[]) => {
  if (data?.length) {
    const newData: any = [].map.call(data, (item: any) => {
      return {
        title: item.item_text ? item.item_text : item.name,
        label: item.item_text ? item.item_text : item.name,
        value: item.item_value ? item.item_value : item.code,
      };
    });
    return newData;
  }
  return [
    {
      value: 'null',
      label: '请选择',
    },
  ];
};
export const refillText = (form: any, type: string, e: any, option?: any) => {
  if (option) {
    form.setFieldValue(type + '_text', option.title);
  } else if (e) {
    form.setFieldValue(type + '_text', e.title);
  }
};
