declare namespace API {
  /**
   * 后台统一返回信息格式
   */
  type ResType<T = any> = {
    errCode: number;
    data: T;
    msg: string;
  };

  interface BaseTime {
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
  }

  /** 请求token 的参数 */
  type TokenQueryInfo = {
    code?: string;
    redirect_uri?: string;
  };

  type Area = {
    /** 行政区划编码,主键 */
    code: string;
    /** 行政区划名称 */
    name: string;
    /** 父级行政区划编码 */
    parentCode?: string;
  };

  type Enterprise = {
    /** 单位ID */
    id: number;
    /** 单位编号 */
    code: string;
    /** 单位名称 */
    name: string;
    /** 英文名称 */
    en_name?: string;
    /** 学制 */
    school_system?: string;
    /** LOGO */
    logo?: string;
    /** 单位类型 */
    type?: string;
    /** 省份编号 */
    province?: string;
    /** 省份名称 */
    province_name?: string;
    /** 城市编号 */
    city?: string;
    /** 城市名称 */
    city_name?: string;
    /** 区域编号 */
    area?: string;
    /** 区域名称 */
    area_name?: string;
    /** 联系人 */
    liaison?: string;
    /** 联系电话 */
    mobile?: string;
    /** 地址 */
    address?: string;
    /** 社会信用代码 */
    social_credit_code?: string;
    /** 官网地址 */
    official_website?: string;
    /** 邮箱地址 */
    email?: string;
    /** 传真地址 */
    fax?: string;
    /** 邮政编码 */
    postal_code?: string;
    /** 单位简介 */
    describe?: string;
    /** 备注信息 */
    remark?: string;
    /** 父级ID，集团校使用 */
    parent_id?: string;
  };

  interface User {
    id: number;
    /** 用户名（不能为空） */
    username: string;
    /** 密码（不能为空） */
    password: string;
    /** 昵称 */
    nickname?: string;
    /** 头像 */
    avatar?: string;
    /** 邮箱 */
    email?: string;
    /** 是否激活（默认为true） */
    isActive?: boolean;
    enterprise_id?: string;
    roles?: Role[];
    features?: Feature[];
    enterprise?: Enterprise;
    grade_section_code?: string;
  }

  type Dictionarie = {
    /** 字典编码 */
    code: number | string;
    /** 字典类型 */
    type: string;
    /** 字典名称 */
    name: string;
    /** 别名 */
    alias?: string;
    /** 字典描述（可选） */
    description?: string;
    /** 排序 */
    sortOrder: number;
    /** 状态 */
    status: number;
  };

  /**
   * 系统功能
   */
  type Feature = {
    /** 功能编号，主键 */
    code: string;
    /** 功能名称 */
    name: string;
    /** 父级功能编号（可选） */
    parentCode?: string;
    /** 功能图标（可选） */
    icon?: string;
    /** 功能路径 */
    path: string;
    /** 排序 */
    orderIndex: number;
    /** 描述（可选） */
    description?: string;
    permissions?: Permission[];
  };

  /**
   * 权限
   */
  type Permission = {
    id: number;
    /** 权限名称 */
    name: string;
    /** 权限描述 */
    description?: string;
    /** 关联的功能编号 */
    featureCode: string;
    feature?: Feature;
  };

  /**
   * 角色
   */
  type Role = {
    id: number;
    /** 角色名称 */
    name: string;
    /** 角色描述 */
    description?: string;
    users: User[];
    permissions: Permission[];
  };

  /** 学年学期 */
  type Semester = {
    /** ID，主键 */
    id: number;
    /** 学年 */
    year: string;
    /** 学期 */
    term: number;
    /** 编号 */
    code: string;
    /** 开始日期 */
    start_date: Date;
    /** 结束日期 */
    end_date: Date;
    /** 状态 */
    status: number;
  };

  /** 课程学制 */
  interface PlanCourseSystem {
    /** 主键 */
    id: number;
    /** 学制 */
    system: string;
    /** 类别名称 */
    type: string;
    /** 描述 */
    describe?: string;
  }

  /** 学科 */
  type Subject = {
    /** ID，主键 */
    id: number;
    /** 学段 */
    grade_section: string;
    /** 学科 */
    subject: string;
    /** 封面 */
    cover?: string;
    /** 描述 */
    description?: string;
    /** 学科教材列表 */
    textbooks?: Textbook[];
    /** 知识点 */
    knowledgePoints?: KnowledgePoint[];
  };

  /** 出版商位 */
  type Publisher = {
    /** 出版商位ID，主键 */
    id: number;
    /** 出版商位名称 */
    name: string;
    /** 地址 */
    address?: string;
    /** 联系人 */
    contact?: string;
    /** 联系电话 */
    phone?: string;
    /** 单位性质 */
    nature?: string;
    /** 描述 */
    description?: string;
    textbooks?: Textbook[];
  };

  type Textbook = {
    /** ID，主键 */
    id: number;
    /** 学科ID */
    subject_id: number;
    /** 教材版本 */
    textbook_version: string;
    /** 别名 */
    alias?: string;
    /** 出版单位ID */
    publisher_id?: number;
    subject?: Subject;
    publisher?: Publisher;
    checklist?: TextbookChecklist[];
  };

  /** 教材名录，目录挂在这上面 */
  type TextbookChecklist = {
    /** ID，主键 */
    id: number;
    /** 教材版本ID */
    textbook_id: number;
    /** 年级编号 */
    grade: string;
    /** 册次 */
    volume: string;
    /** 主编 */
    editor?: string;
    /** 学科教材 */
    textbook?: Textbook;
    catalogs?: TextbookCatalog[];
  };

  /** 教材目录 */
  type TextbookCatalog = {
    /** ID，主键 */
    id: number;
    old_id?: number;
    old_title?: string;
    /** 教材名录ID */
    textbookChecklist_id: number;
    /** 名称 */
    title: string;
    /** 父ID */
    parent_id?: number;
    /** 父名称 */
    parent_name?: string;
    /** 排序 */
    sort_order: number;
    level?: number;
    textbookChecklist?: TextbookChecklist;
    parent?: TextbookCatalog;
    children?: TextbookCatalog[];
  };

  type TextbookChooseDetail = {
    /** ID，主键 */
    id: number;
    /** 选用记录ID */
    textbook_choose_id: number;
    /** 区域编号 */
    area_code: string;
  };

  /** 教材选用信息 */
  type TextbookChoose = {
    /** ID，主键 */
    id: number;
    /** 学年 */
    semester: string;
    /** 学科ID */
    subject_id: number;
    /** 适用年级，逗号分隔 */
    grade_list: string;
    /** 教材版本ID */
    textbook_id: number;
    subject?: Subject;
    textbook?: Textbook;
    items?: Partial<TextbookChooseDetail>[];
  };
  /** 系统试题 */
  interface SystemQuestion {
    /**
     * 解析
     */
    analysis?: string;
    /**
     * 答案
     */
    answer?: string;
    /**
     * 作者
     */
    author?: Author;
    /**
     * 基础题型
     */
    baseType: BaseType;
    /**
     * 分类
     */
    classification?: {
      name: string;
      id: number;
    };
    /**
     * 认知层次
     */
    cognitiveHierarchy?: CognitiveHierarchy;
    /**
     * 核心素养
     */
    coreQuality?: CoreQuality;
    /**
     * 小节题目的创建类型
     */
    createType?: string;
    /**
     * 试题难度
     */
    difficulty: Difficulty;
    /**
     * 年级
     */
    grade?: Grade;
    /**
     * 学段
     */
    gradeSection?: GradeSection;
    /**
     * 考察能力
     */
    investigationAbility?: InvestigationAbility;
    /**
     * 是否是组合题
     */
    isCompose?: boolean;
    /**
     * 名称
     */
    name: string;
    /**
     * 选项
     */
    options?: string;
    /**
     * 页码
     */
    pagination?: number;
    /**
     * 课时
     */
    period?: Period;
    /**
     * 知识点
     */
    points?: Point[];
    /**
     * 被引用数量
     */
    referencedNum?: number;
    /**
     * 来源
     */
    source?: Source;
    /**
     * 学科
     */
    subject?: Subject;
    /**
     * 所在表名称
     */
    tableName?: string;
    /**
     * 类题标签
     */
    tags?: Tag[];
    /**
     * 教材版本
     */
    textbookVersion?: TextbookVersion;
    /**
     * 题目分层
     */
    tier?: Tier;
    /**
     * 扩展题型
     */
    type?: Type;
    /**
     * 单元
     */
    unit?: Unit;
    /**
     * 册次
     */
    volume?: Volume;
    /**
     * 年份
     */
    year?: string;
    [property: string]: any;
  }

  /**
   * 作者
   */
  export interface Author {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 基础题型
   */
  export interface BaseType {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 认知层次
   */
  export interface CognitiveHierarchy {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 核心素养
   */
  export interface CoreQuality {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 试题难度
   */
  export interface Difficulty {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 年级
   */
  export interface Grade {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 学段
   */
  export interface GradeSection {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 考察能力
   */
  export interface InvestigationAbility {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 课时
   */
  export interface Period {
    id: string;
    name: string;
    [property: string]: any;
  }

  export interface Point {
    key?: string;
    value?: string;
    [property: string]: any;
  }

  /**
   * 来源
   */
  export interface Source {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 学科
   */
  export interface Subject {
    id: string;
    name: string;
    [property: string]: any;
  }

  export interface Tag {
    label?: string;
    value?: string;
    [property: string]: any;
  }

  /**
   * 教材版本
   */
  export interface TextbookVersion {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 题目分层
   */
  export interface Tier {
    code: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 扩展题型
   */
  export interface Type {
    code: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 单元
   */
  export interface Unit {
    id: string;
    name: string;
    [property: string]: any;
  }

  /**
   * 册次
   */
  export interface Volume {
    id: string;
    name: string;
    [property: string]: any;
  }

  /** 课时作业范本 */
  type TemplateWork = {
    /**
     * 企业id
     */
    enterprise_id?: string;
    /**
     * 年级code
     */
    grade_code: string;
    /**
     * 年级
     */
    grade_name: string;
    /**
     * 学段code
     */
    grade_section_code: string;
    /**
     * 学段
     */
    grade_section_name: string;
    /**
     * 课时作业范本名称
     */
    name: string;
    /**
     * 状态
     */
    status?: string;
    /**
     * 来源
     */
    source?: string;
    /**
     * 来源code
     */
    source_code?: string;
    /**
     * 学科id
     */
    subject_id: number;
    /**
     * 学科
     */
    subject_name: string;
    /**
     * 教材版本
     */
    textbook_version: string;
    textbook_id?: number;
    /**
     * 范本类型
     */
    type?: string;
    /**
     * 册次
     */
    volume: string;
    /**
     * 册次id
     */
    textbookChecklist_id?: number;
    is_share?: boolean;
    updatedAt?: string;
    createdAt?: string;
    id?: string;
  };
  /** 系统父子题 */
  interface AssociateQuestion {
    _id: string;
    /**
     * 解析
     */
    analysis: string;
    /**
     * 答案
     */
    answer: string;
    /**
     * 作者
     */
    author: string;
    baseType: AssociateQuestionBaseType;
    children: Child[];
    /**
     * 认知层次
     */
    cognitiveHierarchy: string;
    /**
     * 核心素养
     */
    coreQuality: string;
    /**
     * 试题难度
     */
    difficulty: Difficulty;
    /**
     * 年级
     */
    grade: string;
    /**
     * 学段
     */
    gradeSection: string;
    /**
     * 考察能力
     */
    investigationAbility: string;
    /**
     * 是否是组合题
     */
    isCompose: boolean;
    /**
     * 名称
     */
    name: string;
    /**
     * 选项
     */
    options: string[];
    /**
     * 课时
     */
    period: string;
    points: AssociateQuestionPoint[];
    /**
     * 来源
     */
    source: string;
    /**
     * 学科
     */
    subject: string;
    tags: Tag[];
    /**
     * 教材版本
     */
    textbookVersion: string;
    /**
     * 分层
     */
    tier: string;
    type: AssociateQuestionType;
    /**
     * 单元
     */
    unit: string;
    /**
     * 册次
     */
    volume: string;
    /**
     * 年份
     */
    year: string;
  }

  interface AssociateQuestionBaseType {
    code: string;
    name: string;
  }

  interface Child {
    /**
     * 解析
     */
    analysis: string;
    /**
     * 答案
     */
    answer: string;
    baseType: ChildBaseType;
    /**
     * 名称
     */
    name: string;
    /**
     * 选项
     */
    options: string;
    points: ChildPoint[];
    type: ChildType;
  }

  interface ChildBaseType {
    code: string;
    name: string;
  }

  interface ChildPoint {
    key: string;
    value: string;
  }

  interface ChildType {
    code: string;
    name: string;
  }

  /**
   * 试题难度
   */
  interface Difficulty {
    code: string;
    name: string;
  }

  interface AssociateQuestionPoint {
    key?: string;
    value?: string;
  }

  interface Tag {
    label?: string;
    value?: string;
  }

  interface AssociateQuestionType {
    code: string;
    name: string;
  }

  /** 组卷方案 */
  /** 组卷方案 */
  interface ComposerPaperType extends BaseTime {
    /** 方案ID */
    id: number;
    /** 方案名称 */
    name: string;
    /** 适用年级 */
    grade: string;
    /** 教材版本 */
    version: string;
    /** 册次 */
    volume: string;
    /** 学科ID */
    subjectId: number;
    /** 学科名称 */
    subjectName: string;
    /** 创建者ID */
    creatorId: number;
    /** 创建者姓名 */
    creatorName: string;
    /** 所属企业ID */
    enterpriseId: number;
    /** 方案描述 */
    description: string;
    /** 包含试卷数量 */
    paperCount: number;
    /** 是否处理完成 */
    process: boolean;
    /** 学段 */
    gradeSection: string;
  }

  interface QuestionTypes extends BaseTime {
    id: number;
    name: string;
    code: string;
    sourceCode: string;
    sourceName: string;
    isCompose: boolean;
    subjectId: number[];
  }

  interface AdminTargetType extends BaseTime {
    id: number;
    name: string;
    describe: string;
    analy: string;
  }
  interface similarQuestionsParams {
    /**
     * 基础题型
     */
    baseType?: string;
    /**
     * 难度
     */
    difficulty?: string;
    /**
     * 当前作业试题ids
     */
    ids: string[];
    /**
     * 更换试题数量
     */
    num: number;
    /**
     * 课时
     */
    period?: string;
    /**
     * 知识点
     */
    points?: string;
    /**
     * 来源
     */
    source?: string;
    /**
     * 扩展题型
     */
    type?: string;
    [property: string]: any;
  }
  /** 达标检测范本列表 */
  type TemplateComplicance = {
    /**
     * 检测类型 单元 | 期中|期末
     */
    detection_type?: string;
    /**
     * 企业id
     */
    enterprise_id?: number;
    /**
     * 年级code
     */
    grade_code: string;
    /**
     * 年级
     */
    grade_name: string;
    /**
     * 学段code
     */
    grade_section_code: string;
    /**
     * 学段
     */
    grade_section_name: string;
    /**
     * 作业范本名称
     */
    name: string;
    /**
     * 状态
     */
    status?: string;
    /**
     * 来源
     */
    source?: string;
    /**
     * 来源code
     */
    source_code?: string;
    /**
     * 学科id
     */
    subject_id: number;
    /**
     * 学科
     */
    subject_name: string;
    /**
     * 教材id
     */
    textbook_id: string;
    /**
     * 教材版本
     */
    textbook_version: string;
    /**
     * 教材名录id
     */
    textbookChecklist_id: string;
    /**
     * 范本类型 枚举类型：内置 自定义
     */
    type?: string;
    /**
     * 册次
     */
    volume: string;
    is_share?: boolean;
    updatedAt?: string;
    createdAt?: string;
    id?: string;
  };

  /** 知识点 */
  interface KnowledgePoint {
    /** 知识点ID */
    id: number;
    /** 知识点名称 */
    name: string;
    /** 父级知识点ID */
    pid: number;
    /** 所属学科ID */
    subjectId: number;
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
    children?: KnowledgePoint[];
  }
}
