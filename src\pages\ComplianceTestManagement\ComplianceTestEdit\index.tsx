import AnswerSheet from '@/components/AnswerSheet';
import CommonCard from '@/components/CommonCard';
import HomeworkDesign from '@/components/HomeworkDesign';
import AddQuestion from '@/components/HomeworkDesign/PaperEditPage/AddQuestion';
import AnalysisPaper from '@/components/HomeworkDesign/PaperEditPage/AnalysisPaper';
import PaperStructure from '@/components/HomeworkDesign/PaperEditPage/Structure/PaperStructure';
import TitleBar from '@/components/TitleBar';
import {
  templateComplicanceQuestionIndex,
  templateComplicanceQuestionSavePaper,
} from '@/services/compliance_detection_template_question';
import { templateComplicanceStructIndex } from '@/services/compliance_detection_template_struct';
import { downPaperToWord } from '@/services/htmlToWord';
import { getQueryParams } from '@/utils/calc';
import {
  FileDoneOutlined,
  FormOutlined,
  LeftOutlined,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import { Button, Col, Input, InputRef, Row, Spin, message } from 'antd';
import classNames from 'classnames';
import { nanoid } from 'nanoid';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';

const HomeworkTemplateEdit: React.FC = () => {
  const { type, homeworkId } = getQueryParams();
  const [listLoading, setListLoading] = useState<boolean>(false);
  const [questionData, setQuestionData] = useState<API.SystemQuestion[]>([]);
  const [curHomeID, setCurHomeID] = useState<any>(homeworkId);
  const [strucData, setStrucData] = useState<any>();
  const [isWordDownloading, setIsWordDownloading] = useState<boolean>(false);
  const [durationRange, setDurationRange] = useState<number>(0);
  const [questionModal, setQuestionModal] = useState<any>({
    open: false,
  });
  const [paperName, setPaperName] = useState<any>();
  const [inputWidth, setInputWidth] = useState<number>(60);
  const inputRef = useRef<InputRef>(null);
  // 计算输入框宽度
  useEffect(() => {
    if (inputRef.current?.input) {
      const tempSpan = document.createElement('span');
      tempSpan.style.visibility = 'hidden';
      tempSpan.style.whiteSpace = 'pre';
      tempSpan.style.fontSize = '16px';
      tempSpan.style.fontWeight = 'bold';
      tempSpan.style.padding = '0 24px';
      tempSpan.textContent = paperName;
      document.body.appendChild(tempSpan);

      const width = Math.max(60, tempSpan.offsetWidth + 32); // 32是padding和图标宽度
      setInputWidth(width);

      document.body.removeChild(tempSpan);
    }
  }, [paperName]);
  /**
   * 获取作业详情
   *
   */
  const getHomeworkDetail = async () => {
    setListLoading(true);
    const { errCode, data, msg } = await templateComplicanceQuestionIndex({
      detail_id: curHomeID,
    });
    if (errCode) {
      message.error('试卷内容获取失败，请联系管理员或稍后再试！' + msg);
      return;
    }
    setQuestionData(data);
    const {
      errCode: errCode1,
      data: data1,
      msg: msg1,
    } = await templateComplicanceStructIndex({
      detail_id: curHomeID,
    });
    if (errCode1) {
      message.error('试卷结构获取失败，请联系管理员或稍后再试！' + msg1);
      return;
    }
    let newData = data1?.list || [];
    if (newData?.length === 0) {
      const hideId = 'hidden_' + nanoid();
      newData = {
        bigQuestionOrder: [hideId],
        bigQuestions: {},
      };
      newData.bigQuestions[hideId] = {
        id: hideId,
        name: '暂无分组',
        questionIds: data.map((item: any) => item?._id || null),
      };
    } else {
      setPaperName(newData?.[0]?.detail?.name);
      const bigQuestionOrder = newData?.map((v: any) => v.id);
      const bigQuestions = newData?.reduce((acc: any, item: any) => {
        acc[item.id] = item;
        return acc;
      }, {});
      newData = {
        bigQuestionOrder,
        bigQuestions,
      };
    }
    setStrucData(newData);
    setListLoading(false);
  };
  /**
   * 更新作业信息
   *
   */
  const refreshHomework = () => {
    // 获取所有questionIds
    const allQuestionIds = Object.values(strucData.bigQuestions).flatMap(
      (bigQuestion: any) => bigQuestion.questionIds,
    );

    // 筛选questionData中_id匹配的数据
    const matchedQuestions = questionData.filter((item) =>
      allQuestionIds.includes(item._id),
    );

    setQuestionData(matchedQuestions); // 输出匹配的试题数据
  };
  /**
   * 关闭模态框
   */
  const closeModal = () => {
    setQuestionModal({
      open: false,
    });
  };
  /**
   * 保存试卷的函数
   */
  const handleSave = async () => {
    if (questionData?.length) {
      let result;
      // 获取所有questionIds并按顺序排序
      const sortedQuestionIds = strucData.bigQuestionOrder.flatMap(
        (bigQuestionId: string) =>
          strucData.bigQuestions[bigQuestionId].questionIds,
      );

      // 优化1：提前创建Map提高性能
      const questionMap = new Map(questionData.map((item) => [item._id, item]));

      // 优化2：使用filter(Boolean)过滤掉undefined值
      const sortedQuestions = sortedQuestionIds
        .map((id: string) => questionMap.get(id))
        .filter(Boolean);
      const questions = sortedQuestions.map((item: any) => {
        return {
          detail_id: curHomeID, //课时作业详情id
          question_id: item._id,
          source_table: item.source_table || item.tableName,
        };
      });
      const { bigQuestions, bigQuestionOrder } = strucData;
      const data = bigQuestionOrder?.map((key: string) => bigQuestions?.[key]);
      if (curHomeID) {
        result = await templateComplicanceQuestionSavePaper(curHomeID, {
          name: paperName,
          duration: durationRange,
          questions,
          structs: data,
        });
        if (result.errCode) {
          message.error('试卷保存失败，请联系管理员或稍后再试！' + result.msg);
          return;
        }
        // 更新URL查询参数
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.delete('type');
        window.history.replaceState(
          null,
          '',
          `${window.location.pathname}?${searchParams.toString()}`,
        );
        message.success('试卷保存成功');
      }
    } else {
      message.error('请先添加试题再保存试卷！');
    }
  };
  const handleDownloadWord = async () => {
    // 获取所有questionIds并按顺序排序
    const sortedQuestionIds = strucData.bigQuestionOrder.flatMap(
      (bigQuestionId: string) =>
        strucData.bigQuestions[bigQuestionId].questionIds,
    );

    // 优化1：提前创建Map提高性能
    const questionMap = new Map(
      questionData.map((item: any) => [item._id, item]),
    );

    // 优化2：使用filter(Boolean)过滤掉undefined值
    const sortedQuestions = sortedQuestionIds
      .map((id: string) => questionMap.get(id))
      .filter(Boolean);
    const questions = sortedQuestions.map((item: any) => {
      return {
        question_id: item._id,
        source_table: item.source_table || item.tableName,
      };
    });
    const { bigQuestions, bigQuestionOrder } = strucData;
    const data = bigQuestionOrder?.map((key: string) => bigQuestions?.[key]);
    try {
      setIsWordDownloading(true);
      const {
        errCode,
        data: responseData,
        msg,
      } = await downPaperToWord({
        name: paperName || '试卷下载',
        struct: data,
        questions: questions,
        options: {
          fontSize: 14,
          fontFamily:
            'Microsoft YaHei, Helvetica Neue, PingFang SC, sans-serif',
          lineHeight: 1.5,
        },
      });
      if (errCode) {
        setIsWordDownloading(false);
        return message.error('下载失败，请联系管理员后重试！' + msg);
      }
      const uint8Array = new Uint8Array(responseData.data);
      // 假设response.data是Buffer数据
      const blob = new Blob([uint8Array], { type: 'application/msword' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `${paperName || '试卷下载'}.doc`;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error('下载失败:', error);
    } finally {
      setIsWordDownloading(false);
    }
  };
  /**
   * 选题
   */
  const chooseQuestion = () => {
    setQuestionModal({
      open: true,
      questionData,
      setQuestionData,
      strucData,
      setStrucData,
    });
  };
  useEffect(() => {
    if (type) {
      chooseQuestion();
    }
  }, [type]);
  useEffect(() => {
    if (curHomeID) {
      getHomeworkDetail();
    }
  }, [curHomeID]);
  useEffect(() => {
    if (homeworkId) {
      setCurHomeID(homeworkId);
    }
  }, [homeworkId]);
  useEffect(() => {
    if (strucData) {
      refreshHomework();
    }
  }, [strucData]);

  return (
    <div className={styles.homeworkTemplate}>
      <CommonCard
        activeTitle
        title={
          <div className={styles.templateHeader}>
            <Button
              type="link"
              icon={<LeftOutlined />}
              onClick={() => {
                window.close();
              }}
            >
              返回列表
            </Button>
          </div>
        }
        centerStyle={{
          flex: 1,
        }}
        centerDom={
          <h1 className={styles.paperMainTitle}>
            <Input
              ref={inputRef}
              name="name"
              bordered={false}
              suffix={<FormOutlined />}
              defaultValue="编辑试卷"
              value={paperName}
              onChange={async (e) => {
                setPaperName(e.target.value);
              }}
              style={{ width: `${inputWidth}px`, minWidth: 130 }}
            />
          </h1>
        }
      >
        <Button
          icon={<VerticalAlignBottomOutlined />}
          onClick={handleDownloadWord}
          loading={isWordDownloading}
        >
          下载试卷
        </Button>
        <Button icon={<FileDoneOutlined />} onClick={handleSave}>
          保存试卷
        </Button>
        <AnswerSheet
          paperName={paperName}
          strucData={strucData}
          questionData={questionData}
        />
        {/* <Button icon={<ContainerOutlined />}>制作答题卡</Button>
        <Button icon={<FileSyncOutlined />}>版本对比</Button> */}
      </CommonCard>
      <div className={styles.container}>
        <Spin tip="数据加载中..." spinning={listLoading}>
          <Row gutter={[16, 16]}>
            <Col flex="360px">
              <div className={classNames('commonWapper', styles.leftWrapper)}>
                <TitleBar
                  title="试卷分析"
                  style={{
                    padding: '0 0 14px',
                    borderRadius: 0,
                    borderBottom: '1px solid #dadada',
                  }}
                />
                <AnalysisPaper
                  type="analysis"
                  setDurationRange={setDurationRange}
                  questionList={questionData}
                />
              </div>
            </Col>
            <Col flex="1">
              <div className={classNames('commonWapper', styles.centerWrapper)}>
                <HomeworkDesign
                  questionConstrue={strucData}
                  questionList={questionData}
                  sortQuestion={setStrucData}
                  replaceQuestion={setQuestionData}
                />
              </div>
            </Col>
            <Col flex="360px">
              <div className={classNames('commonWapper', styles.rightWrapper)}>
                <PaperStructure
                  strucData={strucData}
                  setStrucData={setStrucData}
                  setQuestionData={setQuestionData}
                  chooseQuestion={chooseQuestion}
                />
              </div>
            </Col>
          </Row>
        </Spin>
      </div>
      <AddQuestion questionModal={questionModal} callback={closeModal} />
    </div>
  );
};
export default HomeworkTemplateEdit;
