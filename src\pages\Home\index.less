.container {
  background: linear-gradient(
    rgba(203, 226, 255, 60%),
    rgba(203, 226, 255, 60%)
  );
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
  position: relative;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 20% 80%,
        rgba(24, 144, 255, 10%) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(24, 144, 255, 10%) 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

.top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
  background: rgba(255, 255, 255, 95%);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 12px rgba(24, 144, 255, 15%);
  border-bottom: 1px solid rgba(24, 144, 255, 10%);

  .logo {
    display: flex;
    align-items: center;
    gap: 16px;

    img {
      height: 48px;
      max-width: 160px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 20%);
    }

    span {
      font-size: 24px;
      font-weight: 600;
      color: #1890ff;
    }
  }

  .userInfo {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: 16px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(24, 144, 255, 15%);
      transform: translateY(-1px);
    }
  }
}

.banner {
  margin: 24px;
  margin-bottom: 32px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 95%) 0%,
    rgba(255, 255, 255, 90%) 100%
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(24, 144, 255, 20%);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8px 32px rgba(24, 144, 255, 15%);
  padding: 40px;
  min-height: 200px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(24, 144, 255, 3%) 0%,
      rgba(24, 144, 255, 8%) 100%
    );
    z-index: 1;
  }

  .bannerContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
  }

  .bannerLeft {
    flex: 1;
    max-width: 500px;

    h2 {
      margin: 0 0 20px;
      line-height: 1.2;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .titleMain {
        color: #1890ff;
        font-size: 36px;
        font-weight: 700;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 2px 4px rgba(24, 144, 255, 10%);
      }

      .titleSub {
        color: #8c8c8c;
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 1px;
        text-transform: uppercase;
        opacity: 0.8;
      }
    }

    p {
      color: #666;
      font-size: 18px;
      margin: 0 0 28px;
      line-height: 1.6;
      font-weight: 400;
    }
  }

  .bannerFeatures {
    display: flex;
    flex-direction: column;

    span {
      color: #52c41a;
      font-size: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        color: #389e0d;
        transform: translateX(8px);
      }

      .featureIcon {
        font-size: 18px;
        color: #52c41a;
        background: rgba(82, 196, 26, 10%);
        padding: 6px;
        border-radius: 50%;
        transition: all 0.3s ease;
      }

      &:hover .featureIcon {
        background: rgba(82, 196, 26, 20%);
        transform: scale(1.1);
      }
    }
  }

  .bannerRight {
    flex: 0 0 320px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -20px;
      left: -20px;
      right: -20px;
      bottom: -20px;
      background: linear-gradient(
        135deg,
        rgba(24, 144, 255, 10%) 0%,
        rgba(64, 169, 255, 10%) 100%
      );
      border-radius: 24px;
      z-index: 1;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  .bannerMainImage {
    width: 100%;
    max-width: 300px;
    height: 200px;
    border-radius: 16px;
    object-fit: cover;
    box-shadow: 0 12px 32px rgba(24, 144, 255, 15%),
      0 6px 16px rgba(24, 144, 255, 10%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
    border: 2px solid rgba(255, 255, 255, 80%);

    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 48px rgba(24, 144, 255, 25%),
        0 10px 24px rgba(24, 144, 255, 15%);
    }
  }

  @media (max-width: 768px) {
    padding: 24px;

    .bannerContent {
      flex-direction: column;
      text-align: center;
      gap: 32px;
    }

    .bannerLeft {
      max-width: none;

      h2 {
        .titleMain {
          font-size: 28px;
        }

        .titleSub {
          font-size: 12px;
        }
      }

      p {
        font-size: 16px;
      }
    }

    .bannerFeatures {
      align-items: center;

      span {
        justify-content: center;
        max-width: 280px;
      }
    }

    .bannerRight {
      flex: none;

      &::before {
        top: -15px;
        left: -15px;
        right: -15px;
        bottom: -15px;
      }
    }

    .bannerMainImage {
      max-width: 260px;
      height: 160px;

      &:hover {
        transform: translateY(-6px) scale(1.01);
      }
    }
  }
}

.content {
  padding: 0 32px 32px;
  height: calc(100vh - 300px);
  overflow-y: auto;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(24, 144, 255, 10%);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(24, 144, 255, 30%);
    border-radius: 3px;

    &:hover {
      background: rgba(24, 144, 255, 50%);
    }
  }

  .title {
    margin-bottom: 32px;
    text-align: center;
    color: #1890ff;
    font-size: 22px;
    font-weight: 600;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: #1890ff;
      border-radius: 2px;
    }
  }

  .statsRow {
    margin-bottom: 40px;

    .ant-pro-card {
      background: rgba(255, 255, 255, 95%);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(24, 144, 255, 15%);
      border-radius: 16px;
      box-shadow: 0 6px 24px rgba(24, 144, 255, 8%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      position: relative;
      min-height: 120px;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(
          90deg,
          #1890ff 0%,
          #40a9ff 50%,
          #69c0ff 100%
        );
        border-radius: 16px 16px 0 0;
      }

      &:hover {
        transform: translateY(-6px);
        box-shadow: 0 12px 40px rgba(24, 144, 255, 15%);
        border-color: rgba(24, 144, 255, 25%);

        &::before {
          background: linear-gradient(
            90deg,
            #40a9ff 0%,
            #69c0ff 50%,
            #91d5ff 100%
          );
        }
      }

      .ant-statistic-title {
        color: #666;
        font-weight: 500;
        margin-bottom: 12px;
        font-size: 14px;
      }

      .ant-statistic-content {
        color: #1890ff;
        font-weight: 700;
        font-size: 28px;
      }
    }
  }

  .statisticCard {
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;

    .ant-statistic {
      flex: 1;
    }
  }

  .statisticIcon {
    transition: all 0.3s ease;
    opacity: 0.85;
    margin-bottom: 8px;

    &:hover {
      transform: scale(1.15) translateY(-2px);
      opacity: 1;
      filter: brightness(1.1) drop-shadow(0 4px 8px rgba(0, 0, 0, 15%));
    }
  }

  .featuresRow {
    margin-bottom: 40px;
  }

  .featureItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 24px;
    background: rgba(255, 255, 255, 95%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(24, 144, 255, 15%);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 160px;
    box-shadow: 0 6px 24px rgba(24, 144, 255, 10%);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: #1890ff;
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(24, 144, 255, 20%);
      border-color: rgba(24, 144, 255, 30%);

      &::before {
        transform: scaleX(1);
      }

      .featureIcon {
        transform: scale(1.1);
      }
    }

    .featureIcon {
      font-size: 48px;
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }

    .featureName {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      text-align: center;
    }
  }

  .nonCardFeatureItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 28px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 140px;
    background: rgba(255, 255, 255, 90%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(24, 144, 255, 20%);
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 10%);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(24, 144, 255, 5%) 0%,
        rgba(24, 144, 255, 10%) 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 6px 25px rgba(24, 144, 255, 15%);
      border-color: rgba(24, 144, 255, 30%);

      &::before {
        opacity: 1;
      }

      .featureIcon {
        transform: scale(1.1);
      }

      .featureName {
        color: #1890ff;
      }
    }

    .featureIcon {
      font-size: 40px;
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }

    .featureName {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      text-align: center;
      transition: all 0.3s ease;
    }
  }

  @keyframes rocket {
    0%,
    100% {
      transform: scale(1.2) translateY(-2px);
    }

    50% {
      transform: scale(1.2) translateY(-6px);
    }
  }
}

.footer {
  text-align: center;
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
  margin-top: 24px;
  font-size: 14px;
  color: #888;
}
