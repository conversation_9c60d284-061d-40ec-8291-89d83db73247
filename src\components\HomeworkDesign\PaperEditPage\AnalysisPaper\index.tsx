import ConditionalRender from '@/components/ConditionalRender';
import TitleBar from '@/components/TitleBar';
import { analysisQuestions } from '@/services/common_question';
import {
  FieldTimeOutlined,
  LineChartOutlined,
  PicRightOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { Progress, Tooltip, message } from 'antd';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import DifficultyAnalysis from './DifficultyAnalysis';
import TypeAnalysis from './TypeAnalysis';

const AnalysisPaper = ({
  type,
  questionList,
  setDurationRange,
}: {
  type: 'analysis' | 'preview';
  questionList: API.SystemQuestion[];
  setDurationRange?: Dispatch<SetStateAction<number>>;
}) => {
  const [analysisData, setAnalysisData] = useState<any>();
  const getQuestionAnalysis = async () => {
    const questions = questionList.map((item) => {
      return {
        question_id: item._id,
        source_table: item.source_table || item.tableName,
      };
    });
    const { errCode, data, msg } = await analysisQuestions({
      type,
      questions,
    });
    if (errCode) {
      message.error('试卷分析失败，请联系管理员或稍后再试！' + msg);
      return;
    }
    setDurationRange?.(data?.duration || 0);
    setAnalysisData(data);
  };
  useEffect(() => {
    if (!questionList?.length) {
      setAnalysisData(undefined);
      return;
    }
    getQuestionAnalysis();
  }, [questionList]);
  return (
    <div>
      <ConditionalRender
        hasAccess={type === 'analysis'}
        accessComponent={
          <>
            <TitleBar
              prefix={false}
              title={
                <>
                  <PicRightOutlined />
                  作业设计评价分
                  <Tooltip
                    placement="topLeft"
                    color="blue"
                    title={
                      '总评分 = (知识点覆盖度 × 权重分) + (认知层次覆盖度 × 权重分) + (类型覆盖度 × 权重分) + (难度覆盖度 × 权重分) '
                    }
                  >
                    <QuestionCircleOutlined
                      style={{
                        marginLeft: '10px',
                      }}
                    />
                  </Tooltip>
                </>
              }
              actionDom={
                <div>
                  <span
                    style={{
                      color: '#ff4d4f',
                      fontSize: '18px',
                      fontWeight: 'bold',
                    }}
                  >
                    {analysisData?.score}
                  </span>
                </div>
              }
            />
            <TitleBar
              prefix={false}
              title={
                <>
                  <FieldTimeOutlined />
                  预计作答时长
                </>
              }
              actionDom={
                <div>
                  <span
                    style={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      color: '#666',
                    }}
                  >
                    {analysisData?.duration
                      ? analysisData?.duration + '分钟'
                      : '未定义'}
                  </span>
                </div>
              }
            />
            <TitleBar
              prefix={false}
              title={
                <>
                  <LineChartOutlined />
                  知识点覆盖度
                </>
              }
            />
            <Progress percent={analysisData?.pointCoverage} status="active" />
            <TitleBar
              prefix={false}
              title={
                <>
                  <LineChartOutlined />
                  认知层次覆盖度
                </>
              }
            />
            <Progress
              percent={analysisData?.cognitiveHierarchyCoverage}
              status="active"
            />
          </>
        }
      />
      <TypeAnalysis data={analysisData} />
      <DifficultyAnalysis data={analysisData} />
    </div>
  );
};

export default AnalysisPaper;
