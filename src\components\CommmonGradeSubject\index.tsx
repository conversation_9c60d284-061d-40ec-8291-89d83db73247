import { DictionarieState } from '@/models/dictionarie';
import { index } from '@/services/subjects';
import { ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { connect, useDispatch } from '@umijs/max';
import { FormInstance, message } from 'antd';
import React, { useEffect, useState } from 'react';
import ConditionalRender from '../ConditionalRender';

interface CommmonGradeSubjectProps {
  dictionarie: DictionarieState;
  form: FormInstance<any>;
  /** 显示年级 */
  showGrade?: boolean;
}

interface GradeOption {
  label: string;
  value: string;
}

const getGradeOptions = (
  dictionarieList: any[],
  selectedSection: string | undefined,
  gradeSectionMap: Record<string, string[]>,
): GradeOption[] => {
  if (!selectedSection) return [];

  const gradeMap = gradeSectionMap[selectedSection];
  if (!gradeMap) return [];

  const grades = dictionarieList.filter((item) => item.type === 'grade');

  return grades
    .map((item) => ({
      label: item.name,
      value: item.code,
    }))
    .filter((option) => gradeMap.includes(option.value));
};

/** 学段年级对比 */
const gradeSectionMap: Record<string, string[]> = {
  GRADE_PRIMARY: ['1', '2', '3', '4', '5', '6'],
  GRADE_MIDDLE: ['7', '8', '9'],
  GRADE_HIGH: ['10', '11', '12'],
};

const CommmonGradeSubject: React.FC<CommmonGradeSubjectProps> = ({
  dictionarie,
  form,
  showGrade = false,
}) => {
  const [selectedSection, setSelectedSection] = useState<string>();
  const [gradeOptions, setGradeOptions] = useState<any[]>([]);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch({
      type: 'dictionarie/queryList',
    });
  }, []);

  const gradeSectionList = dictionarie.list
    .filter((item) => item.type === 'grade_section')
    .map((item) => ({ label: item.name, value: item.code }));

  useEffect(() => {
    const options = getGradeOptions(
      dictionarie.list,
      selectedSection,
      gradeSectionMap,
    );
    setGradeOptions(options);
  }, [dictionarie.list, selectedSection]);

  useEffect(() => {
    const { grade_section_code, grade_section_name, subject_id, subject_name } =
      form.getFieldsValue() || {};
    if (
      grade_section_code &&
      grade_section_name &&
      subject_id &&
      subject_name
    ) {
      setSelectedSection(grade_section_code);
      form.setFieldsValue({
        subject_name,
        grade_section_name,
        grade_section_code,
        subject_id,
      });
    }
  }, [form]);

  return (
    <>
      <ProFormText name="subject_name" hidden />
      <ProFormText name="grade_section_name" hidden />
      <ConditionalRender
        hasAccess={showGrade}
        accessComponent={<ProFormText name="grade_name" hidden />}
      />
      <ProFormSelect
        name="grade_section_code"
        label="学段"
        options={gradeSectionList}
        fieldProps={{
          onChange: (value: string, option: any) => {
            setSelectedSection(value);
            form.setFieldsValue({
              grade_section_name: option?.title,
              subject_id: undefined,
              subject_name: undefined,
              grade_code: undefined,
              grade_name: undefined,
            });
          },
          onClear: () => {
            setSelectedSection(undefined);
            form.setFieldsValue({
              subject_id: undefined,
              subject_name: undefined,
              grade_code: undefined,
              grade_name: undefined,
            });
          },
        }}
        rules={[{ required: true, message: '请输入学段！' }]}
      />
      <ConditionalRender
        hasAccess={showGrade}
        accessComponent={
          <ProFormSelect
            name="grade_code"
            label="年级"
            options={gradeOptions}
            fieldProps={{
              onChange: (value: string, option: any) => {
                form.setFieldsValue({
                  grade_name: option?.title,
                });
              },
            }}
            rules={[{ required: true, message: '请输入年级！' }]}
          />
        }
      />
      <ProFormSelect
        name="subject_id"
        label="学科"
        params={{ grade_section: selectedSection }}
        fieldProps={{
          onChange: (_value: string, option: any) => {
            form.setFieldsValue({
              subject_name: option?.title,
            });
          },
        }}
        request={async (params) => {
          if (!selectedSection) {
            return [];
          }
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.warning(`获取学科列表失败 ${msg}`);
            return [];
          }
          return (
            data?.list?.map((item) => ({
              label: item.subject,
              value: item.id,
            })) ?? []
          );
        }}
        placeholder="请选择学科"
        rules={[{ required: true, message: '此项为必填项' }]}
      />
    </>
  );
};
export default connect(({ dictionarie }) => ({ dictionarie }))(
  CommmonGradeSubject,
);
