import ConditionalRender from '@/components/ConditionalRender';
import {
  QuestionCheckStatus,
  tagColor,
} from '@/pages/QuestionManagement/Audit/Audited';
import { editUserTag } from '@/services/common_question';
import eventBus from '@/utils/eventBus';
import {
  DeleteOutlined,
  DiffOutlined,
  EditOutlined,
  EyeOutlined,
  FlagOutlined,
  FormOutlined,
  HistoryOutlined,
  MinusOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Button,
  Checkbox,
  Modal,
  Popconfirm,
  Popover,
  Space,
  Tag,
  Tooltip,
  message,
} from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import DiffModal from '../DiffModal';
import AddTagButton from './AddTagButton';
import styles from './common.less';
import ReviseClassHoursModal from './ReviseClassHoursModal';
import TopicContent from './TopicContent';

interface TopicCardProps {
  info: API.SystemQuestion;
  isChoosed?: boolean;
  allSelect?: string[];
  /** 选择回调 */
  onSelect?: (id: string, status: boolean) => void;
  /** 删除回调 isFather：是父级ID，false：为子级ID，默认为 false */
  onDelete?: (id: string, isFather?: boolean) => void;
  /** 编辑回调 */
  onEditInfo?: (value: API.SystemQuestion) => void;
  /** 查看回调 */
  onLookInfo?: (value: API.SystemQuestion) => void;
  /** 解析展示回调 */
  onShowAnalysis?: (id: string) => void;
  choosedEvent?: (value: API.SystemQuestion, status: boolean) => void;
  /** 共享试题，仅个人题库需要 */
  onShare?: (data: API.SystemQuestion, status: boolean) => void;
  /** 撤销共享申请记录 */
  onRevokeShareLog?: (data: API.SystemQuestion) => void;
  /** 刷新回调 */
  onreload?: () => void;
  /** 展示解析 */
  showAnalysis?: Record<string, boolean>;
  /** 组题模式*/
  isGroupModel: boolean;
  /** 是否只读 */
  readonly?: boolean;
  /** 是否可标记 */
  isTag?: boolean;
}

const TopicCard: React.FC<TopicCardProps> = ({
  info,
  isChoosed,
  onSelect,
  allSelect,
  onDelete,
  onEditInfo,
  onLookInfo,
  onShowAnalysis,
  choosedEvent,
  showAnalysis,
  isGroupModel,
  readonly,
  onShare,
  onreload,
  onRevokeShareLog,
  isTag = true,
}) => {
  const { initialState } = useModel('@@initialState');
  const [open, setOpen] = useState(false);
  const [diffVisible, setDiffVisible] = useState(false);
  /** 当前内容 */
  const [currentContent, setCurrentContent] =
    useState<API.SystemQuestion | null>(null);
  const showDiffModal = () => {
    setCurrentContent(info);
    setDiffVisible(true);
  };
  const onUpdateTags = async (questionId: string, tags: string[]) => {
    // 检测是否有重复项
    const hasDuplicates = new Set(tags).size !== tags.length;
    if (hasDuplicates) {
      message.warning('当前标记存在重复项，请检查！');
      return;
    }
    const params = { userId: initialState?.id, questionId, tags: tags };
    const { errCode, msg } = await editUserTag(params);
    if (errCode) {
      message.error('试题标记修改失败，' + msg);
    } else {
      message.success('试题标记修改成功！');
      onreload?.();
      eventBus.notify(); // 通知所有订阅者
    }
  };

  const reviseClassHours = () => {
    setOpen(true);
  };

  return (
    <>
      <div className={classNames('commonWapper', styles.topicCard)}>
        <div className={styles.topicCardHeader}>
          <div className={styles.title}>
            <ConditionalRender
              hasAccess={allSelect?.length !== 0}
              accessComponent={
                <Checkbox
                  checked={allSelect?.includes(info._id)}
                  className={styles.topicCheckbox}
                  onChange={(e) => onSelect?.(info._id, e.target.checked)}
                />
              }
            />
            <div className={styles.topicType}>题型：{info?.type?.name}</div>
            <div className={styles.topicDifficulty}>
              难度：{info?.difficulty?.name}
            </div>
            <div>
              作答时长：{info?.duration ? `${info?.duration} 分钟` : '待填写'}
            </div>

            <ConditionalRender
              hasAccess={(info?.points ?? []).length > 0}
              accessComponent={
                <div className={styles.topicKnowledge}>
                  知识点：
                  <Tooltip
                    title={
                      <Space direction="horizontal" wrap>
                        {info?.points?.map((item) => (
                          <Tag key={item._id}>{item.name}</Tag>
                        ))}
                      </Space>
                    }
                  >
                    <Space>
                      {info?.points?.map((item, index) => {
                        if (index < 3)
                          return <Tag key={item._id}>{item.name}</Tag>;
                        else return null;
                      })}
                      {(info?.points?.length || 0) > 3 && <Tag>...</Tag>}
                    </Space>
                  </Tooltip>
                </div>
              }
            />

            <ConditionalRender
              hasAccess={INDEPENDENT_QUESTION_BANK}
              accessComponent={
                <div className={styles.topicLabel}>类题标签：</div>
              }
            />
          </div>
          <div className={styles.headerOptions}>
            <ConditionalRender
              hasAccess={!!onShare}
              accessComponent={
                <>
                  <Popover
                    placement="topLeft"
                    title="系统检测到试题内容有修改（与学校题库版本不一致），为确保题库内容准确，请确认是否需要重新提交？"
                  >
                    {info.commitAt &&
                      dayjs(info?.updatedAt).isAfter(dayjs(info?.commitAt)) && (
                        <span
                          onClick={() => {
                            showDiffModal();
                          }}
                          style={{ color: '#722ed1' }}
                        >
                          <DiffOutlined /> 更新提醒
                        </span>
                      )}
                  </Popover>

                  <ConditionalRender
                    hasAccess={info?.isShared}
                    accessComponent={
                      <Space>
                        <Tag
                          color={tagColor(info?.checkStatus)}
                          style={{ marginInline: '8px 0' }}
                        >
                          {info?.checkStatus === QuestionCheckStatus['待审核']
                            ? '审核中'
                            : info?.checkStatus ===
                              QuestionCheckStatus['已拒绝']
                            ? '已拒绝'
                            : '已共享'}
                        </Tag>

                        {/* 原有状态判断逻辑保持不变 */}
                        <ConditionalRender
                          hasAccess={
                            info?.checkStatus === QuestionCheckStatus['待审核']
                          }
                          accessComponent={
                            <Popconfirm
                              placement="topRight"
                              title="您确定要撤回共享该试题吗？"
                              okText="继续"
                              onConfirm={() => onShare?.(info, false)}
                            >
                              <span className="delete">撤回共享</span>
                            </Popconfirm>
                          }
                          noAccessComponent={
                            <Popconfirm
                              placement="topRight"
                              title="再次申请"
                              description={
                                info.commitAt &&
                                dayjs(info?.updatedAt).isAfter(
                                  dayjs(info?.commitAt),
                                )
                                  ? '检测到试题有更新，您确定要重新申请共享该试题吗？'
                                  : '您确定要重新申请共享该试题吗？'
                              }
                              okText="继续"
                              onConfirm={() => onShare?.(info, true)}
                            >
                              <span
                                style={{
                                  cursor: 'pointer',
                                  color: 'var(--primary-color)',
                                }}
                              >
                                再次申请
                              </span>
                            </Popconfirm>
                          }
                        />
                        <ConditionalRender
                          hasAccess={
                            info?.checkStatus === QuestionCheckStatus['已拒绝']
                          }
                          accessComponent={
                            <Space>
                              <Popconfirm
                                placement="topRight"
                                title="撤销申请"
                                description={
                                  <>
                                    <div>
                                      撤销申请后，该试题的共享记录将被删除。
                                    </div>
                                    <div>
                                      如果最近一次共享成功，试题将恢复至该状态；否则，所有共享记录将被清除。
                                    </div>
                                  </>
                                }
                                okText="继续"
                                onConfirm={() => {
                                  onRevokeShareLog?.(info);
                                }}
                              >
                                <span
                                  style={{
                                    cursor: 'pointer',
                                    color: '#52C41A',
                                  }}
                                >
                                  撤销申请
                                </span>
                              </Popconfirm>
                              <span
                                className="delete"
                                style={{
                                  cursor: 'pointer',
                                }}
                                onClick={() => {
                                  Modal.info({
                                    title: '拒绝原因',
                                    content: info?.suggestion,
                                  });
                                }}
                              >
                                拒绝原因
                              </span>
                            </Space>
                          }
                        />
                      </Space>
                    }
                    noAccessComponent={
                      <Popconfirm
                        placement="topRight"
                        title="您确定要共享该试题吗？"
                        okText="继续"
                        description={
                          <>
                            <div>
                              共享试题需要被管理员审核，审核通过的试题无法撤回，
                            </div>
                            <div>
                              共享后的试题可在学校题库中查看。是否继续？
                            </div>
                          </>
                        }
                        onConfirm={() => onShare?.(info, true)}
                      >
                        <Button
                          className={styles.share}
                          size="small"
                          type="link"
                        >
                          共享试题
                        </Button>
                      </Popconfirm>
                    }
                  />
                </>
              }
            />
          </div>
        </div>
        <div className={styles.topicCardContent}>
          <TopicContent
            info={info}
            isGroupModel={isGroupModel}
            showAnalysis={showAnalysis}
            onShowAnalysis={onShowAnalysis}
            onDeleteChild={(id, isFather) => {
              onDelete?.(id, isFather);
            }}
          />
        </div>

        <div className={styles.topicCardFooter}>
          <div className={styles.leftpart}>
            <div className={styles.update}>
              <HistoryOutlined className={styles.icon} />
              更新：{dayjs(info.updatedAt).format('YYYY-MM-DD HH:mm')}
            </div>
            <ConditionalRender
              hasAccess={isTag || info?.user_tags?.length}
              accessComponent={
                <div className={styles.update}>
                  <FlagOutlined className={styles.icon} />
                  标记：
                  <Space size={[0, 8]} wrap>
                    {info?.user_tags?.map((item: string) => (
                      <Tag
                        key={item}
                        closable={isTag}
                        onClose={() => {
                          // 删除标记的回调
                          const newTags =
                            info.user_tags?.filter(
                              (tag: string) => tag !== item,
                            ) || [];
                          // 这里调用API更新试题标记
                          onUpdateTags?.(info._id, newTags);
                        }}
                        style={{ marginRight: 3 }}
                      >
                        {item}
                      </Tag>
                    ))}
                    {isTag && info?.user_tags?.length < 3 && (
                      <AddTagButton
                        onAdd={(newTag) => {
                          const newTags = [...(info.user_tags || []), newTag];
                          // 这里调用API更新试题标记
                          onUpdateTags?.(info._id, newTags);
                        }}
                      />
                    )}
                  </Space>
                </div>
              }
            />
          </div>
          <ConditionalRender
            hasAccess={!readonly}
            accessComponent={
              <div className={styles.options}>
                <ConditionalRender
                  hasAccess={!!onLookInfo}
                  accessComponent={
                    <div
                      className={styles.detail}
                      onClick={() => onLookInfo?.(info)}
                    >
                      <EyeOutlined className={styles.icon} />
                      详情
                    </div>
                  }
                />
                <ConditionalRender
                  hasAccess={INDEPENDENT_QUESTION_BANK}
                  accessComponent={
                    <div
                      className={styles.modifyClassHours}
                      onClick={reviseClassHours}
                    >
                      <FormOutlined className={styles.icon} />
                      修改课时
                    </div>
                  }
                />
                <ConditionalRender
                  hasAccess={!!onEditInfo}
                  accessComponent={
                    <div
                      className={styles.edit}
                      onClick={() => onEditInfo?.(info)}
                    >
                      <EditOutlined className={styles.icon} />
                      编辑
                    </div>
                  }
                />
                <ConditionalRender
                  hasAccess={!!onDelete}
                  accessComponent={
                    <Popconfirm
                      title="您确定要删除该试题吗？"
                      description="删除后无法恢复，请谨慎操作！"
                      onConfirm={() => {
                        onDelete?.(info._id, info.isCompose);
                      }}
                    >
                      <div className={styles.delete}>
                        <DeleteOutlined className={styles.icon} />
                        删除
                      </div>
                    </Popconfirm>
                  }
                />
                <ConditionalRender
                  hasAccess={isGroupModel}
                  accessComponent={
                    <>
                      <ConditionalRender
                        hasAccess={isChoosed === false}
                        accessComponent={
                          <div
                            className={styles.edit}
                            onClick={() => {
                              choosedEvent?.(info, true);
                            }}
                          >
                            <PlusOutlined className={styles.icon} />
                            加入试卷
                          </div>
                        }
                      />
                      <ConditionalRender
                        hasAccess={isChoosed === true}
                        accessComponent={
                          <div
                            className={styles.detail}
                            onClick={() => {
                              choosedEvent?.(info, false);
                            }}
                          >
                            <MinusOutlined className={styles.icon} />
                            移除试卷
                          </div>
                        }
                      />
                    </>
                  }
                />
              </div>
            }
          />
        </div>
      </div>
      <ReviseClassHoursModal
        title="修改课时"
        onOk={async () => {
          return false;
        }}
        onCancel={() => {
          setOpen(false);
        }}
        open={open}
      />
      <DiffModal
        diffVisible={diffVisible}
        onCancel={() => {
          setDiffVisible(false);
        }}
        currentQuestion={currentContent}
      />
    </>
  );
};
export default TopicCard;
