import { useCurrentAccess } from '@/common/useCurrentAccess';
import ConditionalRender from '@/components/ConditionalRender';
import {
  create,
  index,
  remove,
  update,
} from '@/services/city_homework_requirement';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProList,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Form, Popconfirm, Space, message } from 'antd';
import React, { useRef, useState } from 'react';
import DurationModal from './DurationModal';
import EditModal from './EditModal';

const Requirement: React.FC = () => {
  const { isAdmin } = useCurrentAccess();
  const actionRef = useRef<ActionType>();
  const actionTableRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [durationModalVisible, setDurationModalVisible] = useState<{
    visible: boolean;
    data?: any;
  }>({
    visible: false,
    data: undefined,
  });
  const [current, setCurrent] = useState<any | undefined>(undefined);
  const [form] = Form.useForm();

  const handleSave = async (values: any) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleSaveDuration = async (values: any) => {
    console.log(values);

    // let response;
    // if (durationModalVisible?.data) {
    //   const { id, ...info } = values;
    //   response = await updateDuration(id, info);
    // } else {
    //   response = await createDuration(values);
    // }
    // if (response.errCode) {
    //   message.error(response.msg);
    // } else {
    //   message.success('操作成功');
    //   actionTableRef?.current?.reload();
    //   setDurationModalVisible({
    //     visible: false,
    //     data: undefined,
    //   });
    // }
  };

  const handleDel = async (record: any) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const handleDelDuration = async (id: string) => {
    console.log(id);

    // const response = await removeDuration(id);
    // if (response.errCode) {
    //   message.error(response.msg);
    // } else {
    //   message.success('删除成功');
    //   actionTableRef?.current?.reload();
    // }
  };

  const columns: ProColumns<any>[] = [
    {
      title: '学科',
      align: 'center',
      dataIndex: 'subject_name',
    },
    {
      title: '年级',
      align: 'center',
      dataIndex: 'grade_name',
    },
    {
      title: '最小时长',
      align: 'center',
      dataIndex: 'min_duration',
      render: (_, record) => {
        return record.min_duration + '分钟';
      },
    },
    {
      title: '最大时长',
      align: 'center',
      dataIndex: 'max_duration',
      render: (_, record) => {
        return record.max_duration + '分钟';
      },
    },
    {
      title: '操作',
      width: 120,
      valueType: 'option',
      key: 'option',
      render: (_, data) => {
        return (
          <ConditionalRender
            hasAccess={isAdmin}
            key={data?.id}
            accessComponent={
              <Space size="large">
                <a
                  onClick={() => {
                    setDurationModalVisible({
                      visible: true,
                      data: data,
                    });
                  }}
                >
                  编辑
                </a>
                <Popconfirm
                  title="确定删除?"
                  onConfirm={() => handleDelDuration(data?.id)}
                >
                  <a className="delete">删除</a>
                </Popconfirm>
              </Space>
            }
          />
        );
      },
    },
  ];

  const test = [
    {
      id: 5,
      subject_id: 1,
      subject_name: '语文',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '3',
      grade_name: '三年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:17:00.000Z',
      updatedAt: '2025-05-12T10:19:33.000Z',
    },
    {
      id: 6,
      subject_id: 4,
      subject_name: '数学',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '3',
      grade_name: '三年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:17:17.000Z',
      updatedAt: '2025-05-12T10:19:38.000Z',
    },
    {
      id: 7,
      subject_id: 7,
      subject_name: '英语',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '3',
      grade_name: '三年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:17:37.000Z',
      updatedAt: '2025-05-12T10:19:42.000Z',
    },
    {
      id: 8,
      subject_id: 1,
      subject_name: '语文',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '4',
      grade_name: '四年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:17:50.000Z',
      updatedAt: '2025-05-12T10:19:47.000Z',
    },
    {
      id: 9,
      subject_id: 4,
      subject_name: '数学',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '4',
      grade_name: '四年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:18:07.000Z',
      updatedAt: '2025-05-12T10:20:13.000Z',
    },
    {
      id: 10,
      subject_id: 7,
      subject_name: '英语',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '4',
      grade_name: '四年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:18:26.000Z',
      updatedAt: '2025-05-12T10:20:19.000Z',
    },
    {
      id: 11,
      subject_id: 1,
      subject_name: '语文',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '5',
      grade_name: '五年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:20:35.000Z',
      updatedAt: '2025-05-12T10:20:35.000Z',
    },
    {
      id: 12,
      subject_id: 4,
      subject_name: '数学',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '5',
      grade_name: '五年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:20:49.000Z',
      updatedAt: '2025-05-12T10:20:49.000Z',
    },
    {
      id: 13,
      subject_id: 7,
      subject_name: '英语',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '5',
      grade_name: '五年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:21:02.000Z',
      updatedAt: '2025-05-12T10:21:02.000Z',
    },
    {
      id: 14,
      subject_id: 1,
      subject_name: '语文',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '6',
      grade_name: '六年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:21:18.000Z',
      updatedAt: '2025-05-12T10:21:18.000Z',
    },
    {
      id: 15,
      subject_id: 4,
      subject_name: '数学',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '6',
      grade_name: '六年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:21:37.000Z',
      updatedAt: '2025-05-12T10:21:37.000Z',
    },
    {
      id: 16,
      subject_id: 7,
      subject_name: '英语',
      grade_section_code: 'GRADE_PRIMARY',
      grade_section_name: '小学',
      grade_code: '6',
      grade_name: '六年级',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:21:48.000Z',
      updatedAt: '2025-05-12T10:21:48.000Z',
    },
    {
      id: 17,
      subject_id: 2,
      subject_name: '语文',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '7',
      grade_name: '初一',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:22:13.000Z',
      updatedAt: '2025-05-12T10:22:13.000Z',
    },
    {
      id: 18,
      subject_id: 5,
      subject_name: '数学',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '7',
      grade_name: '初一',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:22:29.000Z',
      updatedAt: '2025-05-12T10:22:29.000Z',
    },
    {
      id: 19,
      subject_id: 8,
      subject_name: '英语',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '7',
      grade_name: '初一',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:22:45.000Z',
      updatedAt: '2025-05-12T10:22:45.000Z',
    },
    {
      id: 20,
      subject_id: 2,
      subject_name: '语文',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '8',
      grade_name: '初二',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:23:05.000Z',
      updatedAt: '2025-05-12T10:23:05.000Z',
    },
    {
      id: 21,
      subject_id: 5,
      subject_name: '数学',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '8',
      grade_name: '初二',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:23:29.000Z',
      updatedAt: '2025-05-12T10:23:42.000Z',
    },
    {
      id: 22,
      subject_id: 8,
      subject_name: '英语',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '8',
      grade_name: '初二',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:23:56.000Z',
      updatedAt: '2025-05-12T10:23:56.000Z',
    },
    {
      id: 23,
      subject_id: 2,
      subject_name: '语文',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '9',
      grade_name: '初三',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:24:11.000Z',
      updatedAt: '2025-05-12T10:24:11.000Z',
    },
    {
      id: 24,
      subject_id: 5,
      subject_name: '数学',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '9',
      grade_name: '初三',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:24:22.000Z',
      updatedAt: '2025-05-12T10:24:22.000Z',
    },
    {
      id: 24,
      subject_id: 5,
      subject_name: '英语',
      grade_section_code: 'GRADE_MIDDLE',
      grade_section_name: '初中',
      grade_code: '9',
      grade_name: '初三',
      min_duration: '20',
      max_duration: '30',
      createdAt: '2025-05-12T10:24:22.000Z',
      updatedAt: '2025-05-12T10:24:22.000Z',
    },
  ];

  return (
    <>
      <ProTable
        columns={columns}
        actionRef={actionTableRef}
        cardBordered
        toolBarRender={() => {
          return [
            <ConditionalRender
              hasAccess={isAdmin}
              key="add"
              accessComponent={
                <Button
                  type="primary"
                  onClick={() => {
                    setDurationModalVisible({
                      visible: true,
                      data: undefined,
                    });
                  }}
                  icon={<PlusOutlined />}
                >
                  新增
                </Button>
              }
            />,
          ];
        }}
        dataSource={test}
        // request={async (params: any) => {
        //   const { current, pageSize } = params;
        //   const { errCode, msg, data } = await getDuration({
        //     offset: Number((current || 1) - 1) * Number(pageSize || 10),
        //     limit: Number(pageSize || 10),
        //   });
        //   if (errCode) {
        //     message.error(msg || '查询失败');
        //     return {
        //       data: [],
        //       total: 0,
        //     };
        //   }
        //   return {
        //     data: data.list,
        //     total: data.total,
        //   };
        // }}
        pagination={{
          pageSize: 10,
        }}
        options={false}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={false}
        form={{
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        dateFormatter="string"
        headerTitle="学科作业时长维护"
        style={{
          marginBottom: 16,
        }}
      />
      <ProList<any>
        actionRef={actionRef}
        toolBarRender={() => {
          return [
            <ConditionalRender
              hasAccess={isAdmin}
              key="add"
              accessComponent={
                <Button
                  type="primary"
                  onClick={() => {
                    setCurrent(undefined);
                    setModalVisible(true);
                  }}
                  icon={<PlusOutlined />}
                >
                  新增
                </Button>
              }
            />,
          ];
        }}
        search={{
          filterType: 'light',
        }}
        rowKey="id"
        headerTitle="学科设计要求管理"
        request={async (params: any) => {
          const { current, pageSize, name, describe } = params;
          const { errCode, msg, data } = await index({
            offset: Number((current || 1) - 1) * Number(pageSize || 10),
            limit: Number(pageSize || 10),
            name: name || undefined,
            describe: describe || undefined,
          });
          if (errCode) {
            message.error(msg || '作业目标列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data.list,
            total: data.total,
          };
        }}
        pagination={{
          pageSize: 10,
        }}
        showActions="hover"
        metas={{
          title: {
            dataIndex: 'name',
            search: false,
            render: (_, row) => {
              return (
                <>
                  <a>
                    {row.grade_section_name}
                    {row.grade_name}
                    {row.subject_name}
                  </a>
                </>
              );
            },
          },
          description: {
            dataIndex: 'require_detail',
            search: false,
          },
          actions: {
            render: (text, row) => {
              return (
                <ConditionalRender
                  hasAccess={isAdmin}
                  key={row?.id}
                  accessComponent={
                    <Space>
                      <Button
                        type="link"
                        onClick={() => {
                          setCurrent(row);
                          setModalVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                      <Popconfirm
                        title="确认删除？"
                        onConfirm={() => {
                          handleDel(row);
                        }}
                      >
                        <Button type="link" danger>
                          删除
                        </Button>
                      </Popconfirm>
                    </Space>
                  }
                />
              );
            },
          },
        }}
      />
      <EditModal
        form={form}
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
      <DurationModal
        form={form}
        open={durationModalVisible.visible}
        info={durationModalVisible.data}
        onClose={() =>
          setDurationModalVisible({
            visible: false,
            data: undefined,
          })
        }
        onSave={handleSaveDuration}
      />
    </>
  );
};

export default Requirement;
