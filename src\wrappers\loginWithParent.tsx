/*
 * @Description: 检测外部传入ssoToken的情况，若用户未登录，会先去403，再进业务界面，这种情况403中会删除ssoToken
 * 这个包裹组件是处理不跳403但url中有ssoToken的情况，进入不鉴权的界面，或登录后url中又出现ssoToken的场景
 * @Date: 2025-04-21 16:16:18
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-05-09 16:51:58
 */
import { SSOState } from '@/models/sso_3th';
import { getSSOToken, setSSOToken } from '@/utils/auth';
import {
  AnyAction,
  connect,
  Dispatch,
  Outlet,
  terminal,
  useSearchParams,
} from '@umijs/max';
import { useEffect, useState } from 'react';

const LoginWithParent: React.FC<{
  sso_3th: SSOState;
  dispatch: Dispatch<AnyAction>;
  loading: {
    global: boolean;
    models: { [key: string]: boolean };
    effects: { [key: string]: boolean };
  };
}> = ({ sso_3th, dispatch, loading }) => {
  const [query] = useSearchParams();
  const token = query.get('token');
  const clearToken = query.get('clearToken');

  // 是否需要重新登录
  const [needLogin, setNeedLogin] = useState<boolean>(false);
  const [currentToken] = useState<string | null>(token);

  useEffect(() => {
    // 只有在SSO模式下才处理第三方token参数
    if (AUTH_MODE === 'sso' && token && token !== 'null') {
      terminal.log('url中包含token');
      // 清除token参数，防止发生复解析
      query.delete('token');
      window.history.replaceState(
        {},
        '',
        `${location.pathname}${query.toString() ? `?${query.toString()}` : ''}`,
      );

      // 设置嵌套标识
      sessionStorage.setItem('homework-ischild', 'true');

      // 发起解析
      const oldSSOToken = getSSOToken();
      if (
        (!oldSSOToken || oldSSOToken !== token) &&
        !loading.effects['sso_3th/parseSsoToken']
      ) {
        terminal.log('开始解析ssoToken');
        setSSOToken(token);
        setNeedLogin(true);
      }
    }
  }, [token]);

  useEffect(() => {
    console.log('needLogin', needLogin);
    console.log('currentToken', currentToken);
    if (needLogin && currentToken) {
      if (!sso_3th.done && !loading.effects['sso_3th/login']) {
        terminal.log('开始sso自动登录');
        dispatch({
          type: 'sso_3th/login',
          payload: { token: currentToken },
        });
      }
      if (sso_3th.done) {
        terminal.log('sso自动登录成功');
        const url = new URL(location.href);
        url.searchParams.delete('token');
        location.href = url.toString();
        // refresh().then(() => {
        //   setTimeout(() => {
        //     query.delete('token');
        //     history.replace(
        //       `${pathname}${query.toString() ? `?${query.toString()}` : ''}`,
        //     );
        //   }, 200);
        // });
      }
    }
  }, [needLogin, currentToken, loading.effects['sso_3th/login']]);

  useEffect(() => {
    if (clearToken) {
      sessionStorage.removeItem('homework-ischild');
    }
  }, [clearToken]);

  return <Outlet />;
};

export default connect(({ sso_3th, loading }) => ({ sso_3th, loading }))(
  LoginWithParent,
);
