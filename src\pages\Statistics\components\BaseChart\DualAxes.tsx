import { DualAxes } from '@ant-design/plots';
import React from 'react';
import NoData<PERSON><PERSON> from './NoDataChart';

interface DualAxesChartProps {
  data?: { time: string; value: number; count: number }[];
  loading?: boolean;
  field?: [string, string];
}

const DualAxesChart: React.FC<DualAxesChartProps> = ({ data, loading }) => {
  const config = {
    data,
    height: 270,
    xField: 'month',
    children: [
      {
        type: 'interval',
        yField: 'total',
        style: {
          // 圆角样式
          radiusTopLeft: 10,
          radiusTopRight: 10,
          maxWidth: 30,
        },
        label: { position: 'inside' },
        interaction: {
          elementHighlight: true,
          elementHighlightByColor: { background: true },
        },
      },
      {
        type: 'line',
        yField: 'rate',
        shapeField: 'smooth',
        style: { lineWidth: 2 },
        axis: { y: { position: 'right' } },
        interaction: {
          tooltip: {
            crosshairs: false,
            marker: false,
          },
        },
      },
    ],
  };

  return (
    <NoDataChart data={data} height={270} loading={loading}>
      <DualAxes {...config} />
    </NoDataChart>
  );
};
export default DualAxesChart;
