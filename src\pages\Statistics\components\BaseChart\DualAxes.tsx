import { DualAxes } from '@ant-design/plots';
import React from 'react';
import NoD<PERSON><PERSON><PERSON> from './NoDataChart';

interface DualAxesChartProps {
  data?: { time: string; value: number; count: number }[];
  loading?: boolean;
  field?: [string, string];
}

const DualAxesChart: React.FC<DualAxesChartProps> = ({ data, loading }) => {
  const config = {
    data,
    height: 270,
    xField: 'month',
    children: [
      {
        type: 'interval',
        yField: '数量',
        style: {
          // 圆角样式
          radiusTopLeft: 10,
          radiusTopRight: 10,
          maxWidth: 30,
        },
        label: { position: 'inside' },
        interaction: {
          elementHighlight: true,
          elementHighlightByColor: { background: true },
        },
      },
      {
        type: 'line',
        yField: '环比',
        shapeField: 'smooth',
        style: { lineWidth: 2 },
        axis: {
          y: {
            position: 'right',
          },
        },
        interaction: {
          tooltip: {
            crosshairs: false,
            marker: false,
          },
        },
      },
    ],
    interaction: {
      elementHighlight: { background: true },
      tooltip: {
        render: (_: any, { title, items }: any) => {
          return (
            <div>
              <h4>{title}</h4>
              {items.map((item: any) => {
                const { value, color, name } = item;
                return (
                  <div key={color}>
                    <div
                      style={{
                        margin: 0,
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <div>
                        <span
                          style={{
                            display: 'inline-block',
                            width: 6,
                            height: 6,
                            borderRadius: '50%',
                            backgroundColor: color,
                            marginRight: 6,
                          }}
                        ></span>
                        <span>{name}</span>
                      </div>
                      <b>
                        {name === '环比'
                          ? value === 0
                            ? 0
                            : `${value}%`
                          : value}
                      </b>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        },
      },
    },
  };

  return (
    <NoDataChart data={data} height={270} loading={loading}>
      <DualAxes {...config} />
    </NoDataChart>
  );
};
export default DualAxesChart;
