import { styled } from '@umijs/max';
import { Tree } from 'antd';

export const TreeStyled = styled(Tree)`
  .ant-tree-treenode {
    .ant-tree-node-content-wrapper {
      .ant-tree-title {
        .options {
          display: none;
        }
      }
      &.ant-tree-node-selected,
      &:hover {
        background-color: rgba(41, 121, 255, 0.2);
        .ant-tree-title {
          .options {
            display: flex;
            justify-content: space-around;
            width: 2em;
          }
        }
      }
    }
  }
`;
