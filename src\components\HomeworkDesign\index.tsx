import { Alert, Empty } from 'antd';
import { useEffect, useState } from 'react';
import ConditionalRender from '../ConditionalRender';
import styles from './index.less';
import PaperQuestionList from './PaperQuestionList';

const HomeworkDesign = ({
  type,
  settingScore = false,
  showDuration = true,
  questionConstrue,
  questionList,
  sortQuestion,
  replaceQuestion,
}: {
  type?: string;
  showDuration?: boolean;
  settingScore?: boolean;
  questionConstrue: any;
  questionList: any;
  sortQuestion: React.Dispatch<any>;
  replaceQuestion?: React.Dispatch<React.SetStateAction<API.SystemQuestion[]>>;
  getNewData?: () => void;
}) => {
  const [listData, setListData] = useState<any>();
  const convertPaperData = (structure: any, list: any) => {
    if (!structure || !list) return;
    const layout = [];
    const isHidden = structure?.find(
      (v: { id: string }) => v?.id?.indexOf('hidden') !== -1,
    );

    const ids = list?.map((v: { _id: string }) => v._id);
    for (let index = 0; index < structure.length; index++) {
      const { id, name, questionIds } = structure[index];
      let scoreTotal = 0;
      const groupedData = questionIds?.reduce((acc: any, item: string) => {
        const curQz = list.find((v: { _id?: string }) => v?._id === item);
        scoreTotal += curQz?.score || 0;
        acc.push({ ...curQz, ids });
        return acc;
      }, []);
      layout.push({
        id,
        name,
        score: scoreTotal,
        orderIndex: isHidden ? index - 1 : index,
        children: groupedData,
      });
    }
    return layout;
  };
  useEffect(() => {
    const { bigQuestions, bigQuestionOrder = [] } = questionConstrue || {};
    if (bigQuestions && bigQuestionOrder?.length > 0) {
      // 试题结构根据 order 数组的顺序重新排序
      const sortedArray = [
        ...bigQuestionOrder.map((key: string) => bigQuestions[key]),
      ];
      const partData = convertPaperData(sortedArray, questionList);
      setListData(partData);
    } else {
      setListData([]);
    }
  }, [questionConstrue, questionList]);

  return (
    <>
      <div className={styles.container}>
        <ConditionalRender
          hasAccess={!!listData?.length}
          accessComponent={
            <div className={styles.paperWrapper}>
              <ConditionalRender
                hasAccess={!type}
                accessComponent={
                  <Alert
                    message="注意：题目排序，内容等发生变更，均需要点击“保存试卷”按钮保存本次修改。"
                    banner
                  />
                }
              />
              <div className={`${styles.contentRightBody} card-wrapper`}>
                <PaperQuestionList
                  showDuration={showDuration}
                  settingScore={settingScore}
                  actionType={!type}
                  data={listData}
                  sortQuestion={sortQuestion}
                  replaceQuestion={replaceQuestion}
                />
              </div>
            </div>
          }
          noAccessComponent={
            <Empty
              className="commonPositionCenter"
              description="未查询到试题内容，请先添加试题！"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          }
        />
      </div>
    </>
  );
};

export default HomeworkDesign;
