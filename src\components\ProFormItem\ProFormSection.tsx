/*
 * @Description: 学段选择组件,会自动获取并显示学段数据，在ProForm中使用
 * @Date: 2025-02-22 11:47:16
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-22 15:19:22
 */
import { DictionarieState } from '@/models/dictionarie';
import { ProForm, ProFormItemProps } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Segmented } from 'antd';
import React from 'react';
import ColWrapper from '../Wrapper/ColWrapper';

const ProFormSection: React.FC<
  ProFormItemProps & {
    dictionarie: DictionarieState;
  }
> = (prop) => {
  const { dictionarie, colProps, ...others } = prop;
  return (
    <ColWrapper colProps={colProps}>
      <ProForm.Item {...others}>
        <Segmented
          options={dictionarie.list
            .filter((item) => item.type === 'grade_section')
            .map((item) => ({
              label: item.name,
              value: item.code,
            }))}
        />
      </ProForm.Item>
    </ColWrapper>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(ProFormSection);
