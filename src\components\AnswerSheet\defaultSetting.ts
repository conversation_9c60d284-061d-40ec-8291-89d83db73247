export const pageData: any = {
  lineHeight: 1.5,
  templateFontFamily: 'Microsoft YaHei',
  templateFontSize: 16,
  pageNumberHeight: 30,
  loading: !1,
  pagePadding: [66, 47],
  preloadWidth: 898, //992-47-47
  layoutType: 'a4',
  currentLayout: {
    title: 'A4',
    page: '992*1403',
    viewScale: 1,
    column: 1,
    gap: 0,
    printSize: [210, 297],
    orientation: 'portrait',
    isContentColumn: false,
    columnGap: 0,
    layoutType: 'a4',
  },
  pageSize: {
    h: 1403,
    w: 992,
  },
  // preloadWidth: 1891, //1985-47-47
  // layoutType: 'a3',
  // currentLayout: {
  //   title: 'A3',
  //   page: '1985*1403',
  //   viewScale: 1,
  //   column: 1,
  //   gap: 0,
  //   printSize: [210, 297],
  //   orientation: 'portrait',
  //   isContentColumn: false,
  //   columnGap: 0,
  //   layoutType: 'a4',
  // },
  // pageSize: {
  //   h: 991,
  //   w: 1985,
  // },
  // preloadWidth: 606, //700-47-47
  // layoutType: 'a5',
  // currentLayout: {
  //   title: 'A5',
  //   page: '700*991',
  //   viewScale: 1,
  //   column: 1,
  //   gap: 0,
  //   printSize: [210, 297],
  //   orientation: 'portrait',
  //   isContentColumn: false,
  //   columnGap: 0,
  //   layoutType: 'a4',
  // },
  // pageSize: {
  //   h: 991,
  //   w: 700,
  // },
  headHeight: 0,
  questionNameHeight: 40,
  totalPages: 1,
  pageContent: [],
};
export const fontFamilyOptions: any = [
  {
    label: '微软雅黑',
    value: 'Microsoft YaHei, Helvetica Neue, PingFang SC, sans-serif',
  },
  { label: '宋体', value: 'SimSun, serif' },
  { label: '楷体', value: 'KaiTi, STKaiti, serif' },
  { label: '仿宋', value: 'FangSong, STFangsong, serif' },
  { label: '等线', value: 'DengXian, Microsoft YaHei, sans-serif' },
  { label: '黑体', value: 'SimHei, Microsoft YaHei, sans-serif' },
  { label: 'Arial', value: 'Arial, Helvetica, sans-serif' },
  { label: 'Times New Roman', value: 'Times New Roman, Times, serif' },
  { label: 'Courier New', value: 'Courier New, Courier, monospace' },
  { label: 'Verdana', value: 'Verdana, Geneva, sans-serif' },
  { label: 'Georgia', value: 'Georgia, serif' },
  { label: 'Tahoma', value: 'Tahoma, Geneva, sans-serif' },
  { label: 'Trebuchet MS', value: 'Trebuchet MS, sans-serif' },
  { label: 'Impact', value: 'Impact, Charcoal, sans-serif' },
  { label: 'Comic Sans MS', value: 'Comic Sans MS, cursive' },
  { label: 'Arial Black', value: 'Arial Black, Gadget, sans-serif' },
  {
    label: 'Lucida Sans Unicode',
    value: 'Lucida Sans Unicode, Lucida Grande, sans-serif',
  },
  {
    label: 'Palatino Linotype',
    value: 'Palatino Linotype, Book Antiqua, Palatino, serif',
  },
];
export const fontSizeOptions: any = [
  { label: '10px', value: 10 },
  { label: '12px', value: 12 },
  { label: '14px', value: 14 },
  { label: '16px', value: 16 },
  { label: '18px', value: 18 },
  { label: '20px', value: 20 },
  { label: '22px', value: 22 },
  { label: '24px', value: 24 },
  { label: '26px', value: 26 },
  { label: '28px', value: 28 },
  { label: '30px', value: 30 },
  { label: '32px', value: 32 },
  { label: '34px', value: 34 },
  { label: '36px', value: 36 },
  { label: '38px', value: 38 },
  { label: '40px', value: 40 },
];
export const lineHeightOptions: any = [
  { label: '1.0', value: 1 },
  { label: '1.2', value: 1.2 },
  { label: '1.5', value: 1.5 },
  { label: '1.7', value: 1.7 },
  { label: '2.0', value: 2 },
  { label: '2.5', value: 2.5 },
  { label: '3.0', value: 3 },
  { label: '3.5', value: 3.5 },
  { label: '4.0', value: 4 },
  { label: '4.5', value: 4.5 },
  { label: '5.0', value: 5 },
];
