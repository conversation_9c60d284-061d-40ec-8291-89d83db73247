.questionArea {
  .similarModal {
    .titleBar {
      font-size: 16px;
      line-height: 50px;
      border-bottom: 1px solid #dadada;
    }

    div[class*='ant-tabs-top'] {
      .questionItem {
        box-shadow: 1px 1px 2px rgba(0, 0, 0, 10%),
          -1px -1px 2px rgba(0, 0, 0, 10%);

        &:hover {
          border: none;
        }

        .questionItemTop {
          color: var(--tip-color);
          font-size: 12px;
          line-height: 30px;

          span + span {
            margin-left: 8px;
            padding-left: 8px;
            border-left: 1px solid #dadada;
          }
        }
      }
    }
  }
}

.questionPart,
.analysisModal,
.similarModal {
  position: relative;
  padding: 12px;

  .questionPartTitle {
    color: var(--sub-color);
    font-weight: bold;
    font-size: 16px;
    line-height: 40px;

    p {
      // text-indent: 0 !important;
    }
  }

  &.border:hover {
    border: 1px solid var(--primary-color-5);
    border-radius: 6px;

    > .questionOperation {
      display: flex;

      li {
        cursor: pointer;
      }
    }
  }

  .questionItem {
    position: relative;
    padding: 12px;

    &:hover {
      border: 1px solid var(--primary-color-5);
      border-radius: 6px;

      .questionOperation {
        display: flex;

        li {
          cursor: pointer;
        }
      }
    }

    p {
      margin-bottom: 0 !important;
    }

    .questionItemContent {
      display: flex;
      line-height: 28px;

      &:nth-child(1) {
        padding-top: 0;
      }

      div[class*='questionNum'] {
        line-height: 28px;
      }
    }

    div[class*='questionTitle'] {
      flex: 1;
      line-height: 28px;

      * {
        max-width: 100%;
        line-height: 28px;
        word-wrap: break-word;
        word-break: break-word;
        white-space: break-spaces;
      }

      p {
        margin-bottom: 0;
        // text-indent: 0 !important;
      }
    }
  }
  // .hide {
  //   display: none;
  // }

  .questionOperation {
    position: absolute;
    top: -39px;
    right: 0;
    display: none;
    padding: 4px 8px;
    color: #fff;
    line-height: 30px;
    background-color: var(--primary-color);
    border-radius: 4px 4px 0 0;
    list-style: none;

    li {
      padding: 0 6px;

      span {
        margin-right: 6px;
      }
    }
  }
}

.analysisModal,
.similarModal {
  .ant-tabs-content-holder {
    height: 50vh;
    overflow-y: auto;
  }

  .questionItem {
    &:hover {
      border: none;
    }
  }
}

.questionType {
  .questionPart {
    &:nth-child(1),
    &.hideUp + .questionPart {
      > .questionOperation {
        > li:nth-child(2) {
          color: rgba(255, 255, 255, 50%);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }

    &:nth-last-child(1) {
      > .questionOperation {
        > li:nth-child(3) {
          color: rgba(255, 255, 255, 50%);
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
