import { DictionarieState } from '@/models/dictionarie';
import { index as publishersIndex } from '@/services/publishers';
import {
  ModalForm,
  ProFormGroup,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { message } from 'antd';
import React, { useRef } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Textbook;
  currentSubject?: API.Subject;
  onSave: (info: API.Textbook) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  currentSubject,
  onSave,
  onClose,
  dictionarie,
}) => {
  const formRef = useRef<ProFormInstance<API.Textbook>>();

  return (
    <ModalForm<API.Textbook>
      title={info ? '编辑教材版本' : '注册教材版本'}
      formRef={formRef}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info || { subject: currentSubject, volume: '上册' }}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText
        name="subject"
        label="已选学科"
        rules={[{ required: true, message: '请选择学科！' }]}
        convertValue={(value?: API.Subject) => {
          if (value) {
            const section = dictionarie.list.find(
              (item) => item.code === value.grade_section,
            )?.name;
            return `${section} / ${value.subject}`;
          }
          return '';
        }}
        readonly
      />
      <ProFormText
        name="textbook_version"
        label="教材版本"
        rules={[{ required: true, message: '请输入教材版本名称！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormSelect
        name="publisher_id"
        label="出版单位"
        request={async () => {
          const { errCode, data, msg } = await publishersIndex({});
          if (errCode) {
            message.error(msg || '获取出版单位列表失败');
            return [];
          }
          return (data.list || []).map((item) => ({
            label: item.name,
            value: item.id,
          }));
        }}
        colProps={{ span: 12 }}
        placeholder="请选择出版商"
      />
      <ProFormGroup>
        <ProFormText name="alias" label="别名" colProps={{ span: 12 }} />
      </ProFormGroup>
    </ModalForm>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(EditModal);
