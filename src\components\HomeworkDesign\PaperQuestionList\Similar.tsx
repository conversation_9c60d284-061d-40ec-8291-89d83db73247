import { getSimilarQuestions } from '@/services/common_question';
import { ReloadOutlined } from '@ant-design/icons';
import { Button, Col, Empty, Modal, Row, Tabs, message } from 'antd';
import moment from 'moment';
import { Dispatch, useEffect, useState } from 'react';
import styles from './index.less';
import QuestionPart from './QuestionPart';

const Similar = ({
  open,
  hideModal,
  data,
  replaceQuestion,
  replaceStructure,
}: {
  open: boolean;
  hideModal: () => void;
  data?: any;
  reolad?: () => void;
  replaceStructure: Dispatch<any>;
  replaceQuestion?: React.Dispatch<React.SetStateAction<API.SystemQuestion[]>>;
}) => {
  const { question } = data;
  // 获取试题
  const [questionData, setQuestionData] = useState<any>();
  const getData = async (ids: string[]) => {
    const {
      errCode,
      msg,
      data: res,
    } = await getSimilarQuestions(question?.tableName, {
      num: 3,
      ids,
      catalog: question?.catalog?.id,
      type: question?.type?.code,
    });
    if (errCode) {
      message.error('试题数据获取失败：' + msg);
      setQuestionData([]);
    } else if (res?.list?.length) {
      setQuestionData(res?.list);
    } else {
      if (questionData?.length) {
        message.warning('没有更多试题！');
      }
    }
  };
  useEffect(() => {
    if (data) {
      getData(data.ids);
    }
  }, [data]);

  return (
    <Modal
      destroyOnClose
      open={open}
      onOk={hideModal}
      onCancel={hideModal}
      okText="确认"
      cancelText="取消"
      width={1000}
      centered
      getContainer={false}
      classNames={{
        body: styles.similarModal,
      }}
      footer={null}
    >
      <Row gutter={[24, 24]}>
        <Col span={12}>
          <div className={styles.titleBar}>原题</div>
          <div className={styles.questionItem}>
            <QuestionPart
              data={{
                question: question,
              }}
            />
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.titleBar}>更换题目</div>
          {questionData?.length ? (
            <Tabs
              defaultActiveKey="1"
              items={new Array(questionData?.length).fill(null).map((_, i) => {
                const id = String(i + 1);
                const val = questionData[i];
                return {
                  label: (
                    <div
                      style={{
                        padding: '0 16px',
                      }}
                    >
                      {id}
                    </div>
                  ),
                  key: id,
                  children: (
                    <div className={styles.questionItem}>
                      <div className={styles.questionItemTop}>
                        {data?.type ? <span>题型：{data?.type}</span> : ''}
                        {data?.difficulty ? (
                          <span>难度：{data?.difficulty}</span>
                        ) : (
                          ''
                        )}
                      </div>
                      <QuestionPart
                        data={{
                          question: val,
                        }}
                      />
                      <div
                        style={{
                          marginTop: 8,
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div className={styles.questionItemTop}>
                          <span>
                            更新：
                            {moment(val?.updatedAt).format('YYYY-MM-DD HH:mm')}
                          </span>
                        </div>
                        <Button
                          type="primary"
                          onClick={async (e) => {
                            e.stopPropagation();
                            replaceQuestion?.(
                              (prevData: API.SystemQuestion[]) => {
                                return prevData.map((v) =>
                                  v._id === question._id ? { ...val } : v,
                                );
                              },
                            );
                            replaceStructure((prevData: any) => {
                              const { pId } = data;
                              const current = prevData.bigQuestions[pId];

                              // 根据题目ID匹配替换，而不是根据下标
                              const newQuestionIds = current.questionIds.map(
                                (id: string) =>
                                  id === question._id ? val._id : id,
                              );

                              return {
                                ...prevData,
                                bigQuestions: {
                                  ...prevData.bigQuestions,
                                  [pId]: {
                                    ...current,
                                    questionIds: newQuestionIds,
                                  },
                                },
                              };
                            });
                            hideModal();
                          }}
                        >
                          替换
                        </Button>
                      </div>
                    </div>
                  ),
                };
              })}
              tabBarExtraContent={
                <Button
                  type="link"
                  onClick={() => {
                    const ids = questionData?.map(
                      (v: { _id: string }) => v._id,
                    );
                    const newIds = (data?.ids || []).concat(ids);
                    getData(newIds);
                  }}
                >
                  <ReloadOutlined />
                  换一换
                </Button>
              }
            />
          ) : (
            <div>
              <Empty description="未查询到试题" />
            </div>
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default Similar;
