import { DictionarieState } from '@/models/dictionarie';
import { connect } from '@umijs/max';
import { Col, ColProps, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { RadioGroupStyled } from './styled';

const GradeSection: React.FC<{
  initalValue?: string;
  onChange?: (value?: API.Dictionarie) => void;
  /** 标题列属性 */
  labelCol?: ColProps;
  /** 自定义行样式 */
  rowStyles?: React.CSSProperties;
  dictionarie: DictionarieState;
}> = ({ initalValue, onChange, labelCol, rowStyles, dictionarie }) => {
  const [value, setValue] = useState<string | undefined>(initalValue);

  useEffect(() => {
    if (initalValue) {
      setValue(initalValue);
      const item = dictionarie.list.find(
        (d) => d.type === 'grade_section' && d.code === initalValue,
      );
      onChange?.(item);
    } else {
      const item = dictionarie.list.find((d) => d.type === 'grade_section');
      setValue(item?.code);
      onChange?.(item);
    }
  }, [initalValue]);

  return (
    <Row className={styles.row} style={rowStyles}>
      <Col className={styles.label} {...labelCol}>
        <label>学段</label>
      </Col>
      <Col>
        <RadioGroupStyled
          size="small"
          options={dictionarie.list
            .filter((d) => d.type === 'grade_section')
            .map((d) => ({
              label: d.name,
              value: d.code,
              title: d.description || d.name,
            }))}
          onChange={(e) => {
            setValue(e.target.value);
            const item = dictionarie.list.find(
              (d) => d.type === 'grade_section' && d.code === e.target.value,
            );
            onChange?.(item);
          }}
          value={value}
          optionType="button"
          buttonStyle="solid"
        />
      </Col>
    </Row>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(GradeSection);
