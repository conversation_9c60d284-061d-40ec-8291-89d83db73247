import { convertNumber } from '@/utils/calc';
import { EditOutlined } from '@ant-design/icons';
import { Button, Flex } from 'antd';
import { Draggable, Droppable } from 'react-beautiful-dnd';
import styles from '../common.less';

const TypeGrouped = ({
  index,
  bigQuestion,
  handleEvent,
}: {
  index: number;
  bigQuestion: BigQuestion;
  handleEvent: (type: string, data: any) => void;
}) => {
  const questions = bigQuestion?.questionIds;
  return (
    <div className={styles.questionArea}>
      {index !== -1 && (
        <Flex
          gap="middle"
          align="center"
          justify="space-between"
          className={styles.titleArea}
        >
          <h3 className={styles.titleNum}>
            {convertNumber(index + 1)}、 {bigQuestion?.name}
            <EditOutlined
              onClick={() => {
                if (bigQuestion?.id) {
                  const { id, name } = bigQuestion;
                  handleEvent('edit', { id, name });
                }
              }}
            />
          </h3>
          <div className={styles.action}>
            <Button
              type="link"
              danger
              onClick={() => {
                if (bigQuestion?.id) {
                  handleEvent('delete', {
                    id: bigQuestion?.id,
                  });
                }
              }}
            >
              删除
            </Button>
            {/* <Dropdown
              menu={{
                items: [
                  {
                    key: '1',
                    label: <a>难度从高到底</a>,
                  },
                  {
                    key: '2',
                    label: <a>难度从低到高</a>,
                  },
                ],
              }}
              placement="bottom"
            >
              <Button type="link">排序</Button>
            </Dropdown> */}
          </div>
        </Flex>
      )}
      <Droppable
        droppableId={bigQuestion?.id || ''}
        direction="horizontal"
        type="question"
      >
        {(provided, snapshot) => {
          return (
            <div
              className={styles.question}
              {...provided.droppableProps}
              ref={provided.innerRef}
              style={{
                background: snapshot.isDraggingOver ? '#c1e2ff' : '',
              }}
            >
              {questions?.map((question, index) => {
                if (!question)
                  return (
                    <div key={index + 1}>
                      <span
                        style={{
                          borderColor: 'red',
                          color: 'red',
                          cursor: 'not-allowed',
                        }}
                      >
                        {index + 1}
                      </span>
                    </div>
                  );
                return (
                  <Draggable
                    draggableId={question}
                    index={index}
                    key={question}
                  >
                    {(provided) => {
                      return (
                        <div
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          ref={provided.innerRef}
                        >
                          <span>{index + 1}</span>
                        </div>
                      );
                    }}
                  </Draggable>
                );
              })}
              {provided.placeholder}
            </div>
          );
        }}
      </Droppable>
    </div>
  );
};

export default TypeGrouped;
