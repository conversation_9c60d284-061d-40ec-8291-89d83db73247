/*
 * @Description: 教材名录管理，指定每个年级都有哪些教材以及册次信息
 * @Date: 2025-03-07 17:04:49
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-12 14:37:00
 */
import CommonSelect from '@/components/CommonSelect';
import CustomCollapse from '@/components/CustomCollapse';
import { SECTION_GRADE } from '@/constants';
import { DictionarieState } from '@/models/dictionarie';
import {
  create,
  index as index_checklist,
  remove,
  update,
} from '@/services/textbook_checklist';
import { index as index_textbooks } from '@/services/textbooks';
import {
  ActionType,
  ProCard,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Button, message, Popconfirm, Space, Tree, TreeProps } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import EditModal from './EditModale';

const TextbookChecklist: React.FC<{ dictionarie: DictionarieState }> = ({
  dictionarie,
}) => {
  const actionRef = useRef<ActionType>();
  const [currentSubject, setCurrentSubject] = useState<API.Subject>();
  const [gradeTreeData, setGradeTreeData] = useState<TreeProps['treeData']>([]);
  const [currentGrade, setCurrentGrade] = useState<API.Dictionarie>();
  const [currentTextbookList, setCurrentTextbookList] = useState<
    API.Textbook[]
  >([]);

  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.TextbookChecklist | undefined>(
    undefined,
  );
  const [title, setTitle] = useState<string>('查询条件');

  const handleSave = useCallback(
    async (values: API.TextbookChecklist) => {
      let response;
      if (!currentGrade) {
        message.error('请选择年级');
        return;
      }
      const { id, ...info } = values;
      if (id) {
        response = await update(id, { ...info, grade: currentGrade.code });
      } else {
        response = await create({ ...values, grade: currentGrade.code });
      }

      if (response.errCode) {
        message.error(response.msg);
      } else {
        message.success('操作成功');
        actionRef?.current?.reload();
        setModalVisible(false);
      }
    },
    [currentGrade],
  );

  const handleDel = async (record: API.TextbookChecklist) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.TextbookChecklist, 'text'>[] = [
    {
      title: 'ID',
      key: 'id',
      dataIndex: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '教材',
      key: 'textbook',
      dataIndex: 'textbook',
      render: (_, record) => {
        return record.textbook?.textbook_version || '';
      },
    },
    {
      title: '册次',
      key: 'volume',
      dataIndex: 'volume',
    },
    {
      title: '主编',
      key: 'editor',
      dataIndex: 'editor',
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (currentSubject) {
      const section = dictionarie.list.find(
        (item) => item.code === currentSubject.grade_section,
      )?.name;
      setTitle(`${section} / ${currentSubject.subject}`);
      let gradeKeys: string[] =
        SECTION_GRADE[
          (currentSubject.grade_section as keyof typeof SECTION_GRADE) || ''
        ] || [];
      setGradeTreeData(
        dictionarie.list
          .filter(
            (item) => item.type === 'grade' && gradeKeys.includes(item.code),
          )
          .map((item) => ({
            title: item.name,
            key: item.code,
            ...item,
          })),
      );
    } else {
      setTitle('查询条件');
    }
  }, [currentSubject]);

  return (
    <>
      <CustomCollapse
        items={[
          {
            key: '1',
            label: title,
            children: (
              <CommonSelect
                level="subject"
                onChange={({ subject }) => {
                  setCurrentSubject(subject);
                }}
              />
            ),
          },
        ]}
        defaultActiveKey={['1']}
        style={{ marginBottom: 16 }}
      />
      <ProCard split="vertical">
        <ProCard title="年级" colSpan={{ sm: 8, lg: 6, xl: 4 }}>
          <Tree
            treeData={gradeTreeData}
            onSelect={(_selectedKeys, info) => {
              if (info.selected) {
                setCurrentGrade(info.node as any);
              } else {
                setCurrentGrade(undefined);
              }
            }}
          />
        </ProCard>
        <ProCard
          title={`${title || ''}${
            currentGrade ? ' / ' + currentGrade.name : ''
          }`}
          headerBordered
          extra={
            <Button
              type="primary"
              onClick={() => {
                setCurrent(undefined);
                setModalVisible(true);
              }}
            >
              新增
            </Button>
          }
        >
          <div style={{ minHeight: 360 }}>
            <ProTable<
              API.TextbookChecklist,
              { subject_id?: number; grade?: string }
            >
              actionRef={actionRef}
              options={false}
              search={false}
              columns={columns}
              params={{
                subject_id: currentSubject?.id,
                grade: currentGrade?.code,
              }}
              request={async (params) => {
                const { subject_id, grade } = params;
                if (!subject_id || !grade) {
                  return [];
                }
                const { errCode, msg, data } = await index_textbooks({
                  subject_id,
                });
                if (errCode) {
                  message.error(msg || '列表查询失败');
                  return [];
                }
                setCurrentTextbookList(data.list || []);
                if (!data.list?.length) {
                  return [];
                }
                const {
                  errCode: err1,
                  data: data1,
                  msg: msg1,
                } = await index_checklist({
                  textbook_id: data.list.map((item) => item.id).join(','),
                  grade,
                });
                if (err1) {
                  message.error(msg1 || '列表查询失败');
                  return [];
                }
                return {
                  data: data1.list || [],
                  success: true,
                  total: data1.total || 0,
                };
              }}
            />
          </div>
        </ProCard>
      </ProCard>
      <EditModal
        open={modalVisible}
        info={current}
        textbookList={currentTextbookList}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(
  TextbookChecklist,
);
