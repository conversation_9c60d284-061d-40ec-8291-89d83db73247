.customPreviewDrawer {
  background: #f1f1f1;

  .container {
    p {
      margin: 0;
      padding: 0;
    }

    img {
      max-width: 100%;
    }

    .optionBar {
      padding: 16px;
      display: flex;
      justify-content: space-between;
    }

    :global {
      .paperTitle {
        margin-bottom: 16px;

        h1 {
          text-align: center;
          margin-bottom: 0;
        }

        .paperTip {
          color: #666;
        }
      }

      u {
        text-decoration: none;
        border-bottom: 1px solid #000;
      }

      .padding-8 {
        padding: 2px;
        word-break: break-word;
      }

      .question-name {
        display: flex;
        align-items: center;

        > span {
          display: inline-block;
          word-wrap: normal;
          word-break: keep-all;
        }

        .name-content {
          font-weight: bold;
          font-size: 18px;
          padding-left: 2px;
        }
      }

      .option-row {
        width: 100%;
        display: box;
        display: flexbox;
        display: flex;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
        flex-flow: row wrap;
        position: relative;

        &:hover {
          border: 1px solid #dadada;

          .set-option {
            visibility: visible;
            opacity: 1;
          }
        }

        .question-option {
          padding-left: 12px;
          position: relative;

          &.ant-col-24 {
            flex: 0 0 100%;
            max-width: 100%;
          }

          &.ant-col-12 {
            flex: 0 0 50%;
            max-width: 50%;
          }

          &.ant-col-6 {
            flex: 0 0 25%;
            max-width: 25%;
          }

          .option {
            display: inline-block;
            width: 26px;
            text-align: center;
            margin-right: 6px;
            // vertical-align: top;
            position: absolute;
            top: 2px;
          }

          .option-content {
            width: calc(100% - 32px);
            display: inline-block;
            margin-left: 32px;
          }
        }

        .set-option {
          position: absolute;
          top: -31px;
          right: -1px;
          font-size: 12px;
          z-index: 20;
          cursor: pointer;
          visibility: hidden;
          opacity: 0;
          width: max-content;
          display: inline-block;
          padding-left: 8px;
          background: #fff;
          box-shadow: -2px 0 6px 0 rgba(73, 83, 98, 20%);
        }
      }

      .card-content {
        margin: 0 auto 48px;
        color: #000;
        background-color: #fff;
        position: relative;
        // padding: 66px 47px;
        // width: 992px;
        // height: 1403px;
      }

      .questionWrap {
        display: flex;
        // align-items: baseline;

        .questionNum {
          width: 20px;
          padding-top: 2px;
        }

        > div {
          flex: 1;
        }
      }

      .mce-content-body {
        padding: 2px;
        word-break: break-word;
        margin: 0 !important;

        &:focus,
        &:focus-visible {
          border: 1px solid #dadada;
          outline: none;
        }

        .optionNum {
          display: inline-block;
        }

        .questionNum {
          display: inline-block;
        }

        .tinymce-horizontal-line {
          line-break: anywhere;
          font-size: 16px;
          border-bottom: 1px solid #000;
        }
      }

      .preload {
        position: absolute;
        left: 0;
        top: 0;
        z-index: -10;
        font-size: 16px;
        width: 898px;
      }
    }
  }
}
