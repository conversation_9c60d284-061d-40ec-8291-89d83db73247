import { getQuestionWorkMonth } from '@/services/statistics';
import { useModel } from '@umijs/max';
import { Card, Col, message } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import DualAxesChart from '../BaseChart/DualAxes';
import StatisticsContext from '../Context';

interface TrendHomeworkQuantityProps {
  span: number;
  type: 'city' | 'county' | 'school';
  title: string;
}

const TrendHomeworkQuantity: React.FC<TrendHomeworkQuantityProps> = ({
  span,
  type,
  title,
}) => {
  const { initialState } = useModel('@@initialState');
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const { info } = useContext(StatisticsContext);
  const { yearTerm, county, schoolType } = info || {};

  const buildParams = () => {
    const baseParams = {
      semester_code: yearTerm,
      grade_section_code: schoolType,
    };

    switch (type) {
      case 'city':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county,
        };
      case 'county':
        return {
          ...baseParams,
          city_code: initialState?.enterprise?.city,
          area_code: county || initialState?.enterprise?.area,
        };
      case 'school':
        return {
          ...baseParams,
          enterprise_code: initialState?.enterprise?.code,
        };
      default:
        return baseParams;
    }
  };

  const getInitData = async () => {
    const params = buildParams();
    setLoading(true);
    if (!yearTerm) return;
    const res = await getQuestionWorkMonth(params);
    if (res?.errCode) {
      setLoading(false);
      message.warning(`获取数据失败，请稍后重试 ${res?.msg}`);
      return;
    }

    const newData = res.data?.map((item: any) => {
      return {
        month: item?.month,
        数量: item?.total_homework_number,
        环比: item?.homework_growth_rate,
      };
    });
    setLoading(false);
    setData(newData || []);
  };

  useEffect(() => {
    if (type) getInitData();
  }, [type, yearTerm, county, schoolType]);

  return (
    <Col span={span}>
      <Card title={title}>
        <DualAxesChart data={data} loading={loading} />
      </Card>
    </Col>
  );
};
export default TrendHomeworkQuantity;
