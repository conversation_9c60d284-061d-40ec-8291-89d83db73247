import CommonSelect from '@/components/CommonSelect';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: any;
  onSave: (info: any) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (info && open) {
      form.setFieldsValue(info);
    } else {
      form.resetFields();
    }
  }, [info, open]);

  return (
    <ModalForm<any>
      form={form}
      title={info ? '编辑考察能力' : '新增考察能力'}
      autoFocusFirstInput
      modalProps={{
        width: 500,
        destroyOnClose: true,
        onCancel: onClose,
        styles: {
          body: {
            marginTop: '20px',
          },
        },
      }}
      open={open}
      layout="horizontal"
      grid
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText name="subjectName" hidden />
      <ProFormText name="subjectId" hidden />
      <ProFormText name="gradeSectionName" hidden />
      <ProFormText name="gradeSectionCode" hidden />
      <CommonSelect
        level="subject"
        sectionCode={info?.gradeSectionCode}
        subjectId={info?.subjectId}
        onChange={(values) => {
          form.setFieldsValue({
            subjectId: values.subject?.id,
            subjectName: values.subject?.subject,
            gradeSectionCode: values.section?.code,
            gradeSectionName: values.section?.name,
          });
        }}
        rowStyles={{
          marginLeft: 10,
        }}
      />
      <ProFormText
        name="name"
        label="名称"
        fieldProps={{ maxLength: 50 }}
        rules={[{ required: true, message: '请输入名称！' }]}
      />
    </ModalForm>
  );
};

export default EditModal;
