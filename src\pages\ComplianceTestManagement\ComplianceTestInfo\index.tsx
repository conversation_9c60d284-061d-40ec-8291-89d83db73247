import selectImg from '@/assets/noData.png';
import templateIcon from '@/assets/templateIcon.png';
import { useCurrentAccess } from '@/common/useCurrentAccess';
import CommonCard from '@/components/CommonCard';
import CommonDesc from '@/components/CommonDesc';
import ConditionalRender from '@/components/ConditionalRender';
import { MONGO_MODEL_KEY } from '@/pages/QuestionManagement/Entry';
import ModalUpload from '@/pages/QuestionManagement/Entry/ModalUpload';
import {
  templateComplicanceHomeworkCreate,
  templateComplicanceHomeworkIndex,
  templateComplicanceHomeworkRemove,
  templateComplicanceHomeworkUpdate,
} from '@/services/compliance_detection_template_detail';
import { getSSOToken } from '@/utils/auth';
import { PlusOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { Link, useLocation, useParams } from '@umijs/max';
import {
  Breadcrumb,
  Button,
  Col,
  Pagination,
  Popconfirm,
  Row,
  Space,
  Spin,
  Tag,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import AddTemplateModal from '../components/AddTemplateModal';
import styles from './index.less';
type TemplateModalType = {
  width?: number;
  open: boolean;
  title: string | undefined;
  data: any;
};
const HomeworkTemplateInfo: React.FC = () => {
  const { id } = useParams();
  const { isSchoolAdmin, isSysAdmin } = useCurrentAccess();
  const { state: info, pathname } = useLocation() as {
    state: API.TemplateWork;
    pathname: string;
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [homework, setHomework] = useState<any>([]);
  const [ksInfo, setKsInfo] = useState<any>();
  const [baseModal, setBaseModal] = useState<TemplateModalType>();
  const [uploadModalOpen, setUploadModalOpen] = useState<boolean>(false);
  const isHidden =
    (info?.type === '内置' && !isSysAdmin) ||
    (info?.type === '自定义' && !isSchoolAdmin);
  /** 获取数据 */
  const getTemplateInfo = async () => {
    setLoading(true);
    const { errCode, data, msg } = await templateComplicanceHomeworkIndex({
      template_id: id,
      status: isHidden ? '发布' : undefined,
    });
    setLoading(false);
    if (errCode) {
      message.warning('数据获取失败,请稍后重试' + msg);
    }
    setHomework(data?.list ?? []);
  };
  /** 试题导入 */
  const handleUploadQuestion = () => {
    setUploadModalOpen(true);
  };
  const addTemplate = () => {
    setBaseModal({
      width: 800,
      open: true,
      title: '新增达标检测',
      data: undefined,
    });
  };
  const handleDelete = async (id?: string) => {
    const { errCode, msg } = await templateComplicanceHomeworkRemove(id || '');
    if (errCode) {
      message.warning('删除失败,请稍后重试！' + msg);
      return;
    }
    message.success('删除成功');
    getTemplateInfo();
  };

  const updateHomework = async (id?: string, status?: string) => {
    const { errCode, msg } = await templateComplicanceHomeworkUpdate(id || '', {
      status: status || '草稿',
    });
    if (errCode) {
      message.warning('操作失败,请稍后重试！' + msg);
      return;
    }
    message.success('操作成功');
    getTemplateInfo();
  };
  const handleSubmit = async (values: any) => {
    if (values?.id) {
      const { id, ...rest } = values;
      const { errCode, msg } = await templateComplicanceHomeworkUpdate(id, {
        ...rest,
      });
      if (errCode) {
        message.warning('修改失败,请稍后重试！' + msg);
        return;
      }
      message.success('修改成功');
    } else {
      const { errCode, msg } = await templateComplicanceHomeworkCreate(values);
      if (errCode) {
        message.warning('创建失败,请稍后重试！' + msg);
        return;
      }
      message.success('创建成功');
    }
    setBaseModal({
      open: false,
      title: undefined,
      data: undefined,
    });
    getTemplateInfo();
  };
  useEffect(() => {
    if (id) {
      getTemplateInfo();
    }
  }, [id]);

  useEffect(() => {
    const handleFocus = () => {
      if (id) {
        // 添加页面可见性检查
        if (document.visibilityState === 'visible') {
          getTemplateInfo();
        }
      }
    };

    // 同时监听 visibilitychange 和 focus 事件
    document.addEventListener('visibilitychange', handleFocus);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleFocus);
      window.removeEventListener('focus', handleFocus);
    };
  }, [id]); // 添加依赖项
  return (
    <>
      <div className={styles.homeworkTemplateInfo}>
        <Breadcrumb
          items={[
            {
              title: (
                <a
                  onClick={() => {
                    history.back();
                  }}
                >
                  达标检测范本管理
                </a>
              ),
            },
            {
              title: '达标检测范本列表',
            },
          ]}
        />
        <Spin spinning={loading} tip="数据加载中...">
          <div className={styles.container}>
            <Row gutter={16}>
              <Col span={24}>
                <CommonCard
                  height={45}
                  background="#2979ff"
                  color="#fff"
                  style={{
                    marginBottom: 0,
                    borderRadius: '8px 8px 0 0',
                  }}
                  title={
                    <Space size="small">
                      <UnorderedListOutlined />
                      <span>
                        {info?.grade_section_name}
                        {info?.grade_name}
                      </span>
                      {info?.subject_name} · {info?.textbook_version} （
                      {info?.volume}）
                    </Space>
                  }
                >
                  {info?.status === '草稿' && (
                    <Button
                      color="default"
                      variant="outlined"
                      icon={<PlusOutlined />}
                      onClick={addTemplate}
                    >
                      新增达标检测
                    </Button>
                  )}
                </CommonCard>
                <div
                  className="commonWapper"
                  style={{
                    height: 'calc(100vh - 187px)',
                    overflowY: 'auto',
                  }}
                >
                  <ConditionalRender
                    hasAccess={homework?.length}
                    accessComponent={
                      <div className={styles.cardList}>
                        {homework?.map((item: any) => {
                          return (
                            <CommonCard
                              key={item.id}
                              height={65}
                              title={
                                <Space>
                                  <img
                                    src={templateIcon}
                                    style={{
                                      width: '30px',
                                    }}
                                  />
                                  {item.name}
                                  <Tag>{item.detection_type}</Tag>
                                  <Tag
                                    style={{
                                      marginLeft: 16,
                                    }}
                                    color={
                                      item?.status === '草稿' ? 'blue' : 'green'
                                    }
                                  >
                                    {item?.status}
                                  </Tag>
                                </Space>
                              }
                              className={styles.header}
                            >
                              <Link
                                to={`${pathname}/detail`}
                                state={item}
                                type="text"
                              >
                                <Button type="text">查看</Button>
                              </Link>
                              <ConditionalRender
                                hasAccess={item?.status === '草稿'}
                                accessComponent={
                                  <>
                                    <Button
                                      color="purple"
                                      variant="link"
                                      onClick={() => {
                                        setKsInfo({
                                          outterId: item?.template_id,
                                          innerId: item?.id,
                                          name: item?.name,
                                          id: item?.catalogIds?.[0],
                                          title: item?.catalogs?.[0],
                                          detection_type: item.detection_type,
                                        });
                                        handleUploadQuestion();
                                      }}
                                    >
                                      导入文档
                                    </Button>

                                    <Link
                                      to={
                                        `${pathname}/edit?ddtab=true&homeworkId=${
                                          item.id
                                        }&subjectId=${
                                          info?.subject_id
                                        }&volumeId=${
                                          info?.textbookChecklist_id
                                        }&catalogIds=${item.catalogIds.toString()}&isSysAdmin=${isSysAdmin}&token=${getSSOToken()}` +
                                        (item?.questions?.length
                                          ? ''
                                          : '&type=add')
                                      }
                                      target="_blank"
                                      type="text"
                                    >
                                      <Button type="link">编辑</Button>
                                    </Link>
                                    <Popconfirm
                                      key="publish"
                                      title="发布后当前版本不可再做修改，确认发布吗?"
                                      onConfirm={() =>
                                        updateHomework(item?.id, '发布')
                                      }
                                      okText="确定"
                                      cancelText="取消"
                                      placement="topRight"
                                    >
                                      <Button color="cyan" variant="link">
                                        发布
                                      </Button>
                                    </Popconfirm>
                                    <Popconfirm
                                      title="您确认要删除当前达标检测吗？"
                                      description="删除后将无法恢复，请谨慎操作"
                                      onConfirm={() => {
                                        handleDelete(item.id);
                                      }}
                                    >
                                      <Button type="link" size="small" danger>
                                        删除
                                      </Button>
                                    </Popconfirm>
                                  </>
                                }
                                noAccessComponent={
                                  !isHidden && (
                                    <Popconfirm
                                      key="withdraw"
                                      title="确认撤销当前作业范本的发布状态吗?"
                                      onConfirm={() =>
                                        updateHomework(item?.id, '草稿')
                                      }
                                      okText="确定"
                                      cancelText="取消"
                                      placement="topRight"
                                    >
                                      <Button color="cyan" variant="link">
                                        撤销
                                      </Button>
                                    </Popconfirm>
                                  )
                                }
                              />
                            </CommonCard>
                          );
                        })}
                        <Pagination
                          align="center"
                          pageSize={10}
                          total={homework?.length}
                        />
                      </div>
                    }
                    noAccessComponent={
                      <CommonDesc
                        style={{
                          margin: '0 auto',
                        }}
                        icon={selectImg}
                        title="暂无数据"
                        desc={
                          info?.status === '草稿' ? '请先创建达标检测范本' : ''
                        }
                        extra={
                          info?.status === '草稿' && (
                            <Button type="primary" onClick={addTemplate}>
                              新增达标检测
                            </Button>
                          )
                        }
                      />
                    }
                  />
                </div>
              </Col>
            </Row>
          </div>
        </Spin>
      </div>
      <AddTemplateModal
        width={baseModal?.width ?? 450}
        open={baseModal?.open}
        title={baseModal?.title}
        initialValues={baseModal?.data}
        onFinish={handleSubmit}
        layout="horizontal"
        modalProps={{
          destroyOnClose: true,
          centered: true,
          onCancel: () => {
            setBaseModal({
              open: false,
              title: undefined,
              data: undefined,
            });
          },
          styles: {
            body: {
              marginTop: 20,
            },
          },
        }}
      />
      <ModalUpload
        info={{
          fbname: ksInfo?.name,
          innerId: ksInfo?.innerId,
          section: {
            name: info?.grade_section_name,
            code: info?.grade_section_code,
          },
          grade: {
            name: info?.grade_name,
            code: info?.grade_code,
          },
          subject: {
            subject: info?.subject_name,
            id: info?.subject_id,
          },
          textbook: {
            textbook_version: info?.textbook_version,
            id: info?.textbook_id,
          },
          volume: {
            volume: info?.volume,
            id: info?.textbookChecklist_id,
          },
          catalog: ksInfo?.detection_type === '单元' && {
            title: ksInfo?.title,
            id: ksInfo?.id,
          },
        }}
        type="达标检测范本"
        open={uploadModalOpen}
        onClose={() => {
          setUploadModalOpen(false);
        }}
        onSuccess={async () => {
          setUploadModalOpen(false);
          getTemplateInfo();
        }}
        tableName={
          isSysAdmin
            ? MONGO_MODEL_KEY.SYSTEM
            : isSchoolAdmin
            ? MONGO_MODEL_KEY.SCHOOL
            : undefined
        }
      />
    </>
  );
};
export default HomeworkTemplateInfo;
