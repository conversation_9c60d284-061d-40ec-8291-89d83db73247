import { AreaState } from '@/models/area';
import { RightOutlined } from '@ant-design/icons';
import { connect } from '@umijs/max';
import { Checkbox, Col, ColProps, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

/** 单区域列表 */
const RenderRow: React.FC<{
  /** 区域标题 */
  label: string;
  /** 下级区域列表 */
  list: API.Area[];
  /** 选中的区域列表 */
  value: string[];
  /** 操作选择回调 */
  onChange: (values: API.Area[]) => void;
  /** 标题列属性 */
  labelCol?: ColProps;
  /** 自定义行样式 */
  rowStyles?: React.CSSProperties;
}> = ({ label, list, value, onChange, labelCol, rowStyles }) => {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <Row className={styles.row} style={rowStyles}>
      <Col className={styles.label} {...labelCol}>
        <label>{label}</label>
      </Col>
      <Col>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 16,
            marginBottom: 8,
          }}
        >
          <Checkbox
            title="全选"
            onChange={(e) => {
              if (e.target.checked) {
                onChange?.(list);
              } else {
                onChange?.([]);
              }
            }}
          >
            全选
          </Checkbox>
          <a onClick={() => setOpen(!open)}>
            折叠全部
            <RightOutlined
              style={{
                marginLeft: '2px',
                // transition: 'all 0.3s',
                transform: open ? 'rotate(90deg)' : '',
              }}
            />
          </a>
        </div>
        <div style={{ height: open ? 'auto' : 0, overflow: 'hidden' }}>
          <Checkbox.Group
            options={list.map((d) => ({
              label: d.name,
              value: d.code,
              title: d.name,
            }))}
            onChange={(checkedValue) => {
              onChange?.(
                checkedValue
                  .map((c) => list.find((l) => l.code === c))
                  .filter((v) => !!v),
              );
            }}
            value={(value || []).filter((v) => list.find((l) => l.code === v))}
          />
        </div>
      </Col>
    </Row>
  );
};

/** 区域选择器 */
export type AreaSelectProps = {
  /** 选中的区域列表，只需要最下一级区域 */
  value: string[];
  /** 操作选择回调 */
  onChange: (values: string[]) => void;
  /** 标题列属性 */
  labelCol?: ColProps;
  /** 自定义行样式 */
  rowStyles?: React.CSSProperties;
  area: AreaState;
};
const Area: React.FC<AreaSelectProps> = ({
  value,
  onChange,
  labelCol,
  rowStyles,
  area,
}) => {
  const [valueObj, setValueObj] = useState<Record<string, API.Area[]>>();
  const [shis, setShis] = useState<API.Area[]>([]);

  useEffect(() => {
    // 根据value更新市
    const list = Array.from(
      new Set(
        shis
          .map((s) => s.code)
          .concat((value || []).map((v) => v.slice(0, -2) + '00')),
      ),
    );
    setShis(
      list.map((v) => area.list.find((l) => l.code === v)).filter((v) => !!v),
    );
  }, [value]);

  useEffect(() => {
    if (valueObj) {
      const list = (value || []).concat(
        Object.values(valueObj || {})
          .flat()
          .filter((v) => v)
          .map((v) => v.code),
      );
      onChange?.(Array.from(new Set(list)));
    }
  }, [valueObj]);

  return (
    <>
      <RenderRow
        label="地区"
        list={area.treeData.find((t) => t.code === '610000')?.children || []}
        value={shis.map((s) => s.code)}
        onChange={(arr) => {
          setShis(arr);
          // 清除不在市中的value
          const newValueObj = valueObj ? { ...valueObj } : {};
          Object.keys(newValueObj).forEach((key) => {
            if (!arr.map((a) => a.code).includes(key)) {
              delete newValueObj[key];
            }
          });
          setValueObj(newValueObj);
        }}
        labelCol={labelCol}
        rowStyles={rowStyles}
      />
      {shis.map((s) => (
        <RenderRow
          key={s.code}
          label={s.name}
          list={area.list.filter((l) => l.parentCode === s.code) || []}
          value={value}
          onChange={(v) => {
            const newValueObj = valueObj ? { ...valueObj } : {};
            newValueObj[s.code] = v;
            setValueObj(newValueObj);
          }}
          labelCol={labelCol}
          rowStyles={rowStyles}
        />
      ))}
    </>
  );
};

export default connect(({ area }) => ({ area }))(Area);
