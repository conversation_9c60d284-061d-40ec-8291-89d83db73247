import '@umijs/max/typings';

declare module 'docx';
declare module 'html-docx-js';

type PartialWithRequired<T, K extends keyof T> = Partial<T> & Pick<T, K>;

declare global {
  /** 综合基础路由与 umi 扩展路由 */
  type IBestAFSRoute = {
    path?: string;
    component?: string;
    name?: string;
    layout?: false;
    redirect?: string;
    keepQuery?: boolean;
    routes?: IBestAFSRoute[];
    wrappers?: Array<string>;
    /** 菜单图标 */
    icon?: string;
    /** 新页面打开 */
    target?: '_blank';
    /** 不展示顶栏 */
    headerRender?: boolean;
    /** 不展示页脚 */
    footerRender?: boolean;
    /** 不展示菜单 */
    menuRender?: boolean;
    /** 不展示菜单顶栏 */
    menuHeaderRender?: boolean;
    /** 权限配置，需要与 plugin-access 插件配合使用 */
    access?: string;
    /** 隐藏子菜单 */
    hideChildrenInMenu?: boolean;
    /** 隐藏自己和子菜单 */
    hideInMenu?: boolean;
    /** 在面包屑中隐藏 */
    hideInBreadcrumb?: boolean;
    /** 子项往上提，仍旧展示 */
    flatMenu?: boolean;
  };

  /** yeald语法参数定义 */
  type Call = <T>(
    fn: (...args: any[]) => Promise<T>,
    ...args: any[]
  ) => Promise<T>;

  /** yeald语法参数定义 */
  type Put = <A extends { type: string }>(action: A) => { type: string };

  declare const SSO: {
    AppID: string;
    AppSecret: string;
    Host: string;
    state: string;
  };

  declare const COS_CONFIG: {
    /** 存储桶名称 */
    Bucket: string;
    /** 存储桶所在地域 */
    Region: string;
    /** 本项目统一存储路径前缀 */
    Prefix: string;
  };

  /** storage前缀，与其他系统集成时防止重名 */
  declare const STORAGE_PREFIX: string;

  /** 是否处于独立题库模式 */
  declare const INDEPENDENT_QUESTION_BANK: boolean;

  /** 认证模式配置 */
  declare const AUTH_MODE: 'sso' | 'local';

  /** 部署文件夹 */
  declare const BASE_URL: string;
}
