import QuestionForm, { QuestionFormRef } from '@/components/QuestionForm';
import { QuestionContext } from '@/context/QuestionContext';
import {
  createAssociatetCommonQuestion,
  createCommonQuestion,
  updateAssociatetCommonQuestion,
  updatetCommonQuestion,
} from '@/services/common_question';
import { getQueryParams } from '@/utils/calc';
import { useLocation, useModel } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, useEffect, useRef, useState } from 'react';
import Analysis from './Analysis';
import QuestionGrouped from './QuestionGrouped';
import Similar from './Similar';
import styles from './index.less';

const PaperQuestionList = ({
  data,
  showDuration = true,
  settingScore = false,
  sortQuestion,
  actionType = false,
  replaceQuestion,
  reolad,
}: {
  data: any;
  sortQuestion: Dispatch<any>;
  showDuration?: boolean;
  settingScore?: boolean;
  replaceQuestion?: React.Dispatch<React.SetStateAction<API.SystemQuestion[]>>;
  actionType?: boolean;
  reolad?: () => void;
}) => {
  const { initialState } = useModel('@@initialState');
  const { pathname } = useLocation() as { pathname: string };
  const workDesign = pathname.includes('Design');
  const { isSysAdmin } = getQueryParams();
  const childRef = useRef<QuestionFormRef>(null);
  const [isReadonly, setIsReadonly] = useState(false);
  const { subjectId } = getQueryParams();
  const [open, setOpen] = useState<{
    show: boolean;
    type: string;
    data: any;
  }>({
    show: false,
    type: '',
    data: undefined,
  });
  const hideModal = () => {
    setOpen({
      show: false,
      type: '',
      data: undefined,
    });
  };
  useEffect(() => {
    if (open.type === 'edit') {
      const initVals = open.data;
      childRef.current?.show?.();
      if (initVals?.isCompose) {
        initVals.questions = initVals?.children.map(
          (item: { type: { code: string } }) => {
            return {
              ...item,
              type_code: item?.type?.code,
            };
          },
        );
      }
      const backfillData = {
        ...initVals,
        type_code: initVals?.type?.code,
        answer:
          initVals?.baseType?.name === '多选题'
            ? JSON.parse(initVals?.answer || '[]')
            : initVals?.answer,
        difficulty: initVals?.difficulty?.code,
        tier: initVals?.tier?.code,
        cognitiveHierarchy: initVals?.cognitiveHierarchy?.id,
        coreQuality: initVals?.coreQuality?.id,
        investigationAbility: initVals?.investigationAbility?.id,
        classification: initVals?.classification?.id,
      };
      setIsReadonly(true);
      childRef?.current?.initData?.(backfillData as any);
    } else if (open.type === 'add') {
      const initVals = open.data;
      childRef.current?.show?.();
      childRef?.current?.initData?.({
        ...initVals,
        difficulty: 'EASY',
        isCompose: false,
        year: dayjs(),
        tableName:
          isSysAdmin === 'true' ? 'system' : workDesign ? 'personal' : 'school',
      } as any);
    }
  }, [open]);
  /** 提交试题 */
  const submitQuestion = async (data: Partial<API.SystemQuestion>) => {
    const newData = { ...data };
    let newQuz = { ...data };
    childRef?.current?.loading(true, '试题保存中，请耐心等候...');
    try {
      let result;
      const { _id: id, tableName: type, ...rest } = data;
      if (newData?._id) {
        if (newData.isCompose) {
          result = await updateAssociatetCommonQuestion(id, type!, {
            ...rest,
          } as any);
        } else {
          result = await updatetCommonQuestion(id, type!, {
            ...rest,
          } as any);
        }
      } else {
        const tableName =
          isSysAdmin === 'true' ? 'system' : workDesign ? 'personal' : 'school';
        if (newData.isCompose) {
          result = await createAssociatetCommonQuestion(tableName, {
            ...data,
            author: {
              id: initialState?.id,
              name: initialState?.nickname,
            },
            enterpriseCode: initialState?.enterprise?.code,
          } as any);
        } else {
          result = await createCommonQuestion(tableName, {
            ...data,
            author: {
              id: initialState?.id,
              name: initialState?.nickname,
            },
            enterpriseCode: initialState?.enterprise?.code,
          } as any);
        }
        newQuz = result?.data;
      }
      if (result?.errCode) {
        childRef?.current?.loading(false);
        return message.warning(`试题保存失败，请稍后重试 ${result?.msg}`);
      }
      message.success('试题保存成功，已同步更新到题库');
      replaceQuestion?.((preData: any) => {
        // 去重处理
        const uniqueData = [
          ...new Map(
            [...preData, newQuz].map((item) => [item._id, item]),
          ).values(),
        ];
        return uniqueData;
      });
      if (open.type === 'add') {
        sortQuestion?.((prevData: any) => {
          const pId = prevData?.bigQuestionOrder?.[0];
          const current = prevData?.bigQuestions?.[pId];
          const newQuestionIds: any = Array.from(current.questionIds);
          newQuestionIds.push(newQuz._id);
          const newBigQuestion = {
            ...current,
            questionIds: newQuestionIds,
          };
          return {
            ...prevData,
            bigQuestions: {
              ...prevData.bigQuestions,
              [newBigQuestion.id]: newBigQuestion,
            },
          };
        });
      }
      childRef?.current?.loading(false);
      childRef?.current?.close();
    } catch (error) {
      childRef?.current?.loading?.(false);
      return message.warning(`试题改编失败，请稍后重试 ${error ?? ''}`);
    }
  };
  return (
    <QuestionContext.Provider
      value={{
        readonly: isReadonly,
        subjectId: subjectId,
      }}
    >
      <div className={styles.questionArea}>
        <div className={styles.questionType}>
          {data?.map((item: any, index: number) => {
            if (item?.orderIndex === -1 && item?.children?.length === 0) {
              return null;
            }
            return (
              <QuestionGrouped
                key={item.id}
                showDuration={showDuration}
                settingScore={settingScore}
                item={{ ...item, index }}
                index={item.orderIndex}
                setOpen={setOpen}
                reolad={reolad}
                sortQuestion={sortQuestion}
                actionType={actionType}
              />
            );
          })}
        </div>
        {open.type === 'Analysis' && (
          <Analysis open={open.show} data={open.data} hideModal={hideModal} />
        )}
        {open.type === 'Similar' && (
          <Similar
            open={open.show}
            data={open.data}
            hideModal={hideModal}
            reolad={reolad}
            replaceQuestion={replaceQuestion}
            replaceStructure={sortQuestion}
          />
        )}
        {(open.type === 'edit' || open.type === 'add') && (
          <QuestionForm
            subjectId={subjectId}
            handleQuestionSubmit={submitQuestion}
            ref={childRef}
            refresh={(): any => {
              setIsReadonly(false);
            }}
          />
        )}
      </div>
    </QuestionContext.Provider>
  );
};

export default PaperQuestionList;
