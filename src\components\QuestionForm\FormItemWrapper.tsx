import { QuestionContext } from '@/context/QuestionContext';
import { getKnowledgePointDetailByCatalogId } from '@/services/knowledge_point';
import { convertToTree } from '@/utils/calc';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormRadio,
  ProFormSegmented,
  ProFormSelect,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { Button, FormListOperation, message, Popconfirm, Tag } from 'antd';
import { useContext, useState } from 'react';
import ConditionalRender from '../ConditionalRender';
import TinyMCEEditor from '../TinyMCEEditor';
import { CommonItem, JudgeItem, SingleChoiceItem } from './FormItems';
import styles from './index.less';
import { refillText } from './util';

interface FormItemWrapperProps {
  form: any;
  initValues?: any;
  isCompose?: boolean;
  index?: number;
  type?: any[];
  degree?: any[];
  topicSlice?: any[];
  cognitive?: any[];
  quality?: any[];
  assessment?: any[];
  catalogId?: number;
  isReadonly?: boolean;
  isSystematicCompose?: boolean;
  classify?: any[];
  mode?: 'answerAnalysis';
}

const FormItemWrapper = ({
  form,
  initValues,
  isCompose = false,
  index,
  catalogId,
  type = [],
  degree = [],
  topicSlice = [],
  cognitive = [],
  quality = [],
  assessment = [],
  classify = [],
  isReadonly,
  mode,
}: FormItemWrapperProps) => {
  const { readonly } = useContext(QuestionContext);
  const [loading, setLoading] = useState(false);
  const [isReadonlyMode] = useState(mode === 'answerAnalysis');

  /** 当前小题是否填写内容 */
  const isFilled = (
    action: FormListOperation & {
      getCurrentRowData: () => any;
      setCurrentRowData: (data: any) => void;
    },
  ) => {
    return Object.keys(action.getCurrentRowData()).length > 0;
  };

  return (
    <>
      <ProFormDependency name={['type_code', 'isCompose']}>
        {({ type_code, isCompose }) => {
          const target = type.find((item) => item.code === type_code);
          // 这里的value就是当前层级的type_code
          const filteredOptions = target
            ? type.filter(
                (item) =>
                  item.sourceCode === target?.sourceCode &&
                  (isCompose ? item.isCompose === isCompose : true),
              )
            : type;
          return (
            <ProFormSelect
              showSearch
              allowClear={false}
              options={isReadonly || readonly ? filteredOptions : type}
              width="md"
              name={'type_code'}
              label={`题型`}
              readonly={!!mode ? isReadonlyMode : false}
              labelCol={{
                flex: '7em',
              }}
              colProps={{
                span: 8,
              }}
              rules={[
                {
                  required: true,
                  message: '请选择题型',
                },
              ]}
              fieldProps={{
                onChange: () => {
                  setLoading(true);
                  setTimeout(() => {
                    setLoading(false);
                  }, 500);
                },
              }}
            />
          );
        }}
      </ProFormDependency>
      <ConditionalRender
        hasAccess={!isCompose}
        accessComponent={
          <ProFormDigit
            name="duration"
            label="作答时长"
            readonly={isReadonlyMode}
            colProps={{
              span: 8,
            }}
            min={0}
            fieldProps={{ precision: 0, suffix: '分钟' }}
          />
        }
      />
      <ConditionalRender
        hasAccess={!isCompose}
        accessComponent={
          <>
            <ProFormSegmented
              name="difficulty"
              label="难度"
              readonly={isReadonlyMode}
              colProps={{
                span: 8,
              }}
              fieldProps={{
                options: degree,
              }}
              transform={(value: string) => ({
                difficulty: {
                  code: value,
                  name:
                    degree.find((item) => item.value === value)?.label || '',
                },
              })}
            />
            <ProFormDatePicker.Year
              colProps={{ md: 12, xl: 8 }}
              name="year"
              readonly={isReadonlyMode}
              label="所属年份"
              fieldProps={{
                style: {
                  width: '100%',
                },
              }}
            />
            <ProFormSelect
              colProps={{ md: 12, xl: 8 }}
              options={topicSlice}
              name="tier"
              disabled={isReadonlyMode}
              label="题目分层"
              transform={(value) => {
                const topicSliceLabel = topicSlice.find(
                  (item) => item.value === value,
                )?.title;
                return {
                  tier: {
                    name: topicSliceLabel,
                    id: value,
                  },
                };
              }}
            />
            <ProFormSelect
              colProps={{ md: 12, xl: 8 }}
              options={classify}
              name="classification"
              label="题目分类"
              disabled={isReadonlyMode}
              transform={(value) => {
                const classifyOptionsLabel = classify.find(
                  (item: { value: any }) => item.value === value,
                )?.title;
                return {
                  classification: {
                    name: classifyOptionsLabel,
                    id: value,
                  },
                };
              }}
            />
            <ProFormSelect
              colProps={{ md: 12, xl: 8 }}
              options={cognitive}
              name="cognitiveHierarchy"
              label="认知层次"
              disabled={isReadonlyMode}
              transform={(value) => {
                const cognitiveLabel = cognitive.find(
                  (item) => item.value === value,
                )?.title;
                return {
                  cognitiveHierarchy: {
                    name: cognitiveLabel,
                    id: value,
                  },
                };
              }}
            />
            <ProFormSelect
              colProps={{ md: 12, xl: 8 }}
              options={quality}
              name="coreQuality"
              label="核心素养"
              disabled={isReadonlyMode}
              fieldProps={{
                onChange: (e, option) => refillText(form, 'hxsy', e, option),
              }}
              transform={(value) => {
                const qualityLabel = quality.find(
                  (item) => item.value === value,
                )?.title;
                return {
                  coreQuality: {
                    name: qualityLabel,
                    id: value,
                  },
                };
              }}
            />
            <ProFormSelect
              colProps={{ md: 12, xl: 8 }}
              options={assessment}
              name="investigationAbility"
              label="考察能力"
              disabled={isReadonlyMode}
              fieldProps={{
                onChange: (e, option) => refillText(form, 'kcnl', e, option),
              }}
              transform={(value) => {
                const investigationAbilityLabel = assessment.find(
                  (item) => item.value === value,
                )?.title;
                return {
                  investigationAbility: {
                    name: investigationAbilityLabel,
                    id: value,
                  },
                };
              }}
            />
            <ConditionalRender
              hasAccess={INDEPENDENT_QUESTION_BANK}
              accessComponent={
                <ProFormText
                  name="source"
                  readonly={isReadonlyMode}
                  label="题源"
                  colProps={{ md: 12, xl: 8 }}
                />
              }
            />
          </>
        }
      />
      <ConditionalRender
        hasAccess={INDEPENDENT_QUESTION_BANK}
        accessComponent={
          <ProFormSelect
            name="tags"
            readonly={isReadonlyMode}
            mode="multiple"
            placeholder="请填写类题标签"
            colProps={{ md: 12, xl: 8 }}
            allowClear
            label="类题标签"
            secondary
            style={{ width: '100%' }}
            fieldProps={{
              maxCount: 3,
            }}
            onChange={(_, options) => {
              form.setFieldValue(
                'tags',
                options?.map((item: any) => ({
                  label: item.label.trim(),
                  value: item.value,
                })),
              );
            }}
          />
        }
      />
      <ProFormTreeSelect
        name="points"
        placeholder="请选择知识点"
        readonly={isReadonlyMode}
        allowClear
        label="知识点"
        secondary
        request={async () => {
          const { errCode, data, msg } =
            await getKnowledgePointDetailByCatalogId(catalogId);
          if (errCode) {
            message.warning(`获取知识点失败，请稍后重试 ${msg}`);
            return [];
          } else {
            return convertToTree(data?.list?.[0]?.knowledgePoints ?? []);
          }
        }}
        labelCol={{
          flex: '7em',
        }}
        colProps={{ md: 24, xl: INDEPENDENT_QUESTION_BANK ? 24 : 16 }}
        style={{ width: '100%' }}
        fieldProps={{
          maxCount: 10,
          filterTreeNode: true,
          showSearch: true,
          popupMatchSelectWidth: false,
          labelInValue: true,
          autoClearSearchValue: true,
          multiple: true,
          treeNodeFilterProp: 'title',
          fieldNames: {
            label: 'title',
          },
        }}
      />
      <ProFormSelect
        name="personalTags"
        label="标记"
        mode="tags"
        hidden
        placeholder="选择或输入标记"
        colProps={{ md: 12, xl: 12 }}
        options={[
          { value: '重点题', label: '重点题' },
          { value: '易错题', label: '易错题' },
          { value: '高频题', label: '高频题' },
        ]}
        fieldProps={{
          tagRender: (props) => (
            <Tag
              closable={props.closable}
              onClose={props.onClose}
              style={{ marginRight: 3 }}
            >
              {props.label}
            </Tag>
          ),
        }}
      />
      <ProFormDependency name={['type_code']}>
        {({ type_code }) => {
          const questionInfo = type?.find((v) => v.code === type_code);
          const { sourceName, isCompose: isSystematicCompose } =
            questionInfo || {};
          if (loading) {
            return (
              <div className={styles.loadingWrap}>
                <div className={styles.loading}></div>
                <span>加载中...</span>
              </div>
            );
          }
          if (isSystematicCompose && !isCompose) {
            return (
              <>
                <ProFormRadio.Group
                  readonly={isReadonlyMode || readonly}
                  name="isCompose"
                  label="组题"
                  options={[
                    {
                      label: '是',
                      value: true,
                    },
                    {
                      label: '否',
                      value: false,
                    },
                  ]}
                  rules={[
                    {
                      required: true,
                      message: '请选择组题',
                    },
                  ]}
                />
                <ProFormDependency name={['isCompose']}>
                  {({ isCompose }) => {
                    switch (isCompose) {
                      case true:
                        return (
                          <>
                            <ProForm.Item
                              label="题干"
                              name="name"
                              style={{ width: '100%' }}
                              rules={[
                                {
                                  required: true,
                                  message: '请输入题干',
                                  validator: (_, value, callback) => {
                                    if (!value) {
                                      callback('请输入题干');
                                    } else {
                                      callback();
                                    }
                                  },
                                },
                              ]}
                            >
                              <TinyMCEEditor
                                readonly={isReadonlyMode}
                                isForm={true}
                                editorChange={(val, text) => {
                                  form.setFieldValue('stem', text);
                                  form.setFieldValue('name', val);
                                }}
                              />
                            </ProForm.Item>
                            <ProFormGroup title="组题小题">
                              <ProFormList
                                name={'questions'}
                                itemContainerRender={(doms) => {
                                  return <ProForm.Group>{doms}</ProForm.Group>;
                                }}
                                creatorButtonProps={{
                                  position: 'bottom',
                                  creatorButtonText: '添加小题',
                                  type: 'primary',
                                  variant: 'dashed',
                                  ghost: true,
                                  disabled: isReadonlyMode,
                                  style: {
                                    width: '40%',
                                    position: 'absolute',
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                  },
                                }}
                                alwaysShowItemLabel
                                copyIconProps={false}
                                deleteIconProps={false}
                              >
                                {(f, index, action) => {
                                  return (
                                    <>
                                      <ProFormGroup
                                        title={
                                          <div
                                            style={{
                                              display: 'flex',
                                              alignItems: 'center',
                                              justifyContent: 'space-between',
                                              gap: '8px',
                                            }}
                                          >
                                            <div>【组题小题】{index + 1}</div>
                                            <ConditionalRender
                                              hasAccess={!isReadonlyMode}
                                              accessComponent={
                                                <ConditionalRender
                                                  hasAccess={isFilled(action)}
                                                  accessComponent={
                                                    <Popconfirm
                                                      key="clear"
                                                      title="确定删除此小题吗？"
                                                      description="删除后无法恢复，请谨慎操作！"
                                                      onConfirm={() => {
                                                        action.remove(index);
                                                      }}
                                                    >
                                                      <Button type="dashed">
                                                        删除此小题
                                                      </Button>
                                                    </Popconfirm>
                                                  }
                                                  noAccessComponent={
                                                    <Button
                                                      type="dashed"
                                                      onClick={() => {
                                                        action.remove(index);
                                                      }}
                                                    >
                                                      删除此小题
                                                    </Button>
                                                  }
                                                />
                                              }
                                            />
                                          </div>
                                        }
                                      >
                                        <FormItemWrapper
                                          form={form}
                                          isCompose={true}
                                          degree={degree}
                                          type={type}
                                          index={index}
                                          isReadonly={
                                            action.getCurrentRowData()?._id
                                              ? true
                                              : false
                                          }
                                          catalogId={catalogId}
                                          mode={mode}
                                        />
                                      </ProFormGroup>
                                    </>
                                  );
                                }}
                              </ProFormList>
                            </ProFormGroup>
                          </>
                        );
                      default:
                        return <CommonItem form={form} />;
                    }
                  }}
                </ProFormDependency>
              </>
            );
          }

          switch (sourceName) {
            case '单选题':
            case '多选题':
            case '选择题':
              return (
                <SingleChoiceItem
                  readonly={isReadonlyMode}
                  key={sourceName}
                  form={form}
                  type={sourceName}
                  isCompose={isCompose}
                  index={index}
                  initValues={initValues}
                />
              );
            case '判断题':
              return (
                <JudgeItem
                  readonly={isReadonlyMode}
                  key={sourceName}
                  form={form}
                  isCompose={isCompose}
                  index={index}
                />
              );
            default:
              return (
                <CommonItem
                  isReadonlyMode={isReadonlyMode}
                  form={form}
                  isCompose={isCompose}
                  index={index}
                />
              );
          }
        }}
      </ProFormDependency>
    </>
  );
};

export default FormItemWrapper;
