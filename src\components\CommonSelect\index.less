.row {
  width: 100%;
  margin-bottom: 10px;
  flex-wrap: nowrap;
  align-items: baseline;

  .label {
    text-align: right;

    > label {
      position: relative;
      display: inline-flex;
      align-items: center;
      max-width: 100%;
      min-width: 3em;
      height: 32px;
      color: rgba(0, 0, 0, 88%);
      font-size: 14px;

      &::after {
        content: ':';
        position: relative;
        margin-block: 0;
        margin-inline-start: 2px;
        margin-inline-end: 8px;
      }
    }
  }
}
