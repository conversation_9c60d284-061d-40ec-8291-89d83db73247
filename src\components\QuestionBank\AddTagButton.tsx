import { getUserTags } from '@/services/common_question';
import eventBus from '@/utils/eventBus';
import { PlusOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Select, Tag } from 'antd';
import React, { useEffect, useState } from 'react';

interface AddTagButtonProps {
  onAdd: (tag: string) => void;
}
// 在组件外部定义缓存变量
let tagDataCache: { value: string; label: string }[] = [];
let isFetching = false;
const AddTagButton: React.FC<AddTagButtonProps> = ({ onAdd }) => {
  const [isAdding, setIsAdding] = useState(false);
  const { initialState } = useModel('@@initialState');
  const [tagOptions, setTagOptions] =
    useState<{ value: string; label: string }[]>(tagDataCache);
  const getTagData = async () => {
    if (tagDataCache?.length) {
      setTagOptions(tagDataCache);
      return;
    }

    if (isFetching) return;
    isFetching = true;

    const { errCode, data } = await getUserTags({
      userId: initialState?.id,
    });

    isFetching = false;
    if (!errCode && data) {
      // 按照中文首字母排序
      tagDataCache = data
        .sort((a: string, b: string) => a.localeCompare(b, 'zh-Hans-CN'))
        .map((tag: string) => ({ value: tag, label: tag }));
      setTagOptions(tagDataCache || []);
    }
  };

  useEffect(() => {
    const unsubscribe = eventBus.subscribe(() => {
      tagDataCache = []; // 清空缓存
      getTagData();
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const handleClickAdd = async () => {
    getTagData();
    setIsAdding(true);
  };

  return (
    <>
      {!isAdding ? (
        <Tag
          style={{
            background: '#f5f5f5',
            borderStyle: 'dashed',
            cursor: 'pointer',
          }}
          icon={<PlusOutlined />}
          onClick={handleClickAdd} // 修改点击事件
        >
          添加标记
        </Tag>
      ) : (
        <Select
          mode="tags"
          style={{ width: 150 }}
          placeholder="输入标记"
          autoFocus
          open
          onChange={(value) => {
            if (value && value.length > 0) {
              onAdd(value[value.length - 1]);
              setIsAdding(false);
            }
          }}
          onBlur={() => setIsAdding(false)}
          options={tagOptions}
        />
      )}
    </>
  );
};

export default AddTagButton;
