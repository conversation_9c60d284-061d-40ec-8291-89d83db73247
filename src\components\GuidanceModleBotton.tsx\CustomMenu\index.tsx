import React from 'react';
import styles from './index.less';

interface CustomMenuProps {
  activeKey: string;
  openKeys: string[];
  onClick: (e: { key: string }) => void;
  onOpenChange: (keys: string[]) => void;
  items: any[];
}

const CustomMenu: React.FC<CustomMenuProps> = ({
  activeKey,
  openKeys,
  onClick,
  onOpenChange,
  items,
}) => {
  console.log(activeKey);

  const handleItemClick = (key: string) => {
    onClick({ key });
    const isSubItem = items.some((item) =>
      item.children?.some((child: { key: string }) => child.key === key),
    );
    if (isSubItem) {
      const currentOpenKey = items.find((item) =>
        item.children?.some((child: { key: string }) => child.key === key),
      )?.key;
      onOpenChange(currentOpenKey ? [currentOpenKey] : []);
    }
  };

  const toggleSubMenu = (key: string) => {
    onOpenChange(
      openKeys.includes(key)
        ? openKeys.filter((k) => k !== key)
        : [...openKeys, key],
    );
  };

  return (
    <div className={styles.customMenu}>
      {items.map((item) => (
        <div key={item.key}>
          <div
            className={`${styles.menuItem} ${
              activeKey === item.key ? styles.selected : ''
            }`}
            onClick={() =>
              item.children
                ? toggleSubMenu(item.key)
                : handleItemClick(item.key)
            }
          >
            <span>{item.label}</span>
            {item.children && (
              <span
                className={`${styles.arrowIcon} ${
                  openKeys.includes(item.key) ? styles.open : ''
                }`}
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 10l5 5 5-5"
                    stroke={activeKey === item.key ? '#1890ff' : '#8c8c8c'}
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </span>
            )}
          </div>

          {item.children && openKeys.includes(item.key) && (
            <div className={styles.subMenu}>
              {item.children.map((child: any) => (
                <div
                  key={child.key}
                  className={`${styles.subMenuItem} ${
                    activeKey === child.key ? styles.selected : ''
                  }`}
                  onClick={() => handleItemClick(child.key)}
                >
                  {child.label}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default CustomMenu;
