import ConditionalRender from '@/components/ConditionalRender';
import TitleBar from '@/components/TitleBar';
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Flex, Input, InputRef, Modal, message } from 'antd';
import { nanoid } from 'nanoid';
import { useEffect, useRef, useState } from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import styles from '../common.less';
import TypeGrouped from './TypeGrouped';

const PaperStructure = ({
  strucData,
  setStrucData,
  setQuestionData,
  chooseQuestion,
  showClear = true,
  selectTopic = true,
}: {
  strucData: any;
  setStrucData: React.Dispatch<any>;
  setQuestionData: React.Dispatch<React.SetStateAction<API.SystemQuestion[]>>;
  chooseQuestion: () => void;
  isDrag?: boolean;
  showClear?: boolean;
  selectTopic?: boolean;
}) => {
  const inputRef = useRef<InputRef>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isHidden, setIsHidden] = useState(false);
  const [modalData, setModalData] = useState<any>();
  /**
   * 处理确认按钮点击事件
   */
  const handleOk = () => {
    const val = inputRef?.current?.input?.value;
    if (val?.trim() === '' || !val) {
      message.warning('请输入题型名称');
    } else {
      setStrucData((prevData: any) => {
        const { bigQuestionOrder, bigQuestions } = prevData;
        if (modalData) {
          const curNode = bigQuestions[modalData.id];
          bigQuestions[modalData.id] = {
            ...curNode,
            name: val,
          };
        } else {
          const id = nanoid();
          bigQuestionOrder.push(id);
          bigQuestions[id] = {
            id,
            name: val,
            questionIds: [],
          };
        }
        return {
          bigQuestionOrder,
          bigQuestions,
        };
      });
      setIsModalOpen(false);
      setModalData(undefined);
    }
  };
  /**
   * 拖拽结束时的处理函数
   *
   * @param result 拖拽的结果，包含拖拽目标、拖拽源、拖拽类型等信息
   */
  const onDragEnd = (result: any) => {
    const { destination, draggableId, source, type } = result;
    if (!destination) {
      return;
    }
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    // 检查是否涉及"暂无分组"的拖拽交互
    if (type === 'big-question' && destination?.index === 0) {
      return; // 禁止与"暂无分组"交互
    }
    // 移动大题
    if (type === 'big-question') {
      const newBigQuestionOrder = Array.from(strucData.bigQuestionOrder);
      newBigQuestionOrder.splice(source.index, 1);
      newBigQuestionOrder.splice(destination.index, 0, draggableId);
      setStrucData((prevData: any) => ({
        ...prevData,
        bigQuestionOrder: newBigQuestionOrder,
      }));
      return;
    }
    // 移动小题
    const start = strucData.bigQuestions[source.droppableId];
    const finish = strucData.bigQuestions[destination.droppableId];
    // 同一题移动小题
    if (start === finish) {
      const newQuestionIds = Array.from(start.questionIds);
      // 移除当前拖拽的元素，并将其插入到目标位置
      newQuestionIds.splice(source.index, 1);
      newQuestionIds.splice(destination.index, 0, draggableId);
      const newBigQuestion = {
        ...start,
        questionIds: newQuestionIds,
      };
      setStrucData((prevData: any) => ({
        ...prevData,
        bigQuestions: {
          ...prevData.bigQuestions,
          [newBigQuestion.id]: newBigQuestion,
        },
      }));
      return;
    }
    // 跨大题移动小题
    const startQuestionIds = Array.from(start.questionIds);
    startQuestionIds.splice(source.index, 1);
    const finishQuestionIds = Array.from(finish.questionIds);
    finishQuestionIds.splice(destination.index, 0, draggableId);
    const newStart = {
      ...start,
      questionIds: startQuestionIds,
    };
    const newFinish = {
      ...finish,
      questionIds: finishQuestionIds,
    };
    setStrucData((prevData: any) => ({
      ...prevData,
      bigQuestions: {
        ...prevData.bigQuestions,
        [newStart.id]: newStart,
        [newFinish.id]: newFinish,
      },
    }));
  };
  /**
   * 清除所有题型
   */
  const clearAll = () => {
    const hideId = 'hidden_' + nanoid();
    const newData: any = {
      bigQuestionOrder: [hideId],
      bigQuestions: {},
    };
    newData.bigQuestions[hideId] = {
      id: hideId,
      name: '暂无分组',
      questionIds: [],
    };
    setStrucData(newData);
    setModalData(undefined);
    setQuestionData([]);
  };
  const handleEvent = (type: string, data: any) => {
    if (type === 'edit') {
      setModalData(data);
      setTimeout(() => {
        setIsModalOpen(true);
      }, 100);
    }
    if (type === 'delete') {
      setStrucData((prevData: any) => {
        const newBigQuestionOrder = prevData.bigQuestionOrder.filter(
          (id: string) => id !== data.id,
        );
        const newBigQuestions = { ...prevData.bigQuestions };
        delete newBigQuestions[data.id];
        return {
          ...prevData,
          bigQuestionOrder: newBigQuestionOrder,
          bigQuestions: newBigQuestions,
        };
      });
    }
  };
  useEffect(() => {
    if (strucData?.bigQuestionOrder?.length) {
      const hasHidden = strucData.bigQuestionOrder.some((item: string) => {
        return item.indexOf('hidden') !== -1;
      });
      setIsHidden(hasHidden);
    }
  }, [strucData]);

  return (
    <div>
      <TitleBar
        title="试卷结构"
        style={{
          padding: '0 0 14px',
          borderRadius: 0,
          borderBottom: '1px solid #dadada',
        }}
        actionDom={
          showClear ? (
            <Button type="link" onClick={clearAll}>
              全部清空
            </Button>
          ) : null
        }
      />
      <div className={styles.construction}>
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="all-big-questions" type="big-question">
            {(provided) => {
              return (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  {strucData?.bigQuestionOrder?.map(
                    (bigQuestionId: any, index: number) => {
                      // 确保 bigQuestionId 是有效的键
                      const bigQuestion = strucData.bigQuestions[bigQuestionId];
                      if (!bigQuestion || bigQuestion.length === 0) return null;
                      return (
                        <Draggable
                          key={bigQuestionId + isHidden}
                          draggableId={bigQuestionId}
                          index={index}
                          isDragDisabled={bigQuestion.name === '暂无分组'} // 添加条件判断
                        >
                          {(provided) => {
                            return (
                              <div
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                ref={provided.innerRef}
                              >
                                <TypeGrouped
                                  index={isHidden ? index - 1 : index}
                                  bigQuestion={bigQuestion}
                                  handleEvent={handleEvent}
                                />
                              </div>
                            );
                          }}
                        </Draggable>
                      );
                    },
                  )}
                  {provided.placeholder}
                </div>
              );
            }}
          </Droppable>
        </DragDropContext>
      </div>
      <Flex
        justify={'space-between'}
        align={'center'}
        style={{
          marginTop: '20px',
        }}
      >
        <Button
          type="primary"
          ghost
          icon={<PlusOutlined />}
          onClick={() => {
            setIsModalOpen(true);
          }}
        >
          自定义题型
        </Button>
        <ConditionalRender
          hasAccess={selectTopic}
          accessComponent={
            <Button type="primary" onClick={chooseQuestion}>
              继续选题
            </Button>
          }
        />
      </Flex>
      <Modal
        title={modalData?.id ? '编辑题型' : '添加新题型'}
        open={isModalOpen}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        onOk={handleOk}
        onCancel={() => {
          setIsModalOpen(false);
        }}
      >
        <Flex justify="space-around" align={'center'}>
          <div
            style={{
              fontWeight: 'bold',
            }}
          >
            题型名称：
          </div>
          <Input
            placeholder="请输入题型名称"
            ref={inputRef}
            defaultValue={modalData?.name || ''}
            style={{
              width: '360px',
            }}
          />
        </Flex>
        <p
          style={{
            lineHeight: '40px',
            color: '#999',
            marginTop: '10px',
          }}
        >
          <QuestionCircleOutlined
            style={{
              marginInlineEnd: '8px',
            }}
          />
          在右侧试卷结构中，拖动题号可将试题移入此题型下
        </p>
      </Modal>
    </div>
  );
};

export default PaperStructure;
