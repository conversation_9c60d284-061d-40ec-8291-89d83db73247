/*
 * @Description: 行政区域选择组件，支持在ProForm中使用
 * @Date: 2025-03-14 08:56:39
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-14 11:54:54
 */
import { ProForm, ProFormItemProps } from '@ant-design/pro-components';
import React from 'react';
import Area, { AreaSelectProps } from '../CommonSelect/Area';
import ColWrapper from '../Wrapper/ColWrapper';

const ProFormArea: React.FC<ProFormItemProps<AreaSelectProps>> = (prop) => {
  const { colProps, fieldProps, ...others } = prop;

  return (
    <ColWrapper colProps={colProps}>
      <ProForm.Item {...others}>
        <Area {...fieldProps} />
      </ProForm.Item>
    </ColWrapper>
  );
};

export default ProFormArea;
