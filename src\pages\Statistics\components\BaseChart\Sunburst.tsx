import { Sunburst } from '@ant-design/plots';
import React from 'react';
import NoData<PERSON>hart from './NoDataChart';

interface SunburstChartProps {
  data?: any;
  loading?: boolean;
  type: 'city' | 'county' | 'school';
}

const SunburstChart: React.FC<SunburstChartProps> = ({
  data,
  loading,
  type,
}) => {
  const convertToSunburstData = (data: any[]) => {
    if (!Array.isArray(data) || data.length === 0) return null;

    // 第一步：按年级段分组
    const groupedByGrade: Record<string, any> = {};

    data.forEach((item) => {
      const gradeName = item.grade_section_name;
      const subjectName = item.subject;
      const difficultyName = item.difficulty_name;
      const value = Number(item.total_difficulty_number);

      if (!groupedByGrade[gradeName]) {
        groupedByGrade[gradeName] = {};
      }

      // 第二步：在年级段下按学科分组
      if (!groupedByGrade[gradeName][subjectName]) {
        groupedByGrade[gradeName][subjectName] = [];
      }

      // 第三步：在学科下添加难度数据
      groupedByGrade[gradeName][subjectName].push({
        name: difficultyName,
        value: value,
      });
    });

    // 构建层级结构
    const children = Object.entries(groupedByGrade).map(
      ([gradeName, subjects]) => {
        const subjectChildren = Object.entries(subjects).map(
          ([subjectName, difficulties]) => {
            const total = (difficulties as any[]).reduce(
              (sum, d) => sum + d.value,
              0,
            );
            return {
              name: subjectName,
              value: total,
              children: difficulties,
            };
          },
        );

        const gradeTotal = subjectChildren.reduce(
          (sum, sub) => sum + sub.value,
          0,
        );
        return {
          name: gradeName,
          value: gradeTotal,
          children: subjectChildren,
        };
      },
    );

    // 返回最终结构
    return {
      name: '题目难度分布',
      children,
    };
  };

  const convertToSunburstSchoolData = (data: any[]) => {
    if (!Array.isArray(data) || data.length === 0) return null;

    const groupedByGrade: Record<string, any> = {};

    // 第一步：按年级分组
    data.forEach(
      (item: { grade_name: any; subject: any; total_question_number: any }) => {
        const gradeName = item.grade_name;
        const subject = item.subject;
        const value = Number(item.total_question_number);

        if (!groupedByGrade[gradeName]) {
          groupedByGrade[gradeName] = [];
        }

        groupedByGrade[gradeName].push({
          name: subject,
          value: value,
        });
      },
    );

    // 第二步：构建层级结构
    const children = Object.entries(groupedByGrade).map(
      ([gradeName, subjects]) => {
        const total = subjects.reduce(
          (sum: string, sub: { value: string }) => sum + sub.value,
          0,
        );
        return {
          name: gradeName,
          value: total,
          children: subjects,
        };
      },
    );

    // 返回最终结构
    return {
      name: '题目分布',
      children: children,
    };
  };

  const config = {
    height: 400,
    data: {
      value:
        type === 'school'
          ? convertToSunburstSchoolData(data)
          : convertToSunburstData(data),
    },
    label: {
      text: 'name',
      transform: [{ type: 'overflowHide' }],
    },
    animate: {
      enter: { type: 'waveIn' },
    },
    innerRadius: 0,
  };

  return (
    <NoDataChart data={data} loading={loading}>
      <Sunburst {...config} />
    </NoDataChart>
  );
};

export default SunburstChart;
