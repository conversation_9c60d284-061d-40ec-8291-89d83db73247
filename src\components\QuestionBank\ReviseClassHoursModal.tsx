import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import React from 'react';

interface ReviseClassHoursProps {
  open: boolean;
  title: string;
  onCancel: () => void;
  onOk: () => Promise<boolean>;
}

const ReviseClassHoursModal: React.FC<ReviseClassHoursProps> = ({
  open,
  title,
  onCancel,
  onOk,
}) => {
  return (
    <ModalForm
      layout="horizontal"
      width={400}
      title={title}
      open={open}
      onFinish={onOk}
      modalProps={{
        onCancel,
      }}
      style={{
        marginTop: '16px',
      }}
    >
      <ProFormSelect
        label="章节课时"
        placeholder="请选择"
        rules={[{ required: true, message: '请选择章节课时' }]}
      />
    </ModalForm>
  );
};
export default ReviseClassHoursModal;
