// 引入外部样式
@import '../../common/topicCardContent.less';

.searchTopicBox {
  background-color: var(--card-bg);
  padding: 16px 16px 0;
  border-radius: var(--border-radius-base);

  .other {
    margin-top: 24px;
  }

  .questionTypes {
    margin-bottom: 0;
  }

  .segmentedContainer {
    display: inline-flex;
    flex-wrap: wrap;
    padding: 2px;
    border-radius: 6px;
    transition: all 0.3s ease;
  }

  .segmentedItem {
    position: relative;
    min-height: 28px;
    line-height: 28px;
    padding: 0 11px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    border-radius: 4px;
    color: rgba(0, 0, 0, 65%);
    backface-visibility: hidden;

    &:hover:not(.segmentedItemActive) {
      color: #000;
      background-color: #e0e0e0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
    }
  }

  .segmentedItemActive {
    color: #fff;
    background-color: var(--primary-color);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.topicListBox {
  position: relative;
  min-height: calc(100vh - 400px);

  .topicList {
    .topicCard {
      cursor: pointer;
      margin-bottom: 16px;

      .topicCardHeader,
      .topicCardFooter {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: var(--sub-color);
      }

      .topicCardHeader {
        margin-bottom: 6px;
        gap: 8px;
        justify-content: space-between;

        .title {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .share {
          font-size: 12px;
        }
      }

      .topicCardFooter {
        display: flex;
        justify-content: space-between;

        .leftpart {
          display: flex;
          align-items: center;

          .update + .update {
            margin-left: 10px;
            color: var(--primary-color);
            display: flex;
            align-items: center;

            :global {
              .ant-select {
                margin-left: 5px;

                .ant-select-selector {
                  background: transparent !important;
                  padding: 0 !important;
                }

                .ant-select-selection-overflow {
                  flex-wrap: nowrap;
                  max-width: 200px;
                  overflow: hidden;
                }
              }
            }
          }
        }

        .icon {
          margin-right: 5px;
        }

        .options {
          display: flex;
          gap: 16px;
          cursor: pointer;

          .edit {
            color: var(--primary-color);
          }

          .delete {
            color: var(--delete-color);
          }
        }
      }
    }
  }
}

.lookQuestionModal {
  .topicContent {
    margin: 16px 0 0;
    max-height: 400px;
    overflow: hidden;
    overflow-y: auto;
  }

  .answerTitle {
    margin-bottom: 5px;
  }

  .analysis {
    max-height: 200px;
    overflow: hidden;
    overflow-y: auto;
    margin-top: 5px;
  }
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 80%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.zoomedImage {
  position: absolute;
  transition: all 0.3s ease;
  cursor: zoom-out;
  transform-origin: center center; /* 确保缩放以图片中心为基准 */
}
