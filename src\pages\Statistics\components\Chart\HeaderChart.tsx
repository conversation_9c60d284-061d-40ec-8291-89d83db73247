import { frequency, schoolTotal, time, works } from '@/assets';
import {
  getQuestionWorkStatistic,
  getSchoolAnalysis,
} from '@/services/statistics';
import { useModel } from '@umijs/max';
import { Card, Col, message, Row, Statistic } from 'antd';
import React, { useEffect, useState } from 'react';
import SchoolTrend from '../BaseChart/SchoolTrend';
import CardTitle from '../CardTitle';
import styles from './styles/index.less';

interface HeaderChartProps {
  type?: 'city' | 'county' | 'school';
  searchCondition?: any;
}

/** 去除负数 */
export const getPositiveNumber = (value: any) => {
  if (typeof value === 'string') {
    return value.includes('-') ? value.replace('-', '') : value;
  }
  return value;
};

const HeaderChart: React.FC<HeaderChartProps> = ({ type, searchCondition }) => {
  const { initialState } = useModel('@@initialState');
  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const getInitData = async () => {
    setLoading(true);
    const { yearTerm, county, schoolType } = searchCondition || {};

    const isSchoolType = type === 'school';
    const params = {
      city_code: type === 'city' ? initialState?.enterprise?.city : undefined,
      area_code:
        type === 'county'
          ? county || initialState?.enterprise?.area
          : undefined,
      enterprise_code: isSchoolType
        ? schoolType || initialState?.enterprise?.code
        : undefined,
      semester_code: yearTerm,
    };

    try {
      const res = await (isSchoolType
        ? getSchoolAnalysis
        : getQuestionWorkStatistic)(params);
      if (res?.errCode) {
        message.warning('获取数据失败，请稍后重试');
        return;
      }
      const data = res?.data || {};
      setData(data);
    } catch (error) {
      console.error('获取数据异常:', error);
      message.warning('获取数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (type) {
      getInitData();
    }
  }, [type, searchCondition]);

  const getTrendType = (value: any): 'up' | 'down' => {
    if (typeof value === 'string') {
      return value.includes('-') ? 'down' : 'up';
    }
    return 'up';
  };

  const getSchoolTrendData = (data: any) => {
    return Object.entries(data ?? {}).map(([key, value]) => {
      return {
        type: getTrendType(value),
        value: `${value || 0}`,
        title: key,
      };
    });
  };

  return (
    <>
      <Col span={6}>
        <Card loading={loading}>
          <Row>
            <Col span={18}>
              <Statistic
                title={
                  type === 'city'
                    ? '全市学校总数'
                    : type === 'county'
                    ? '全区学校总数'
                    : '全校教师总数'
                }
                value={
                  type === 'school'
                    ? data?.teacher_number || 0
                    : data?.school_count || 0
                }
                precision={0}
                suffix={type === 'school' ? '人' : '所'}
              />
            </Col>
            <Col span={6} className={styles.icon}>
              <img src={schoolTotal} alt="" />
            </Col>
          </Row>
          <SchoolTrend
            hidden={type === 'school' || data?.school_count === 0}
            data={[
              {
                value: `${data?.school_count_primary || 0} 所`,
                title: '小学',
              },
              {
                value: `${data?.school_count_middle || 0} 所`,
                title: '初中',
              },
              {
                value: `${data?.school_count_high || 0} 所`,
                title: '高中',
              },
            ]}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card loading={loading}>
          <Row>
            <Col span={19}>
              <Statistic
                title={
                  <CardTitle
                    title={
                      type === 'city'
                        ? '全市作业次数'
                        : type === 'county'
                        ? '全区作业次数'
                        : '全校作业次数'
                    }
                    type={getTrendType(data?.homework_number_last_month)}
                    value={
                      getPositiveNumber(data?.homework_number_last_month) || 0
                    }
                  />
                }
                value={getPositiveNumber(data?.total_homework_number)}
                precision={0}
                suffix="次"
              />
            </Col>
            <Col span={5} className={styles.icon}>
              <img src={frequency} alt="" />
            </Col>
          </Row>
          <SchoolTrend
            hidden={type === 'school' || data?.school_count === 0}
            data={getSchoolTrendData(data?.growthObj?.homework_growth_rate)}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card loading={loading}>
          <Row>
            <Col span={19}>
              <Statistic
                title={
                  <CardTitle
                    title="平均作业时长"
                    type={getTrendType(data?.average_time_last_month)}
                    value={getPositiveNumber(data?.average_time_last_month)}
                  />
                }
                value={getPositiveNumber(data?.total_average_time)}
                precision={2}
                suffix="分钟"
              />
            </Col>
            <Col span={5} className={styles.icon}>
              <img src={time} alt="" />
            </Col>
          </Row>
          <SchoolTrend
            hidden={type === 'school' || data?.school_count === 0}
            data={getSchoolTrendData(data?.growthObj?.average_time_growth_rate)}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card loading={loading}>
          <Row>
            <Col span={19}>
              <Statistic
                title={
                  <CardTitle
                    title={
                      type === 'city'
                        ? '全市试题总量'
                        : type === 'county'
                        ? '全区试题总量'
                        : '全校试题总量'
                    }
                    type={getTrendType(data?.question_number_last_month)}
                    value={getPositiveNumber(data?.question_number_last_month)}
                  />
                }
                value={data?.total_question_number}
                precision={0}
                suffix="道"
              />
            </Col>
            <Col span={5} className={styles.icon}>
              <img src={works} alt="" />
            </Col>
          </Row>
          <SchoolTrend
            hidden={type === 'school' || data?.school_count === 0}
            data={getSchoolTrendData(data?.growthObj?.question_growth_rate)}
          />
        </Card>
      </Col>
    </>
  );
};
export default HeaderChart;
