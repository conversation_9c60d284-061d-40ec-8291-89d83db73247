import { Button, Card, Empty, message, TreeProps } from 'antd';
import classNames from 'classnames';
import { Key, useCallback, useEffect, useState } from 'react';
import CreateModal from './CreateModal';
import EditModal from './EditModal';
import InsertModal from './InsertModal';
import {
  batchGeneration,
  getTreeData,
  handleSave,
  insertCatalog,
} from './methods';
import { TreeStyled } from './Styled';
import TitleRender from './TitleRender';
import { CatalogTreeNode } from './typings';

const CatalogTree: React.FC<{
  /** 只读模式 */
  readonly?: true;
  /** 可多选 */
  checkable?: true;
  /** 最大层级 */
  maxLevel?: number;
  /** 复制后的教材名录id */
  designId?: any;
  /** 不同类型调整接口 */
  proxyType?: string;
  /** 当前册次 */
  currentTextbookChecklist?: Partial<API.TextbookChecklist>;
  /** 默认选中的节点 */
  defaultSelectedNode?: CatalogTreeNode;
  /** 点击目录回调 */
  onSelect?: (node: CatalogTreeNode) => void;
  /** 点击目录多选框回调 */
  onMultipleSelect?: (nodes: CatalogTreeNode[], keys?: any) => void;
  /** 卡片自定义类名 */
  cardClassName?: string;
  /** 卡片标题 */
  cardTitle?: string;
}> = ({
  readonly,
  checkable,
  maxLevel,
  proxyType,
  designId,
  currentTextbookChecklist,
  defaultSelectedNode,
  onSelect,
  onMultipleSelect,
  cardClassName,
  cardTitle,
}) => {
  // 树形结构
  const [treeData, setTreeData] = useState<TreeProps['treeData']>([]);
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);

  // 编辑弹窗
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.TextbookCatalog>();
  const [createModalVisible, setCreateModalVisible] = useState(false);

  // 添加同级目录弹窗
  const [insertModalVisible, setInsertModalVisible] = useState(false);
  // 相邻目录
  const [adjacentCatalog, setAdjacentCatalog] = useState<{
    node: CatalogTreeNode;
    action: 'insert' | 'append';
  }>();

  const refresh = useCallback(async () => {
    if (currentTextbookChecklist) {
      const data = await getTreeData(
        currentTextbookChecklist.id,
        proxyType,
        designId,
        maxLevel,
      );
      setTreeData(data);
      if (defaultSelectedNode) {
        const pid = defaultSelectedNode.parent_id;
        if (pid) {
          setExpandedKeys([pid]);
          onSelect?.(defaultSelectedNode);
        }
      }
    } else {
      setTreeData([]);
      onSelect?.({} as CatalogTreeNode);
      onMultipleSelect?.([]);
    }
  }, [currentTextbookChecklist, proxyType, defaultSelectedNode]);

  useEffect(() => {
    refresh();
  }, [currentTextbookChecklist, proxyType, defaultSelectedNode]);

  useEffect(() => {
    if (current) {
      onSelect?.(current as CatalogTreeNode);
    }
  }, [current]);

  return (
    <Card className={classNames(cardClassName)} title={cardTitle}>
      <TreeStyled<CatalogTreeNode>
        key="id"
        treeData={treeData as CatalogTreeNode[]}
        defaultSelectedKeys={
          defaultSelectedNode ? [defaultSelectedNode.id!] : []
        }
        autoExpandParent
        expandedKeys={expandedKeys}
        onExpand={(keys) => {
          // console.log('onExpand: ', keys);
          setExpandedKeys(keys);
        }}
        showLine
        checkable={checkable}
        titleRender={(node: any) => {
          if (readonly) {
            return node.title;
          }
          return (
            <TitleRender
              proxyType={proxyType}
              node={node}
              actived={node.id === current?.id}
              maxLevel={maxLevel}
              addHandler={(
                node: CatalogTreeNode,
                action: 'insert' | 'append',
              ) => {
                setAdjacentCatalog({
                  node,
                  action,
                });
                setInsertModalVisible(true);
              }}
              editHandler={(node: CatalogTreeNode) => {
                setCurrent(node as unknown as API.TextbookCatalog);
                setModalVisible(true);
              }}
              batchGenerationHandler={(node: CatalogTreeNode) => {
                setCurrent(node as unknown as API.TextbookCatalog);
                setCreateModalVisible(true);
              }}
              onChange={refresh}
            />
          );
        }}
        onSelect={(_selectedKeys: any, info: any) => {
          if (info.selectedNodes?.length) {
            const currentNode = info.selectedNodes[0];
            if (currentNode?.id === current?.id) {
              return;
            }
            const parent_name = treeData?.find(
              (item) => item.key === info.selectedNodes[0].parent_id,
            )?.title;
            setCurrent({
              ...info.selectedNodes[0],
              parent_name,
            } as unknown as API.TextbookCatalog);
          } else {
            setCurrent(undefined);
            onSelect?.({} as CatalogTreeNode);
            onMultipleSelect?.([]);
          }
        }}
        onCheck={(keys, e) => {
          onMultipleSelect?.(e.checkedNodes, keys);
        }}
      />
      {!treeData?.length && (
        <Empty
          description={!!currentTextbookChecklist ? undefined : '请选择教材'}
        >
          {!!currentTextbookChecklist && !readonly && (
            <Button
              type="primary"
              onClick={() => {
                setCurrent(undefined);
                setCreateModalVisible(true);
              }}
            >
              创建目录
            </Button>
          )}
        </Empty>
      )}
      <CreateModal
        parent={current}
        open={createModalVisible}
        okHandler={async (list) => {
          batchGeneration(
            {
              textbookChecklist_id: currentTextbookChecklist?.id,
              parent_id: current?.id,
              list,
              proxyType,
              designId,
            },
            () => {
              refresh();
              setCreateModalVisible(false);
            },
          );
        }}
        cancelHandler={() => {
          setCreateModalVisible(false);
        }}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={async (info) => {
          handleSave(
            info,
            () => {
              refresh();
              setModalVisible(false);
            },
            proxyType,
            designId,
          );
        }}
      />
      <InsertModal
        open={insertModalVisible}
        catalog={adjacentCatalog?.node}
        action={adjacentCatalog?.action}
        onClose={() => setInsertModalVisible(false)}
        onSave={async (info) => {
          if (!adjacentCatalog) {
            message.warning('操作失败，请重试。');
            return;
          }
          const { errCode, msg } = await insertCatalog(
            adjacentCatalog.node.id!,
            {
              title: info.title,
              old_id: adjacentCatalog.node.parent_id,
              action: adjacentCatalog.action,
            },
            proxyType,
          );
          if (errCode) {
            message.warning(msg || '操作失败，请重试。');
            return;
          }
          refresh();
          setInsertModalVisible(false);
        }}
      />
    </Card>
  );
};

export default CatalogTree;
