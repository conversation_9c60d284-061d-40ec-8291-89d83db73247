/*
 * @Description: 题库录入管理
 * @Date: 2025-04-21 17:10:58
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-22 09:19:42
 */
import { useCurrentAccess } from '@/common/useCurrentAccess';
import { Tabs } from 'antd';
import { useState } from 'react';
import styles from './index.less';
import PersonalQuestionBank from './PersonalQuestionBank';
import SchoolQuestionBank from './SchoolQuestionBank';
import SystemQuestionBank from './SystemQuestionBank';

export enum MONGO_MODEL_KEY {
  SYSTEM = 'system', // 系统题库
  PERSONAL = 'personal', // 个人题库
  SCHOOL = 'school', // 学校题库
}

const QuestionBank = () => {
  const { isAdmin, isSchoolAdmin, isSysAdmin } = useCurrentAccess();

  const getDefaultTab = () => {
    if (isSysAdmin) return '1';
    if (isSchoolAdmin) return '2';
    return '3'; // 默认显示个人题库
  };

  const [activeKey, setActiveKey] = useState(getDefaultTab());

  const onChange = (key: string) => {
    setActiveKey(key);
  };

  const getTabItems = () => {
    const baseTabs = [
      {
        key: '1',
        label: '系统题库',
        children: <SystemQuestionBank readonly={!isSysAdmin} />,
      },
      {
        key: '2',
        label: '学校题库',
        children: <SchoolQuestionBank readonly={!isSchoolAdmin} />,
      },
      {
        key: '3',
        label: '个人题库',
        children: <PersonalQuestionBank />,
      },
    ];

    if (isSysAdmin) {
      return [baseTabs[0]]; // 系统管理员只能查看系统题库
    }

    if (isSchoolAdmin) {
      return [baseTabs[0], baseTabs[1]]; // 学校管理员可查看系统和学校题库
    }
    if (isAdmin) {
      return baseTabs; // 普通用户可查看所有题库
    }
  };

  return (
    <div className={styles.container}>
      <Tabs
        className={styles.tabs}
        activeKey={activeKey}
        onChange={onChange}
        destroyInactiveTabPane
        items={getTabItems()}
      />
    </div>
  );
};

export default QuestionBank;
